<?php

/**
 * 测试导入计数逻辑修复
 */

require_once __DIR__ . '/vendor/autoload.php';

use Illuminate\Foundation\Application;
use App\Models\Attachment;
use App\Models\AssetImportTask;
use App\Models\Asset;
use App\Models\Entity;
use App\Services\AssetImportService;

// 启动Laravel应用
$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "=== 测试导入计数逻辑修复 ===\n\n";

try {
    // 1. 记录导入前的数据量
    echo "1. 记录导入前的数据量...\n";
    
    $assetCountBefore = Asset::count();
    $entityCountBefore = Entity::count();
    
    echo "导入前资产数量: {$assetCountBefore}\n";
    echo "导入前主体数量: {$entityCountBefore}\n";
    
    // 2. 执行小批量导入测试
    echo "\n2. 执行小批量导入测试...\n";
    
    $filePath = 'storage/app/public/大数据资产导入模板_2025-08-14_06-26-47.xlsx';
    $fullPath = __DIR__ . '/' . $filePath;
    
    if (!file_exists($fullPath)) {
        echo "测试文件不存在: {$fullPath}\n";
        exit(1);
    }
    
    // 创建附件记录
    $fileName = basename($filePath);
    $attachment = Attachment::where('file_name', $fileName)->first();
    
    if (!$attachment) {
        $attachment = Attachment::create([
            'file_name' => $fileName,
            'file_path' => str_replace('storage/app/public/', '', $filePath),
            'file_size' => filesize($fullPath),
            'mime_type' => 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            'storage_type' => 'local',
            'md5_hash' => md5_file($fullPath),
        ]);
    }
    
    // 创建导入任务
    $importTask = AssetImportTask::create([
        'file_path' => $attachment->file_path,
        'original_filename' => $attachment->file_name,
        'status' => 'pending',
        'created_by' => 1,
    ]);
    
    echo "导入任务创建成功，ID: {$importTask->id}\n";
    
    // 执行导入（只处理少量数据）
    $service = new AssetImportService();
    $reflection = new ReflectionClass($service);
    
    // 设置小批次大小
    $batchSizeProperty = $reflection->getProperty('batchSize');
    $batchSizeProperty->setAccessible(true);
    $batchSizeProperty->setValue($service, 10); // 只处理10行数据
    
    $importTask->markAsProcessing();
    
    $startTime = microtime(true);
    $result = $service->processImport($importTask);
    $endTime = microtime(true);
    
    echo "\n3. 导入结果分析...\n";
    echo "耗时: " . number_format($endTime - $startTime, 2) . " 秒\n";
    echo "总行数: " . $result['total_rows'] . "\n";
    echo "成功行数: " . $result['success_rows'] . "\n";
    echo "失败行数: " . $result['failed_rows'] . "\n";
    
    // 验证计数逻辑
    $totalExpected = $result['total_rows'];
    $successActual = $result['success_rows'];
    $failedActual = $result['failed_rows'];
    $sumActual = $successActual + $failedActual;
    
    echo "\n4. 计数逻辑验证...\n";
    echo "预期总数: {$totalExpected}\n";
    echo "实际成功: {$successActual}\n";
    echo "实际失败: {$failedActual}\n";
    echo "实际总和: {$sumActual}\n";
    
    if ($sumActual == $totalExpected) {
        echo "✅ 计数逻辑正确: 成功数 + 失败数 = 总数\n";
    } else {
        echo "❌ 计数逻辑错误: 成功数 + 失败数 ≠ 总数\n";
        echo "差异: " . ($totalExpected - $sumActual) . "\n";
    }
    
    if ($successActual >= 0 && $failedActual >= 0) {
        echo "✅ 计数值正常: 没有负数\n";
    } else {
        echo "❌ 计数值异常: 存在负数\n";
    }
    
    // 5. 验证实际数据库变化
    echo "\n5. 验证实际数据库变化...\n";
    
    $assetCountAfter = Asset::count();
    $entityCountAfter = Entity::count();
    
    $assetIncrement = $assetCountAfter - $assetCountBefore;
    $entityIncrement = $entityCountAfter - $entityCountBefore;
    
    echo "导入后资产数量: {$assetCountAfter} (增加 {$assetIncrement})\n";
    echo "导入后主体数量: {$entityCountAfter} (增加 {$entityIncrement})\n";
    
    if ($assetIncrement == $successActual) {
        echo "✅ 资产数量匹配: 数据库增量 = 成功行数\n";
    } else {
        echo "⚠️ 资产数量不匹配: 数据库增量({$assetIncrement}) ≠ 成功行数({$successActual})\n";
    }
    
    // 6. 检查错误信息
    if (!empty($result['errors'])) {
        echo "\n6. 错误信息分析...\n";
        echo "错误数量: " . count($result['errors']) . "\n";
        
        if (count($result['errors']) == $failedActual) {
            echo "✅ 错误数量匹配: 错误记录数 = 失败行数\n";
        } else {
            echo "❌ 错误数量不匹配: 错误记录数(" . count($result['errors']) . ") ≠ 失败行数({$failedActual})\n";
        }
        
        echo "前5个错误:\n";
        foreach (array_slice($result['errors'], 0, 5) as $error) {
            echo "  行 {$error['row']}: {$error['error']}\n";
        }
    } else {
        echo "\n6. 没有错误信息\n";
        if ($failedActual == 0) {
            echo "✅ 错误信息匹配: 无错误记录，失败行数为0\n";
        } else {
            echo "❌ 错误信息不匹配: 无错误记录，但失败行数为{$failedActual}\n";
        }
    }
    
    $importTask->markAsCompleted($result);
    
    // 7. 总结
    echo "\n=== 测试总结 ===\n";
    
    $issues = [];
    
    if ($sumActual != $totalExpected) {
        $issues[] = "计数总和不匹配";
    }
    
    if ($successActual < 0 || $failedActual < 0) {
        $issues[] = "存在负数计数";
    }
    
    if ($assetIncrement != $successActual) {
        $issues[] = "数据库增量与成功数不匹配";
    }
    
    if (!empty($result['errors']) && count($result['errors']) != $failedActual) {
        $issues[] = "错误记录数与失败数不匹配";
    }
    
    if (empty($issues)) {
        echo "🎉 所有计数逻辑都正常工作！\n";
        echo "✅ 成功修复了导入计数问题\n";
    } else {
        echo "⚠️ 发现以下问题:\n";
        foreach ($issues as $issue) {
            echo "  - {$issue}\n";
        }
    }
    
} catch (Exception $e) {
    echo "✗ 测试失败：" . $e->getMessage() . "\n";
    echo "错误位置：" . $e->getFile() . ":" . $e->getLine() . "\n";
}

echo "\n=== 测试结束 ===\n";
