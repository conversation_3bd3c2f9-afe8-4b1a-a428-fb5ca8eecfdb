<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\LoginRequest;
use App\Http\Resources\Admin\UserResource;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\ValidationException;

/**
 * @group 授权
 *
 * 管理员认证相关接口
 */
class AuthController extends Controller
{
    /**
     * 登录
     *
     * 管理员登录接口，验证用户名和密码后返回访问令牌
     *
     * @unauthenticated
     *
     * @bodyParam account string required 登录账号 Example: ***********
     * @bodyParam password string required 密码 Example: password123
     */
    public function login(LoginRequest $request)
    {
        $admin = User::where('account', $request->account)->first();

        // 检查用户是否存在
        if (!$admin) {
            throw ValidationException::withMessages([
                'account' => ['账号或密码错误'],
            ]);
        }

        // 检查密码
        if (!Hash::check($request->password, $admin->password)) {
            throw ValidationException::withMessages([
                'account' => ['账号或密码错误'],
            ]);
        }

        // 检查用户状态
        if ($admin->status !== \App\Enums\AdminStatus::ENABLE->value) {
            throw ValidationException::withMessages([
                'account' => ['账号已被禁用'],
            ]);
        }

        // 加载用户的附件和角色关系
        $admin->load(['attachments', 'roles']);

        // 清理旧的令牌（可选，防止令牌累积）
        // $admin->tokens()->delete();

        // 创建新令牌
        $token = $admin->createToken('admin-token', ['*'], now()->addDays(7))->plainTextToken;

        // 记录登录日志
        \Log::info('用户登录成功', [
            'user_id' => $admin->id,
            'account' => $admin->account,
            'nickname'=> $admin->nickname,
            'ip' => $request->ip(),
        ]);

        return response()->json([
            'token' => $token,
            'user' => new UserResource($admin),
        ]);
    }

    /**
     * 退出登录
     *
     * 撤销当前访问令牌，退出登录状态
     */
    public function logout(Request $request)
    {
        $request->user()->currentAccessToken()->delete();

        return response()->noContent();
    }
}
