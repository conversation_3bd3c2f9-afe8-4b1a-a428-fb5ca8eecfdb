<?php

namespace App\Http\Resources\Admin;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use function App\Support\string_to_timestamp;

class CategoryResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'code' => $this->code,
            'parent_id' => $this->parent_id,
            'level' => $this->level,
            'sort' => $this->sort,
            'status' => $this->status,
            'remark' => $this->remark,
            'children_count' => $this->when(isset($this->children_count), $this->children_count, function () {
                return $this->getChildrenCountAttribute();
            }),
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
            // 不返回 children，让前端自己组装
        ];
    }
}
