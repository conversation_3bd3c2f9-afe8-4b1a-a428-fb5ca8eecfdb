<template>
  <ElDialog v-model="visible" :title="dialogTitle" width="500px" :close-on-click-modal="false">
    <ElForm ref="formRef" :model="formData" :rules="formRules" label-width="100px">
      <ElFormItem label="分类名称" prop="name">
        <ElInput v-model="formData.name" placeholder="请输入分类名称" />
      </ElFormItem>

      <ElFormItem label="分类代码" prop="code">
        <ElInput v-model="formData.code" placeholder="请输入分类代码" />
      </ElFormItem>

      <ElFormItem label="上级分类" prop="parent_id">
        <NullableSelect
          v-model="formData.parent_id"
          placeholder="请选择上级分类"
          :disabled="hasFixedParent"
          clearable
          filterable
        >
          <ElOption :value="0" label="顶级分类" />
          <ElOption
            v-for="item in categoryOptions"
            :key="item.id"
            :value="item.id"
            :label="item.displayName"
            :disabled="item.disabled"
          />
        </NullableSelect>
      </ElFormItem>

      <ElFormItem label="排序" prop="sort">
        <ElInputNumber v-model="formData.sort" :min="0" />
      </ElFormItem>

      <ElFormItem label="状态" prop="status">
        <ElRadioGroup v-model="formData.status">
          <ElRadio :value="1">启用</ElRadio>
          <ElRadio :value="0">禁用</ElRadio>
        </ElRadioGroup>
      </ElFormItem>

      <ElFormItem label="备注" prop="remark">
        <ElInput v-model="formData.remark" type="textarea" :rows="3" placeholder="请输入备注信息" />
      </ElFormItem>
    </ElForm>

    <template #footer>
      <span class="dialog-footer">
        <ElButton @click="handleCancel">取消</ElButton>
        <ElButton type="primary" :loading="loading" @click="handleSubmit">确定</ElButton>
      </span>
    </template>
  </ElDialog>
</template>

<script setup lang="ts">
  import { ElMessage } from 'element-plus'
  import type { FormInstance, FormRules } from 'element-plus'
  import { createCategory, updateCategory } from '@/api/admin/categoryApi'
  import type { CategoryForm } from '@/types/api'

  // Props
  interface Props {
    title: string
    isEdit: boolean
    categoryData?: CategoryForm
    categoryOptions: Array<{
      id: number
      displayName: string
      disabled: boolean
    }>
  }

  const props = withDefaults(defineProps<Props>(), {
    title: '',
    isEdit: false,
    categoryOptions: () => []
  })

  // Emits
  const emit = defineEmits<{
    success: []
  }>()

  // 使用 defineModel 简化 v-model
  const visible = defineModel<boolean>({ default: false })

  const formRef = ref<FormInstance>()
  const loading = ref(false)

  // 表单数据
  const formData = ref<CategoryForm>({
    id: undefined,
    name: '',
    code: '',
    parent_id: null,
    sort: 0,
    status: 1,
    remark: ''
  })

  // 表单验证规则
  const formRules: FormRules = {
    name: [
      { required: true, message: '请输入分类名称', trigger: 'blur' },
      { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
    ],
    code: [
      { required: true, message: '请输入分类代码', trigger: 'blur' },
      { pattern: /^[a-zA-Z0-9_]+$/, message: '代码只能包含字母、数字和下划线', trigger: 'blur' }
    ],
    sort: [{ required: true, message: '请输入排序', trigger: 'blur' }]
  }

  // 计算属性
  const dialogTitle = computed(() => props.title || (props.isEdit ? '编辑分类' : '新增分类'))

  // 判断是否有固定的父级（只有新增子分类时才固定）
  const hasFixedParent = computed(() => {
    // 如果是新增模式，且标题包含"子级分类"，说明是新增子分类
    return !props.isEdit && props.title.includes('子级分类')
  })

  // 监听数据变化
  watch(
    () => props.categoryData,
    (newData) => {
      if (newData) {
        formData.value = {
          id: newData.id,
          name: newData.name || '',
          code: newData.code || '',
          parent_id: newData.parent_id ?? null,
          sort: newData.sort ?? 0,
          status: newData.status ?? 1,
          remark: newData.remark || ''
        }
      }
    },
    { immediate: true }
  )

  // 监听对话框显示状态，重置表单
  watch(
    () => visible.value,
    (newVal) => {
      if (!newVal) {
        // 对话框关闭时重置表单
        nextTick(() => {
          formRef.value?.resetFields()
          if (!props.categoryData) {
            // 如果没有传入数据，重置为默认值
            formData.value = {
              id: undefined,
              name: '',
              code: '',
              parent_id: null,
              sort: 0,
              status: 1,
              remark: ''
            }
          }
        })
      }
    }
  )

  // 提交表单
  const handleSubmit = async () => {
    if (!formRef.value) return

    await formRef.value.validate(async (valid) => {
      if (!valid) return

      loading.value = true
      if (props.isEdit && formData.value.id) {
        await updateCategory(formData.value.id, formData.value)
        ElMessage.success('更新成功')
      } else {
        await createCategory(formData.value)
        ElMessage.success('创建成功')
      }

      visible.value = false
      emit('success')
      loading.value = false
    })
  }

  // 取消
  const handleCancel = () => {
    visible.value = false
  }
</script>

<style scoped lang="scss">
  .dialog-footer {
    display: flex;
    gap: 10px;
    justify-content: flex-end;
  }
</style>
