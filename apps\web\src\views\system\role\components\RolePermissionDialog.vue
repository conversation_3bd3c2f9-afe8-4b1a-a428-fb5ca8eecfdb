<template>
  <ElDialog
    v-model="dialogVisible"
    :title="`分配菜单权限 - ${roleData?.name || ''}`"
    width="800px"
    top="5vh"
    :before-close="handleClose"
    destroy-on-close
  >
    <div v-loading="loading" class="permission-container">
      <!-- 搜索框和操作按钮 -->
      <div class="toolbar">
        <ElInput
          v-model="filterText"
          placeholder="搜索菜单"
          clearable
          prefix-icon="Search"
          class="filter-input"
        />
        <ElButton @click="toggleExpandAll">
          <i :class="isExpanded ? 'el-icon-minus' : 'el-icon-plus'" />
          {{ isExpanded ? '全部收起' : '全部展开' }}
        </ElButton>
      </div>

      <!-- 菜单树 -->
      <ElScrollbar height="500px" class="tree-scrollbar">
        <ElTree
          ref="treeRef"
          v-loading="treeLoading"
          :data="menuTreeData"
          :props="treeProps"
          :filter-node-method="filterNode"
          node-key="id"
          show-checkbox
          :default-expanded-keys="defaultExpandedKeys"
          :check-strictly="false"
          :expand-on-click-node="false"
          @check="handleTreeCheck"
        >
          <template #default="{ data }">
            <span class="tree-node">
              <i v-if="data.meta?.icon" :class="data.meta.icon" class="menu-icon" />
              <span>{{ data.label }}</span>
              <ElTag v-if="data.isPermission" type="success" size="small" class="menu-tag">
                权限
              </ElTag>
              <ElTag
                v-else
                :type="getMenuTypeTagType(data.originalData || data) as any"
                size="small"
                class="menu-tag"
              >
                {{ getMenuType(data.originalData || data) }}
              </ElTag>
            </span>
          </template>
        </ElTree>
      </ElScrollbar>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <ElButton @click="handleClose">取消</ElButton>
        <ElButton type="primary" :loading="submitLoading" @click="handleSubmit"> 确定 </ElButton>
      </div>
    </template>
  </ElDialog>
</template>

<script setup lang="ts">
  import { ref, computed, watch, nextTick } from 'vue'
  import { ElMessage, ElTree } from 'element-plus'
  import type { RoleListItem, RoleMenuPermission } from '@/types/api/role'
  import type { BackendMenuItem } from '@/api/admin/menuApi'
  import { getMenuList } from '@/api/admin/menuApi'
  import { updateRolePermissions } from '@/api/admin/roleApi'
  import { useMenu } from '@/composables/useMenu'

  defineOptions({ name: 'RolePermissionDialog' })

  const { getMenuType, getMenuTypeTagType } = useMenu()

  interface Props {
    visible: boolean
    roleData: RoleListItem | null
  }

  const props = defineProps<Props>()
  const emit = defineEmits<{
    'update:visible': [value: boolean]
    submit: []
  }>()

  // 响应式数据
  const loading = ref(false)
  const treeLoading = ref(false)
  const submitLoading = ref(false)
  const filterText = ref('')
  const menuTreeData = ref<BackendMenuItem[]>([])
  const treeRef = ref<InstanceType<typeof ElTree>>()
  const defaultExpandedKeys = ref<(string | number)[]>([])
  const isExpanded = ref(false)

  // 计算属性
  const dialogVisible = computed({
    get: () => props.visible,
    set: (val) => emit('update:visible', val)
  })

  // 树形组件配置
  const treeProps = {
    label: 'label',
    children: 'children'
  }

  // 监听对话框打开
  watch(
    () => props.visible,
    async (val) => {
      if (val && props.roleData) {
        await loadData()
      }
    }
  )

  // 监听搜索文本
  watch(filterText, (val) => {
    treeRef.value?.filter(val)
  })

  /**
   * 加载数据
   */
  const loadData = async () => {
    loading.value = true
    // 加载菜单树
    const menuList = await getMenuList()

    // 为树形组件处理数据，将权限作为子节点
    const processMenuData = (items: BackendMenuItem[]): any[] => {
      // 过滤掉隐藏的菜单
      return items
        .filter((item) => !item.is_hide)
        .map((item) => {
          const menuNode: any = {
            ...item,
            id: item.id,
            label: item.meta?.title || item.name,
            children: [],
            // 保存原始菜单数据，用于正确判断菜单类型
            originalData: item
          }

          // 如果有权限，将权限作为子节点
          if (item.permissions?.length) {
            const permissionNodes = item.permissions.map((perm) => ({
              id: `${item.id}_${perm.id}`,
              label: perm.title,
              isPermission: true,
              parentMenuId: item.id,
              permissionId: perm.id
            }))
            menuNode.children.push(...permissionNodes)
          }

          // 如果有子菜单，递归处理
          if (item.children?.length) {
            menuNode.children.push(...processMenuData(item.children as BackendMenuItem[]))
          }

          return menuNode
        })
    }

    menuTreeData.value = processMenuData(menuList)

    // 设置已选中的菜单和权限（使用角色数据中的 menus 字段）
    nextTick(() => {
      const checkedKeys: (string | number)[] = []
      const expandedKeys: (string | number)[] = []

      if (props.roleData?.menus?.length) {
        props.roleData.menus.forEach((menu) => {
          // 处理菜单权限的勾选逻辑
          // 由于 ElTree 设置了 check-strictly="false"（父子联动），
          // 如果先勾选父节点（菜单），会自动勾选所有子节点（权限）
          // 因此需要根据不同情况处理：

          if (menu.permission_ids?.length) {
            // 情况1：菜单有具体权限
            // 只勾选具体的权限节点，不勾选菜单节点
            // 这样可以避免自动勾选该菜单下的所有权限
            expandedKeys.push(menu.menu_id)
            menu.permission_ids.forEach((permId) => {
              const permKey = `${menu.menu_id}_${permId}`
              checkedKeys.push(permKey)
            })
          } else {
            // 情况2：菜单没有具体权限（只有菜单访问权限）
            // 直接勾选菜单节点
            checkedKeys.push(menu.menu_id)
          }
        })

        treeRef.value?.setCheckedKeys(checkedKeys)

        // 设置默认展开的节点（只展开有选中权限的菜单）
        defaultExpandedKeys.value = expandedKeys
      }
    })
    loading.value = false
  }

  /**
   * 过滤节点
   */
  const filterNode = (value: string, data: any) => {
    if (!value) return true

    // 如果是权限节点，检查其父菜单是否匹配
    if (data.isPermission) {
      // 权限节点的标题匹配
      const permTitle = data.label || ''
      if (typeof permTitle === 'string' && permTitle.toLowerCase().includes(value.toLowerCase())) {
        return true
      }

      // 查找父菜单节点
      const findParentMenu = (nodes: any[], targetId: number): any => {
        for (const node of nodes) {
          if (node.id === targetId) {
            return node
          }
          if (node.children?.length) {
            const found = findParentMenu(node.children, targetId)
            if (found) return found
          }
        }
        return null
      }

      // 检查父菜单是否匹配
      const parentMenu = findParentMenu(menuTreeData.value, data.parentMenuId)
      if (parentMenu) {
        const parentTitle = parentMenu.label || ''
        return (
          typeof parentTitle === 'string' && parentTitle.toLowerCase().includes(value.toLowerCase())
        )
      }
      return false
    }

    // 菜单节点的标题匹配
    const title = data.label || ''
    return typeof title === 'string' ? title.toLowerCase().includes(value.toLowerCase()) : false
  }

  /**
   * 处理树节点勾选
   */
  const handleTreeCheck = () => {
    // 权限已经作为子节点，不需要特殊处理
  }

  /**
   * 切换全部展开/收起
   */
  const toggleExpandAll = () => {
    isExpanded.value = !isExpanded.value

    if (isExpanded.value) {
      // 获取所有节点的 key
      const getAllKeys = (nodes: any[]): (string | number)[] => {
        const keys: (string | number)[] = []
        nodes.forEach((node) => {
          keys.push(node.id)
          if (node.children?.length) {
            keys.push(...getAllKeys(node.children))
          }
        })
        return keys
      }

      const allKeys = getAllKeys(menuTreeData.value)
      // 展开所有节点
      allKeys.forEach((key) => {
        treeRef.value?.store.nodesMap[key]?.expand()
      })
    } else {
      // 收起所有节点
      Object.keys(treeRef.value?.store.nodesMap || {}).forEach((key) => {
        treeRef.value?.store.nodesMap[key]?.collapse()
      })
    }
  }

  /**
   * 处理提交
   */
  const handleSubmit = async () => {
    submitLoading.value = true
    // 获取选中的节点
    const checkedNodes = treeRef.value?.getCheckedNodes() || []

    // 分离菜单节点和权限节点
    const menuNodes = checkedNodes.filter((node: any) => !node.isPermission)
    const permissionNodes = checkedNodes.filter((node: any) => node.isPermission)

    // 构建提交数据
    const menusMap = new Map<number, number[]>()

    // 先添加所有选中的菜单
    menuNodes.forEach((node: any) => {
      if (!menusMap.has(node.id)) {
        menusMap.set(node.id, [])
      }
    })

    // 添加选中的权限到对应的菜单
    permissionNodes.forEach((node: any) => {
      const menuId = node.parentMenuId
      const permId = node.permissionId

      if (!menusMap.has(menuId)) {
        menusMap.set(menuId, [])
      }
      menusMap.get(menuId)!.push(permId)
    })

    // 转换为数组格式
    const menus: RoleMenuPermission[] = Array.from(menusMap.entries()).map(
      ([menu_id, permission_ids]) => ({
        menu_id,
        permission_ids
      })
    )

    // 构建提交数据
    const submitData = {
      menus: menus
    }

    // 调用后端接口更新权限
    await updateRolePermissions(props.roleData!.id, submitData)
    ElMessage.success('权限分配成功')
    emit('submit')
    handleClose()
    submitLoading.value = false
  }

  /**
   * 关闭对话框
   */
  const handleClose = () => {
    filterText.value = ''
    defaultExpandedKeys.value = []
    isExpanded.value = false
    dialogVisible.value = false
  }
</script>

<style lang="scss" scoped>
  .permission-container {
    padding: 0 10px;
  }

  .toolbar {
    display: flex;
    gap: 10px;
    margin-bottom: 15px;

    .filter-input {
      flex: 1;
    }
  }

  .tree-scrollbar {
    border: 1px solid var(--el-border-color);
    border-radius: 4px;
  }

  .tree-node {
    display: inline-flex;
    gap: 8px;
    align-items: center;

    .menu-icon {
      font-size: 16px;
    }

    .menu-tag {
      margin-left: 8px;
    }
  }

  .dialog-footer {
    text-align: right;
  }

  :deep(.el-tree-node__content) {
    height: auto;
    min-height: 36px;
    padding: 8px 0;
  }

  :deep(.el-tree-node__expand-icon) {
    margin-right: 8px;
  }
</style>

<style lang="scss">
  // 全局样式，美化权限数据展示弹窗
  .role-permission-alert {
    .el-message-box__message {
      padding: 0;
    }

    pre {
      margin: 0;
      word-wrap: break-word;
      white-space: pre-wrap;
    }
  }
</style>
