import request from '@/utils/http'
import type { Category, CategoryForm } from '@/types/api'

/**
 * 构建树形结构
 */
const buildTree = (flatData: Category[]): Category[] => {
  const map = new Map<number, Category>()
  const tree: Category[] = []

  // 初始化所有节点，每个节点都有children数组
  flatData.forEach((item) => {
    map.set(item.id, { ...item, children: [] })
  })

  // 构建树形结构
  flatData.forEach((item) => {
    const node = map.get(item.id)!
    if (item.parent_id === 0) {
      tree.push(node)
    } else if (item.parent_id !== null) {
      const parent = map.get(item.parent_id)
      if (parent && parent.children) {
        parent.children.push(node)
      }
    }
  })

  return tree
}

/**
 * 获取分类列表（树形结构）
 */
export const getCategoryList = async (): Promise<Category[]> => {
  const flatData = await request.get<Category[]>({
    url: '/admin/categories'
  })
  return buildTree(flatData)
}

/**
 * 创建分类
 */
export const createCategory = (data: CategoryForm): Promise<Category> => {
  return request.post<Category>({
    url: '/admin/categories',
    data
  })
}

/**
 * 更新分类
 */
export const updateCategory = (id: number | string, data: CategoryForm): Promise<Category> => {
  return request.put<Category>({
    url: `/admin/categories/${id}`,
    data
  })
}

/**
 * 删除分类
 */
export const deleteCategory = (id: number | string): Promise<void> => {
  return request.del<void>({
    url: `/admin/categories/${id}`
  })
}

/**
 * 获取分类的子分类
 */
export const getCategoryChildren = (parentId: number | string): Promise<Category[]> => {
  return request.get<Category[]>({
    url: `/admin/categories/children/${parentId}`
  })
}
