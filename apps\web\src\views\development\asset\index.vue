<template>
  <div class="asset-page art-full-height art-page-view">
    <!-- 搜索栏 -->
    <ArtSearchBar
      v-model:filter="searchForm"
      :items="searchFormItems"
      :showExpand="true"
      @search="() => handleSearch()"
      @reset="handleReset"
    ></ArtSearchBar>

    <ElCard shadow="never" class="art-table-card">
      <!-- 表格头部 -->
      <ArtTableHeader v-model:columns="columnChecks" @refresh="handleRefresh">
        <template #left>
          <ElButton type="primary" @click="handleAdd" v-ripple v-if="hasAuth('add')"
            >新增资产</ElButton
          >
        </template>
      </ArtTableHeader>

      <!-- 表格 -->
      <ArtTable
        ref="tableRef"
        row-key="id"
        :loading="loading"
        :data="tableData"
        :currentPage="pagination.currentPage"
        :pageSize="pagination.pageSize"
        :total="pagination.total"
        :marginTop="10"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      >
        <template #default>
          <ElTableColumn v-for="col in visibleColumns" :key="col.prop || col.type" v-bind="col" />
        </template>
      </ArtTable>

      <!-- 新增/编辑抽屉 -->
      <AssetFormDrawer
        v-model="drawerVisible"
        :asset-id="currentAssetId"
        @success="handleDrawerSuccess"
      />
    </ElCard>
  </div>
</template>

<script setup lang="ts">
  defineOptions({ name: 'AssetManagement' })

  // Vue 核心
  import { ref, reactive, computed, onMounted, h } from 'vue'

  // UI 框架
  import { ElMessage, ElMessageBox } from 'element-plus'

  // 内部 hooks
  import { useCheckedColumns } from '@/composables/useCheckedColumns'
  import { useAuth } from '@/composables/useAuth'

  // 内部组件
  import ArtButtonTable from '@/components/custom/art-button-table/index.vue'
  import AssetFormDrawer from './components/AssetFormDrawer.vue'

  // API
  import { getAssetList, deleteAsset } from '@/api/admin/assetApi'
  import { useDictionaryStore } from '@/store/modules/dictionary'
  import { useRegionStore } from '@/store/modules/region'
  import { formatDate } from '@/utils'

  // 类型定义
  import type { ColumnOption } from '@/types/component'
  import type { Asset, AssetQueryParams } from '@/types/api/asset'
  import type { DictionaryItem } from '@/types/api/dictionary'

  // 搜索表单
  const searchForm = reactive({
    keyword: '',
    asset_source: null,
    asset_status: null,
    asset_condition: null,
    is_accessory: null as string | null,
    asset_category_id: undefined as number | undefined,
    department_category_id: undefined as number | undefined,
    industry_category_id: undefined as number | undefined
  })

  // 搜索表单配置
  const searchFormItems = computed(() => [
    {
      prop: 'keyword',
      label: '关键词',
      type: 'input' as const,
      placeholder: '资产名称/品牌/型号/序列号'
    },
    {
      prop: 'asset_source',
      label: '资产来源',
      type: 'select' as const,
      config: {
        clearable: true,
        placeholder: '请选择资产来源'
      },
      options: () =>
        assetSourceOptions.value.map((item) => ({
          label: item.value,
          value: item.code
        }))
    },
    {
      prop: 'asset_status',
      label: '资产状态',
      type: 'select' as const,
      config: {
        clearable: true,
        placeholder: '请选择资产状态'
      },
      options: () =>
        assetStatusOptions.value.map((item) => ({
          label: item.value,
          value: item.code
        }))
    },
    {
      prop: 'asset_condition',
      label: '成色',
      type: 'select' as const,
      config: {
        clearable: true,
        placeholder: '请选择成色'
      },
      options: () =>
        assetConditionOptions.value.map((item) => ({
          label: item.value,
          value: item.code
        }))
    },
    {
      prop: 'is_accessory',
      label: '是否附属设备',
      type: 'select' as const,
      config: {
        clearable: true,
        placeholder: '请选择'
      },
      options: [
        { label: '是', value: 'true' },
        { label: '否', value: 'false' }
      ]
    }
  ])

  // 分页
  const pagination = reactive({
    currentPage: 1,
    pageSize: 20,
    total: 0
  })

  // 表格数据
  const loading = ref(false)
  const tableData = ref<Asset[]>([])

  // 地区名称映射
  const regionNameMap = ref<Record<string, string>>({})

  // 抽屉
  const drawerVisible = ref(false)
  const currentAssetId = ref<number | undefined>(undefined)
  const tableRef = ref()

  // Store
  const dictionaryStore = useDictionaryStore()
  const regionStore = useRegionStore()

  // 权限控制
  const { hasAuth } = useAuth()

  // 字典选项
  const assetSourceOptions = ref<DictionaryItem[]>([])
  const assetStatusOptions = ref<DictionaryItem[]>([])
  const assetConditionOptions = ref<DictionaryItem[]>([])

  // 定义表格列
  const columns = ref<ColumnOption[]>([
    {
      prop: 'name',
      label: '资产名称',
      minWidth: 180,
      show: true,
      showOverflowTooltip: true
    },
    {
      prop: 'brand',
      label: '品牌',
      width: 120,
      show: true
    },
    {
      prop: 'model',
      label: '规格型号',
      width: 150,
      show: true,
      showOverflowTooltip: true
    },
    {
      prop: 'serial_number',
      label: '序列号',
      width: 150,
      checked: false,
      showOverflowTooltip: true
    },
    {
      prop: 'asset_source',
      label: '资产来源',
      width: 100,
      show: true,
      formatter: (row: Asset) => {
        const item = assetSourceOptions.value.find((opt) => opt.code === row.asset_source)
        return item
          ? h('span', { style: `color: ${item.color || ''}` }, item.value)
          : row.asset_source || '-'
      }
    },
    {
      prop: 'asset_status',
      label: '资产状态',
      width: 120,
      show: true,
      formatter: (row: Asset) => {
        const item = assetStatusOptions.value.find((opt) => opt.code === row.asset_status)
        return item
          ? h('span', { style: `color: ${item.color || ''}` }, item.value)
          : row.asset_status || '-'
      }
    },
    {
      prop: 'asset_condition',
      label: '成色',
      width: 80,
      show: true,
      formatter: (row: Asset) => {
        const item = assetConditionOptions.value.find((opt) => opt.code === row.asset_condition)
        return item
          ? h('span', { style: `color: ${item.color || ''}` }, item.value)
          : row.asset_condition || '-'
      }
    },
    {
      prop: 'is_accessory',
      label: '附属设备',
      width: 100,
      show: true,
      formatter: (row: Asset) => (row.parent_id ? '是' : '否')
    },
    {
      prop: 'parent',
      label: '主设备',
      width: 150,
      checked: false,
      formatter: (row: Asset) => row.parent?.name || '-',
      showOverflowTooltip: true
    },
    {
      prop: 'location',
      label: '所在地',
      minWidth: 200,
      show: true,
      formatter: (row: Asset) => {
        if (!row.region_code) return '-'
        return regionNameMap.value[row.region_code] || row.region_code
      },
      showOverflowTooltip: true
    },
    {
      prop: 'start_date',
      label: '启用日期',
      width: 110,
      show: true,
      formatter: (row: Asset) => (row.start_date ? formatDate(row.start_date, 'YYYY-MM-DD') : '-')
    },
    {
      prop: 'attachments_count',
      label: '附件数量',
      width: 100,
      checked: false,
      formatter: (row: Asset) => row.attachments_count || 0
    },
    {
      prop: 'children_count',
      label: '附属设备',
      width: 100,
      checked: false,
      formatter: (row: Asset) => row.children_count || 0
    },
    {
      prop: 'created_at',
      label: '创建时间',
      width: 160,
      checked: false,
      sortable: true,
      formatter: (row: Asset) => formatDate(row.created_at, 'YYYY-MM-DD HH:mm')
    },
    {
      prop: 'operation',
      label: '操作',
      width: 180,
      fixed: 'right' as const,
      show: true,
      formatter: (row: Asset) => {
        const buttons = [
          h(ArtButtonTable, {
            type: 'view',
            onClick: () => handleDetail(row)
          })
        ]

        if (hasAuth('edit')) {
          buttons.push(
            h(ArtButtonTable, {
              type: 'edit',
              onClick: () => handleEdit(row)
            })
          )
        }

        if (hasAuth('delete')) {
          buttons.push(
            h(ArtButtonTable, {
              type: 'delete',
              onClick: () => handleDelete(row)
            })
          )
        }

        return h('div', { style: 'display: flex; gap: 5px;' }, buttons)
      }
    }
  ])

  // 使用列选择组合式函数
  const { columns: visibleColumns, columnChecks } = useCheckedColumns(() => columns.value)

  // 加载字典数据
  const loadDictionaries = async () => {
    const [sourceData, statusData, conditionData] = await Promise.all([
      dictionaryStore.fetchItemsByCode('asset_source'),
      dictionaryStore.fetchItemsByCode('asset_status'),
      dictionaryStore.fetchItemsByCode('asset_condition')
    ])

    assetSourceOptions.value = sourceData || []
    assetStatusOptions.value = statusData || []
    assetConditionOptions.value = conditionData || []
  }

  // 加载地区名称
  const loadRegionNames = async (assets: Asset[]) => {
    const regionCodes = [
      ...new Set(assets.map((asset) => asset.region_code).filter(Boolean))
    ] as string[]

    if (regionCodes.length === 0) return

    // 批量获取地区名称
    await Promise.all(
      regionCodes.map(async (code) => {
        if (code && !regionNameMap.value[code]) {
          const name = await regionStore.getRegionNameByCode(code)
          regionNameMap.value[code] = name
        }
      })
    )
  }

  // 获取列表数据
  const getList = async () => {
    loading.value = true
    try {
      const params: AssetQueryParams = {
        keyword: searchForm.keyword || undefined,
        asset_source: searchForm.asset_source || undefined,
        asset_status: searchForm.asset_status || undefined,
        asset_condition: searchForm.asset_condition || undefined,
        is_accessory:
          searchForm.is_accessory === 'true'
            ? true
            : searchForm.is_accessory === 'false'
              ? false
              : undefined,
        asset_category_ids: searchForm.asset_category_id
          ? [searchForm.asset_category_id]
          : undefined,
        // department_category_id: searchForm.department_category_id,
        // industry_category_id: searchForm.industry_category_id,
        page: pagination.currentPage,
        per_page: pagination.pageSize
      }

      const res = await getAssetList(params)

      tableData.value = res.data || []
      pagination.total = res.meta.total || 0

      // 加载地区名称
      await loadRegionNames(tableData.value)
    } finally {
      loading.value = false
    }
  }

  // 搜索
  const handleSearch = () => {
    pagination.currentPage = 1
    getList()
  }

  // 重置
  const handleReset = () => {
    searchForm.keyword = ''
    searchForm.asset_source = null
    searchForm.asset_status = null
    searchForm.asset_condition = null
    searchForm.is_accessory = null
    searchForm.asset_category_id = undefined
    searchForm.department_category_id = undefined
    searchForm.industry_category_id = undefined
    handleSearch()
  }

  // 刷新
  const handleRefresh = () => {
    getList()
  }

  // 分页大小变化
  const handleSizeChange = () => {
    pagination.currentPage = 1
    getList()
  }

  // 当前页变化
  const handleCurrentChange = () => {
    getList()
  }

  // 新增
  const handleAdd = () => {
    currentAssetId.value = undefined
    drawerVisible.value = true
  }

  // 详情
  const handleDetail = (row: Asset) => {
    // TODO: 跳转到详情页面或打开详情对话框
    ElMessage.info(`查看资产详情: ${row.name}`)
  }

  // 编辑
  const handleEdit = (row: Asset) => {
    currentAssetId.value = row.id
    drawerVisible.value = true
  }

  // 删除
  const handleDelete = async (row: Asset) => {
    try {
      await ElMessageBox.confirm(`确定要删除资产"${row.name}"吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })

      await deleteAsset(row.id)
      ElMessage.success('删除成功')
      getList()
    } catch {
      // 用户取消删除，不需要处理
    }
  }

  // 抽屉成功
  const handleDrawerSuccess = () => {
    getList()
  }

  // 初始化
  onMounted(() => {
    loadDictionaries()
    getList()
  })
</script>

<style lang="scss" scoped>
  .asset-page {
    height: 100%;
    padding: 20px;
  }

  :deep(.el-form-item) {
    margin-bottom: 18px;
  }
</style>
