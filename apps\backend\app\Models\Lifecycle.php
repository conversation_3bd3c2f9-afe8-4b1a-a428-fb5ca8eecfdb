<?php

namespace App\Models;

use App\Traits\HasAttachments;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\Models\BaseModel;

/**
 * @property int $id
 * @property int|null $asset_id 资产ID
 * @property string $type 类型
 * @property int|null $date 日期
 * @property int|null $initiator_id 发起人ID
 * @property string|null $content 内容
 * @property int|null $acceptance_entity_id 验收主体ID
 * @property int|null $acceptance_personnel_id 验收人员ID
 * @property int|null $acceptance_time 验收时间
 * @property int|null $created_by 创建人
 * @property int|null $updated_by 更新人
 * @property int|null $created_at 创建时间
 * @property int|null $updated_at 更新时间
 * @property \Illuminate\Support\Carbon|null $deleted_at 删除时间
 * @property-read \App\Models\Entity|null $acceptanceEntity
 * @property-read \App\Models\EntityContact|null $acceptancePersonnel
 * @property-read \App\Models\Asset|null $asset
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\User> $assistants
 * @property-read int|null $assistants_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Attachment> $attachments
 * @property-read int|null $attachments_count
 * @property-read \App\Models\User|null $creator
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\LifecycleFollowUp> $followUps
 * @property-read int|null $follow_ups_count
 * @property-read \App\Models\User|null $initiator
 * @property-read \App\Models\User|null $updater
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Lifecycle newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Lifecycle newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Lifecycle onlyTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Lifecycle query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Lifecycle whereAcceptanceEntityId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Lifecycle whereAcceptancePersonnelId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Lifecycle whereAcceptanceTime($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Lifecycle whereAssetId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Lifecycle whereContent($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Lifecycle whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Lifecycle whereCreatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Lifecycle whereDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Lifecycle whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Lifecycle whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Lifecycle whereInitiatorId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Lifecycle whereType($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Lifecycle whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Lifecycle whereUpdatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Lifecycle withTrashed(bool $withTrashed = true)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Lifecycle withoutTrashed()
 * @mixin \Eloquent
 */
class Lifecycle extends BaseModel
{
    use HasAttachments, HasFactory, SoftDeletes;

    protected $fillable = [
        'asset_id',
        'type',
        'date',
        'initiator_id',
        'content',
        'acceptance_entity_id',
        'acceptance_personnel_id',
        'acceptance_time',
        'created_by',
        'updated_by',
    ];

    protected $casts = [
        'created_at' => 'integer',
        'updated_at' => 'integer',
    ];

    protected $hidden = [
        'deleted_at',
    ];

    /**
     * 获取关联的资产
     */
    public function asset(): BelongsTo
    {
        return $this->belongsTo(Asset::class, 'asset_id');
    }

    /**
     * 获取发起人
     */
    public function initiator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'initiator_id');
    }

    /**
     * 获取协助人员
     */
    public function assistants(): BelongsToMany
    {
        return $this->belongsToMany(User::class, 'lifecycle_assistants', 'lifecycle_id', 'user_id');
    }

    /**
     * 获取验收主体
     */
    public function acceptanceEntity(): BelongsTo
    {
        return $this->belongsTo(Entity::class, 'acceptance_entity_id');
    }

    /**
     * 获取验收人员
     */
    public function acceptancePersonnel(): BelongsTo
    {
        return $this->belongsTo(EntityContact::class, 'acceptance_personnel_id');
    }

    /**
     * 获取跟进记录
     */
    public function followUps(): HasMany
    {
        return $this->hasMany(LifecycleFollowUp::class);
    }

    /**
     * 获取创建人
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * 获取更新人
     */
    public function updater(): BelongsTo
    {
        return $this->belongsTo(User::class, 'updated_by');
    }

    /**
     * 检查用户是否是协助人员
     */
    public function isAssistant(int $userId): bool
    {
        return $this->assistants()->where('user_id', $userId)->exists();
    }

    /**
     * 同步协助人员
     */
    public function syncAssistants(array $userIds): void
    {
        $this->assistants()->sync($userIds);
    }
}
