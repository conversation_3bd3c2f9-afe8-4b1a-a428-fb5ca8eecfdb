.ecommerce {
  :deep(.card) {
    box-sizing: border-box;
    padding: 20px;
    background-color: var(--art-main-bg-color);
    border-radius: var(--custom-radius);

    .card-header {
      padding-bottom: 15px;

      .title {
        font-size: 18px;
        font-weight: 500;
        color: var(--art-gray-900);

        i {
          margin-left: 10px;
        }
      }

      .subtitle {
        font-size: 14px;
        color: var(--art-gray-500);
      }
    }
  }

  :deep(.icon-text-widget) {
    display: flex;
    justify-content: space-around;

    .item {
      display: flex;
      align-items: center;

      .icon {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 42px;
        height: 42px;
        margin-right: 10px;
        line-height: 42px;
        color: var(--main-color);
        background-color: var(--el-color-primary-light-9);
        border-radius: 8px;

        i {
          font-size: 20px;
        }
      }

      .content {
        p {
          font-size: 18px;
        }

        span {
          font-size: 14px;
        }
      }
    }
  }

  .no-margin-bottom {
    margin-bottom: 0 !important;
  }

  .el-col {
    margin-bottom: 20px;
  }
}
