<?php

/**
 * 测试使用附件ID进行资产导入
 */

require_once __DIR__ . '/vendor/autoload.php';

use Illuminate\Foundation\Application;
use App\Models\Attachment;
use App\Models\AssetImportTask;
use App\Jobs\ProcessAssetImport;
use App\Services\AssetImportService;

// 启动Laravel应用
$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "=== 使用附件ID测试资产导入 ===\n\n";

try {
    // 查找Excel附件
    echo "1. 查找Excel附件...\n";
    
    $excelAttachments = Attachment::where('mime_type', 'like', '%spreadsheet%')
        ->orWhere('file_name', 'like', '%.xlsx')
        ->orWhere('file_name', 'like', '%.xls')
        ->orderBy('created_at', 'desc')
        ->get(['id', 'file_name', 'file_path', 'file_size']);
    
    if ($excelAttachments->count() === 0) {
        echo "没有找到Excel附件，请先上传Excel文件\n";
        exit(1);
    }
    
    echo "找到 " . $excelAttachments->count() . " 个Excel附件:\n";
    foreach ($excelAttachments as $attachment) {
        echo "  ID: {$attachment->id} - {$attachment->file_name} (" . 
             number_format($attachment->file_size / 1024, 2) . " KB)\n";
    }
    
    // 选择第一个Excel附件进行测试
    $testAttachment = $excelAttachments->first();
    echo "\n选择附件进行测试: ID {$testAttachment->id} - {$testAttachment->file_name}\n";
    
    // 验证文件路径
    $service = new AssetImportService();
    $reflection = new ReflectionClass($service);
    $method = $reflection->getMethod('getActualFilePath');
    $method->setAccessible(true);
    
    $resolvedPath = $method->invoke($service, $testAttachment->file_path);
    if (!file_exists($resolvedPath)) {
        echo "错误：文件不存在 - {$resolvedPath}\n";
        exit(1);
    }
    
    echo "文件路径验证成功: {$resolvedPath}\n";
    
    // 创建导入任务
    echo "\n2. 创建导入任务...\n";
    
    $importTask = AssetImportTask::create([
        'file_path' => $testAttachment->file_path,
        'original_filename' => $testAttachment->file_name,
        'status' => 'pending',
        'created_by' => 1, // 假设用户ID为1
    ]);
    
    echo "导入任务创建成功，ID: {$importTask->id}\n";
    
    // 测试直接处理（不使用队列）
    echo "\n3. 直接处理导入任务...\n";
    
    try {
        $importTask->markAsProcessing();
        echo "任务状态更新为处理中\n";
        
        $result = $service->processImport($importTask);
        
        echo "导入处理完成！\n";
        echo "总行数: " . $result['total_rows'] . "\n";
        echo "成功行数: " . $result['success_rows'] . "\n";
        echo "失败行数: " . $result['failed_rows'] . "\n";
        
        if (!empty($result['errors'])) {
            echo "\n错误详情:\n";
            foreach (array_slice($result['errors'], 0, 5) as $error) {
                echo "  行 {$error['row']}: {$error['error']}\n";
            }
            if (count($result['errors']) > 5) {
                echo "  ... 还有 " . (count($result['errors']) - 5) . " 个错误\n";
            }
        }
        
        $importTask->markAsCompleted($result);
        echo "\n任务状态更新为完成\n";
        
    } catch (Exception $e) {
        echo "导入处理失败: " . $e->getMessage() . "\n";
        echo "错误位置: " . $e->getFile() . ":" . $e->getLine() . "\n";
        
        $importTask->markAsFailed([
            'error' => $e->getMessage(),
            'trace' => $e->getTraceAsString(),
        ]);
        
        echo "任务状态更新为失败\n";
    }
    
    // 显示最终任务状态
    echo "\n4. 最终任务状态...\n";
    $importTask->refresh();
    
    echo "任务ID: {$importTask->id}\n";
    echo "状态: {$importTask->status}\n";
    echo "文件名: {$importTask->original_filename}\n";
    echo "总行数: {$importTask->total_rows}\n";
    echo "成功行数: {$importTask->success_rows}\n";
    echo "失败行数: {$importTask->failed_rows}\n";
    
    if ($importTask->summary) {
        echo "摘要: {$importTask->summary}\n";
    }
    
    if ($importTask->error_details) {
        echo "错误详情: " . json_encode($importTask->error_details, JSON_UNESCAPED_UNICODE) . "\n";
    }
    
    echo "\n=== 测试完成 ===\n";
    
    if ($importTask->status === 'completed') {
        echo "✓ 导入测试成功！\n";
        echo "\n可以使用以下API进行导入:\n";
        echo "POST /api/admin/assets/import/{$testAttachment->id}\n";
    } else {
        echo "✗ 导入测试失败，请检查错误信息\n";
    }
    
} catch (Exception $e) {
    echo "✗ 测试失败：" . $e->getMessage() . "\n";
    echo "错误位置：" . $e->getFile() . ":" . $e->getLine() . "\n";
    echo "堆栈跟踪：\n" . $e->getTraceAsString() . "\n";
}

echo "\n=== 测试结束 ===\n";
