<?php

namespace App\Services\Attachment\Contracts;

use Illuminate\Http\UploadedFile;

interface StorageDriver
{
    /**
     * 本地上传文件
     */
    public function store(UploadedFile $file, string $path): string;

    /**
     * 获取上传凭证（可以是STS凭证、签名URL等）
     * @param array $params 上传参数
     * @return array 返回凭证信息
     * @throws \RuntimeException 如果驱动不支持直传
     */
    public function getUploadCredentials(array $params = []): array;

    /**
     * 验证回调
     */
    public function validateCallback(array $data): bool;

    /**
     * 删除文件
     */
    public function delete(string $path): bool;

    /**
     * 获取访问URL
     */
    public function url(string $path): string;

    /**
     * 判断文件是否存在
     */
    public function exists(string $path): bool;

    /**
     * 获取存储类型标识
     */
    public function getType(): string;
}
