# 资产模板导出API文档

## 接口概述

该接口用于导出资产导入模板，包含所有字段的Excel文件，方便用户进行资产批量导入。模板会根据系统中的分类数据动态生成分类列。

## 接口详情

### 基本信息
- **接口路径**: `GET /api/admin/assets/export-template`
- **接口名称**: 导出资产模板
- **请求方式**: GET
- **需要认证**: 是（需要Bearer Token）

### 请求参数
无

### 请求头
```
Authorization: Bearer {token}
Accept: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet
```

### 响应
- **响应类型**: 文件下载
- **文件格式**: Excel (.xlsx)
- **文件名**: `资产导入模板_YYYY-MM-DD_HH-mm-ss.xlsx`

## Excel模板字段说明

### 基础信息字段
1. **资产名称*** (必填)
   - 描述：资产的名称
   - 示例：办公台式电脑

2. **品牌**
   - 描述：资产的品牌
   - 示例：联想

3. **规格型号**
   - 描述：资产的规格型号
   - 示例：ThinkCentre M720

4. **序列号**
   - 描述：资产的序列号
   - 示例：ABC123456789

### 字典字段
5. **资产来源**
   - 描述：资产的来源（字典code）
   - 可选值：
     - `produce`: 自产
     - `purchase`: 采购
     - `transfer`: 转让
     - `donate`: 捐赠

6. **资产状态**
   - 描述：资产的当前状态（字典code）
   - 可选值：
     - `new_unstocked`: 全新未出库
     - `in_use`: 正常使用中
     - `pending_check`: 待检测
     - `scrap_registered`: 报废登记
     - `under_repair`: 维修中

7. **成色**
   - 描述：资产的成色（字典code）
   - 可选值：
     - `brand_new`: 全新
     - `second_hand`: 二手
     - `refurbished`: 翻新

### 关系字段
8. **主设备**
   - 描述：主设备的名称，为空表示主设备，填写名称表示附属设备
   - 示例：办公台式电脑（表示该资产是办公台式电脑的附属设备）

### 地址字段
9. **所在地区**
   - 描述：资产所在地区
   - 示例：深圳

10. **详细地址**
    - 描述：详细的地址信息
    - 示例：XX街道XX号XX大厦

### 时间字段
11. **启用日期**
    - 描述：资产启用日期
    - 格式：YYYY-MM-DD
    - 示例：2024-01-01

### 数值字段
12. **合同质保期(月)**
    - 描述：合同质保期，单位为月
    - 示例：36

13. **质保期预警(天)**
    - 描述：质保期预警天数
    - 示例：30

14. **维护周期(天)**
    - 描述：维护周期，单位为天
    - 示例：90

15. **预计使用年限(年)**
    - 描述：预计使用年限，单位为年
    - 示例：5

### 相关主体字段
16. **生产厂商名称**
    - 描述：生产厂商的名称
    - 示例：北京凯吉特医药科技发展有限公司

17. **生产厂商联系人**
    - 描述：生产厂商的联系人姓名
    - 示例：顾芳

18. **生产厂商联系电话**
    - 描述：生产厂商的联系电话
    - 示例：17281619968

19. **生产厂商职位**
    - 描述：生产厂商联系人的职位
    - 示例：业务员

20. **供应商名称**
    - 描述：供应商的名称
    - 示例：北京康达和美经贸有限公司

21. **供应商联系人**
    - 描述：供应商的联系人姓名
    - 示例：常芳

22. **供应商联系电话**
    - 描述：供应商的联系电话
    - 示例：13552224534

23. **供应商职位**
    - 描述：供应商联系人的职位
    - 示例：技术人员

24. **服务商名称**
    - 描述：服务商的名称
    - 示例：北京科利达医疗设备发展有限公司

25. **服务商联系人**
    - 描述：服务商的联系人姓名
    - 示例：戴芳

26. **服务商联系电话**
    - 描述：服务商的联系电话
    - 示例：13204091385

27. **服务商职位**
    - 描述：服务商联系人的职位
    - 示例：业务员

28. **售后部名称**
    - 描述：售后服务部门的名称
    - 示例：北京科迪信生物技术开发中心

29. **售后部联系人**
    - 描述：售后服务部门的联系人姓名
    - 示例：常芳

30. **售后部联系电话**
    - 描述：售后服务部门的联系电话
    - 示例：13552224534

31. **售后部职位**
    - 描述：售后服务部门联系人的职位
    - 示例：业务员

32. **备注**
    - 描述：资产的备注信息
    - 示例：这是一台办公用台式电脑

### 动态分类字段
33. **分类列（动态生成）**
    - 描述：根据系统中的分类数据动态生成的分类列
    - 格式：每个分类为一列，用空格表示层级关系
    - 示例：
      - `医疗分类`（顶级分类）
      - `  科室`（二级分类，前面有2个空格）
      - `    护理`（三级分类，前面有4个空格）
    - 填写方式：
      - 选中分类：填写 `✓` 或 `是` 或 `1`
      - 未选中：留空或填写 `否` 或 `0`
    - 说明：
      - 可以同时选择多个分类
      - 分类列会根据系统中的分类数据自动更新
      - 分类的层级关系通过缩进空格表示

## 使用示例

### cURL示例
```bash
curl -X GET "http://your-domain.com/api/admin/assets/export-template" \
  -H "Authorization: Bearer your-token-here" \
  -H "Accept: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" \
  --output "资产导入模板.xlsx"
```

### JavaScript示例
```javascript
fetch('/api/admin/assets/export-template', {
  method: 'GET',
  headers: {
    'Authorization': 'Bearer ' + token,
    'Accept': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
  }
})
.then(response => response.blob())
.then(blob => {
  const url = window.URL.createObjectURL(blob);
  const a = document.createElement('a');
  a.href = url;
  a.download = '资产导入模板.xlsx';
  a.click();
  window.URL.revokeObjectURL(url);
});
```

## 注意事项

1. **必填字段**: 资产名称是必填字段
2. **字典值**: 资产来源、资产状态、成色等字段必须使用字典code值，不能使用中文标签
3. **日期格式**: 启用日期必须使用YYYY-MM-DD格式
4. **分类选择**: 分类列使用复选框形式，选中填写 `✓`，未选中留空
5. **主设备**: 主设备字段填写主设备的名称，不是ID
6. **相关主体**: 相关主体信息填写具体的名称和联系方式，不是ID
7. **动态分类**: 分类列会根据系统中的分类数据动态生成，分类变更后导出的模板也会相应更新

## 错误处理

如果请求失败，API会返回相应的HTTP状态码和错误信息：

- `401 Unauthorized`: 未提供有效的认证token
- `403 Forbidden`: 用户没有权限访问此接口
- `500 Internal Server Error`: 服务器内部错误

## 相关接口

- `POST /api/admin/assets` - 创建资产
- `GET /api/admin/assets` - 获取资产列表
- `PUT /api/admin/assets/{id}` - 更新资产
- `DELETE /api/admin/assets/{id}` - 删除资产
- `GET /api/admin/categories` - 获取分类列表 
