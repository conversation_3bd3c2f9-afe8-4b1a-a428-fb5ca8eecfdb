<?php

/**
 * 创建测试用的Excel文件
 */

require_once __DIR__ . '/vendor/autoload.php';

use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;

echo "创建测试Excel文件...\n";

// 创建新的电子表格
$spreadsheet = new Spreadsheet();
$sheet = $spreadsheet->getActiveSheet();

// 设置标题行
$headers = [
    '资产名称', '资产品牌', '规格型号', '序列号', '资产分类',
    '资产来源', '资产状态', '成色', '区县代码', '详细地址',
    '启用日期', '合同质保期(月)', '质保期预警(天)', '维护周期(天)', '预计使用年限(年)',
    '主体名称', '税号', '主体类型', '主体地址', '主体电话',
    '联系人姓名', '联系人电话', '职位', '部门', '备注'
];

// 写入标题行
$column = 'A';
foreach ($headers as $header) {
    $sheet->setCellValue($column . '1', $header);
    $column++;
}

// 添加测试数据
$testData = [
    [
        '办公电脑', '联想', 'ThinkCentre M720', 'ABC123456', '办公设备',
        '采购', '在用', '九成新', '110101', '北京市东城区XX街道XX号',
        '2024-01-01', '36', '30', '90', '5',
        '北京科技有限公司', '91110000123456789X', '企业', '北京市朝阳区XX路XX号', '010-12345678',
        '张三', '13800138000', '技术经理', '技术部', '负责设备维护'
    ],
    [
        '打印机', '惠普', 'LaserJet Pro M404n', 'HP789012', '办公设备',
        '采购', '在用', '全新', '110101', '北京市东城区XX街道XX号',
        '2024-02-01', '24', '15', '60', '3',
        '北京科技有限公司', '91110000123456789X', '企业', '北京市朝阳区XX路XX号', '010-12345678',
        '李四', '13900139000', '行政专员', '行政部', '负责办公用品管理'
    ],
    [
        '服务器', '戴尔', 'PowerEdge R740', 'DELL345678', 'IT设备',
        '采购', '在用', '九成新', '110101', '北京市东城区XX街道XX号机房',
        '2023-12-01', '60', '60', '30', '8',
        '北京科技有限公司', '91110000123456789X', '企业', '北京市朝阳区XX路XX号', '010-12345678',
        '王五', '13700137000', '系统管理员', '技术部', '负责服务器运维'
    ]
];

// 写入测试数据
$row = 2;
foreach ($testData as $data) {
    $column = 'A';
    foreach ($data as $value) {
        $sheet->setCellValue($column . $row, $value);
        $column++;
    }
    $row++;
}

// 设置列宽自适应
foreach (range('A', 'Y') as $col) {
    $sheet->getColumnDimension($col)->setAutoSize(true);
}

// 保存文件
$writer = new Xlsx($spreadsheet);
$filename = 'storage/app/public/test_assets_import.xlsx';

// 确保目录存在
$dir = dirname($filename);
if (!is_dir($dir)) {
    mkdir($dir, 0755, true);
}

$writer->save($filename);

echo "测试Excel文件已创建：{$filename}\n";
echo "文件包含 " . count($testData) . " 行测试数据\n";
echo "\n可以使用此文件测试导入功能：\n";
echo "curl -X POST http://your-domain/api/admin/assets/import \\\n";
echo "  -H \"Authorization: Bearer your-token\" \\\n";
echo "  -F \"file=@{$filename}\"\n";

echo "\n测试文件创建完成！\n";
