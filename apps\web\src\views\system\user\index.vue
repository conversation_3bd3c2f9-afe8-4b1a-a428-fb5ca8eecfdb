<!-- 用户管理 -->
<template>
  <div class="user-page art-full-height art-page-view">
    <!-- 搜索栏 -->
    <ArtSearchBar
      v-model:filter="searchFormState"
      :items="searchFormItems"
      @search="handleSearch"
      @reset="handleReset"
    />
    <ElCard class="art-table-card" shadow="never">
      <!-- 表格头部 -->
      <ArtTableHeader v-model:columns="columnChecks" @refresh="refreshAll">
        <template #left>
          <ElButton v-if="hasAuth('add')" @click="showDialog('add')">新增用户</ElButton>
        </template>
      </ArtTableHeader>

      <!-- 表格 -->
      <ArtTable
        :loading="isLoading"
        :data="tableData"
        :columns="columns"
        :pagination="paginationState"
        :table-config="{ rowKey: 'id' }"
        :layout="{ marginTop: 10 }"
        @row:selection-change="handleSelectionChange"
        @pagination:size-change="onPageSizeChange"
        @pagination:current-change="onCurrentPageChange"
      >
        <!-- 头像列插槽 -->
        <template #avatar="{ row }">
          <img
            v-if="row.avatar"
            :src="row.avatar"
            style="width: 40px; height: 40px; object-fit: cover; border-radius: 50%"
          />
          <span
            v-else
            style="
              display: inline-block;
              width: 40px;
              height: 40px;
              line-height: 40px;
              color: #999;
              text-align: center;
              background-color: #f0f0f0;
              border-radius: 50%;
            "
          >
            无
          </span>
        </template>

        <!-- 角色列插槽 -->
        <template #roles="{ row }">
          <div
            v-if="row.roles && row.roles.length > 0"
            style="display: flex; flex-wrap: wrap; gap: 4px"
          >
            <ElTag v-for="role in row.roles" :key="role.id" size="small">
              {{ role.name }}
            </ElTag>
          </div>
          <span v-else style="color: #999">暂无角色</span>
        </template>

        <!-- 状态列插槽 -->
        <template #status="{ row }">
          <ElTag :type="row.status === 'enable' ? 'success' : 'danger'">
            {{ row.status === 'enable' ? '启用' : '禁用' }}
          </ElTag>
        </template>

        <!-- 操作列插槽 -->
        <template #operation="{ row }">
          <ArtButtonMore
            :list="getOperationButtons(row)"
            @click="(item) => handleButtonMoreClick(item, row)"
          />
        </template>
      </ArtTable>

      <!-- 用户弹窗 -->
      <UserDialog
        v-model:visible="dialogVisible"
        :type="dialogType"
        :user-data="currentUserData"
        @submit="handleDialogSubmit"
      />

      <!-- 角色分配弹窗 -->
      <UserRoleDialog
        v-model="roleDialogVisible"
        :user-id="currentRoleUser ? Number(currentRoleUser.id) : null"
        :user-name="currentRoleUser?.nickname || ''"
        :current-role-ids="currentRoleUser?.roles?.map((r) => r.id) || []"
        @success="handleRoleDialogSuccess"
      />
    </ElCard>
  </div>
</template>

<script setup lang="ts">
  import { ref, nextTick } from 'vue'
  import { ElMessageBox, ElMessage, ElTag, ElButton } from 'element-plus'
  import { useTable } from '@/composables/useTable'
  import { useAuth } from '@/composables/useAuth'
  import { handleApiError, handleSuccess } from '@/utils/errorHandler'
  import { getUserList, deleteUser as deleteUserApi } from '@/api/admin/userApi'
  import { formatDate } from '@/utils/dataprocess/format'
  import ArtButtonMore from '@/components/core/forms/art-button-more/index.vue'
  import type { ButtonMoreItem } from '@/components/core/forms/art-button-more/index.vue'
  import UserDialog from './components/UserDialog.vue'
  import UserRoleDialog from './components/UserRoleDialog.vue'
  import type { UserListItem } from '@/types/api'
  import type { SearchFormItem } from '@/types'

  defineOptions({ name: 'User' })

  // 权限控制
  const { hasAuth } = useAuth()

  // 搜索表单状态
  const searchFormState = ref({
    keyword: '',
    status: undefined as string | undefined
  })

  // 搜索表单配置
  const searchFormItems: SearchFormItem[] = [
    {
      label: '关键词',
      prop: 'keyword',
      type: 'input',
      config: {
        clearable: true,
        placeholder: '请输入用户昵称/账号/邮箱'
      }
    },
    {
      label: '状态',
      prop: 'status',
      type: 'select',
      config: {
        clearable: true,
        placeholder: '请选择状态'
      },
      options: () => [
        { label: '启用', value: 'enable' },
        { label: '禁用', value: 'disable' }
      ]
    }
  ]

  // 弹窗相关
  const dialogType = ref<Form.DialogType>('add')
  const dialogVisible = ref(false)
  const currentUserData = ref<UserListItem | null>(null)
  const selectedRows = ref<UserListItem[]>([])

  // 角色分配相关
  const roleDialogVisible = ref(false)
  const currentRoleUser = ref<UserListItem | null>(null)

  const {
    columns,
    columnChecks,
    tableData,
    isLoading,
    paginationState,
    searchState,
    searchData,
    resetSearch,
    onPageSizeChange,
    onCurrentPageChange,
    refreshAll,
    refreshAfterCreate,
    refreshAfterUpdate,
    refreshAfterRemove
  } = useTable<UserListItem>({
    // 核心配置
    core: {
      apiFn: (params: any) => {
        // 转换参数格式
        const apiParams = {
          page: params.current,
          per_page: params.size,
          keyword: params.keyword || '',
          status: params.status || undefined
        }
        return getUserList(apiParams)
      },
      apiParams: {
        current: 1,
        size: 20,
        keyword: '',
        status: undefined
      },
      columnsFactory: () => [
        { type: 'index', width: 60, label: '序号' },
        {
          prop: 'avatar',
          label: '头像',
          width: 80,
          useSlot: true
        },
        {
          prop: 'nickname',
          label: '用户昵称',
          minWidth: 150
        },
        {
          prop: 'account',
          label: '账号',
          minWidth: 130
        },
        {
          prop: 'email',
          label: '邮箱',
          minWidth: 180,
          formatter: (row) => row.email || '-'
        },
        {
          prop: 'roles',
          label: '角色',
          minWidth: 150,
          useSlot: true
        },
        {
          prop: 'status',
          label: '状态',
          width: 100,
          useSlot: true
        },
        {
          prop: 'created_at',
          label: '创建时间',
          minWidth: 160,
          sortable: true,
          formatter: (row) =>
            row.created_at ? formatDate(row.created_at, 'YYYY-MM-DD HH:mm:ss') : ''
        },
        {
          prop: 'operation',
          label: '操作',
          width: 100,
          fixed: 'right',
          useSlot: true
        }
      ]
    },
    // 数据处理
    transform: {
      // 使用响应适配器来处理后端返回的数据格式
      responseAdapter: (response: any) => {
        // 如果响应包含 data 和 meta，说明是标准格式
        if (response.data && response.meta) {
          return {
            data: response.data,
            total: response.meta.total,
            pageSize: response.meta.per_page,
            currentPage: response.meta.current_page
          }
        }
        // 否则使用默认格式
        return {
          data: response.data || [],
          total: response.total || 0,
          pageSize: response.size || 20,
          currentPage: response.current || 1
        }
      }
    },
    // 性能优化
    performance: {
      enableCache: true, // 是否开启缓存
      cacheTime: 10 * 60 * 1000 // 缓存时间 10分钟
    },
    // 生命周期钩子
    hooks: {
      onError: (error) => ElMessage.error(error.message) // 错误处理
    },
    // 调试配置
    debug: {
      enableLog: true // 是否开启日志
    }
  })

  /**
   * 动态生成操作按钮
   */
  const getOperationButtons = (row: UserListItem): ButtonMoreItem[] => {
    const buttons: ButtonMoreItem[] = []

    if (hasAuth('edit')) {
      buttons.push({ key: 'edit', label: '编辑' })
    }

    if (hasAuth('assign_roles')) {
      buttons.push({ key: 'role', label: '分配角色' })
    }

    if (hasAuth('delete')) {
      buttons.push({ key: 'delete', label: '删除' })
    }

    return buttons
  }

  /**
   * 显示用户弹窗
   */
  const showDialog = (type: Form.DialogType, row?: UserListItem): void => {
    dialogType.value = type
    currentUserData.value = row || null
    nextTick(() => {
      dialogVisible.value = true
    })
  }

  /**
   * 处理更多按钮点击
   */
  const handleButtonMoreClick = (item: ButtonMoreItem, row: UserListItem): void => {
    switch (item.key) {
      case 'edit':
        showDialog('edit', row)
        break
      case 'role':
        showRoleDialog(row)
        break
      case 'delete':
        deleteUser(row)
        break
    }
  }

  /**
   * 删除用户
   */
  const deleteUser = async (row: UserListItem): Promise<void> => {
    try {
      await ElMessageBox.confirm(`确定要删除用户"${row.account}"吗？`, '删除确认', {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'warning'
      })

      await deleteUserApi(row.id)
      handleSuccess('删除成功')
      refreshAfterRemove() // 智能删除后刷新
    } catch (error: any) {
      if (error !== 'cancel') {
        handleApiError(error, '删除失败')
      }
    }
  }

  /**
   * 处理弹窗提交事件
   */
  const handleDialogSubmit = async () => {
    dialogVisible.value = false
    await (dialogType.value === 'add' ? refreshAfterCreate() : refreshAfterUpdate())
    currentUserData.value = null
  }

  /**
   * 处理表格行选择变化
   */
  const handleSelectionChange = (selection: UserListItem[]): void => {
    selectedRows.value = selection
    console.log('选中行数据:', selectedRows.value)
  }

  /**
   * 显示角色分配弹窗
   */
  const showRoleDialog = (row: UserListItem): void => {
    currentRoleUser.value = row
    roleDialogVisible.value = true
  }

  /**
   * 处理角色分配成功
   */
  const handleRoleDialogSuccess = (): void => {
    refreshAfterUpdate() // 刷新当前页
  }

  /**
   * 处理搜索
   */
  const handleSearch = (): void => {
    searchState.keyword = searchFormState.value.keyword
    searchState.status = searchFormState.value.status
    searchData()
  }

  /**
   * 处理重置
   */
  const handleReset = (): void => {
    searchFormState.value = {
      keyword: '',
      status: undefined
    }
    resetSearch()
  }
</script>

<style lang="scss" scoped>
  .user-page {
    display: flex;
    flex-direction: column;
  }
</style>
