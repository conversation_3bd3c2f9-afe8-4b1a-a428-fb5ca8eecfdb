/**
 * 配置管理 API
 */
import request from '@/utils/http'
import type { AppConfig, UpdateConfigsRequest } from '@/types/api'

/**
 * 获取所有配置
 */
export const getConfigs = async (): Promise<AppConfig> => {
  return request.get<AppConfig>({ url: '/admin/configs' })
}

/**
 * 更新配置（系统配置和上传配置）
 */
export const updateConfigs = async (data: UpdateConfigsRequest): Promise<void> => {
  return request.put<void>({
    url: '/admin/configs/update',
    data
  })
}
