<?php

/**
 * 分析Excel导入模板结构
 */

require_once __DIR__ . '/vendor/autoload.php';

use Illuminate\Foundation\Application;
use Maatwebsite\Excel\Facades\Excel;
use PhpOffice\PhpSpreadsheet\IOFactory;

// 启动Laravel应用
$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "=== 分析Excel导入模板 ===\n\n";

try {
    $templatePath = 'storage/app/public/资产导入模板_2025-08-14_10-26-23.xlsx';
    $fullPath = __DIR__ . '/' . $templatePath;
    
    if (!file_exists($fullPath)) {
        echo "模板文件不存在: {$fullPath}\n";
        exit(1);
    }
    
    echo "模板文件: {$templatePath}\n";
    echo "文件大小: " . number_format(filesize($fullPath) / 1024, 2) . " KB\n\n";
    
    // 使用PhpSpreadsheet直接读取
    $spreadsheet = IOFactory::load($fullPath);
    $worksheet = $spreadsheet->getActiveSheet();
    
    // 获取数据范围
    $highestRow = $worksheet->getHighestRow();
    $highestColumn = $worksheet->getHighestColumn();
    $highestColumnIndex = \PhpOffice\PhpSpreadsheet\Cell\Coordinate::columnIndexFromString($highestColumn);
    
    echo "工作表信息:\n";
    echo "  最大行数: {$highestRow}\n";
    echo "  最大列数: {$highestColumn} (索引: {$highestColumnIndex})\n\n";
    
    // 读取标题行（第一行）
    echo "1. 标题行分析:\n";
    $headers = [];
    for ($col = 1; $col <= $highestColumnIndex; $col++) {
        $cellValue = $worksheet->getCellByColumnAndRow($col, 1)->getCalculatedValue();
        if (!empty($cellValue)) {
            $headers[$col] = $cellValue;
            echo "  列 {$col}: {$cellValue}\n";
        }
    }
    
    echo "\n标题列表 (" . count($headers) . " 列):\n";
    foreach ($headers as $index => $header) {
        echo "  {$index}. {$header}\n";
    }
    
    // 读取示例数据行（如果有的话）
    echo "\n2. 示例数据分析:\n";
    if ($highestRow > 1) {
        echo "发现 " . ($highestRow - 1) . " 行数据\n";
        
        // 读取前几行数据作为示例
        $maxSampleRows = min(5, $highestRow - 1);
        for ($row = 2; $row <= $maxSampleRows + 1; $row++) {
            echo "\n第 {$row} 行数据:\n";
            for ($col = 1; $col <= $highestColumnIndex; $col++) {
                if (isset($headers[$col])) {
                    $cellValue = $worksheet->getCellByColumnAndRow($col, $row)->getCalculatedValue();
                    if (!empty($cellValue)) {
                        echo "  {$headers[$col]}: {$cellValue}\n";
                    }
                }
            }
        }
    } else {
        echo "模板中只有标题行，没有示例数据\n";
    }
    
    // 分析字段分类
    echo "\n3. 字段分类分析:\n";
    
    $assetFields = [];
    $entityFields = [];
    $contactFields = [];
    $otherFields = [];
    
    foreach ($headers as $header) {
        if (strpos($header, '资产') !== false || 
            strpos($header, '设备') !== false ||
            strpos($header, '品牌') !== false ||
            strpos($header, '型号') !== false ||
            strpos($header, '序列号') !== false ||
            strpos($header, '分类') !== false ||
            strpos($header, '来源') !== false ||
            strpos($header, '状态') !== false ||
            strpos($header, '成色') !== false ||
            strpos($header, '启用') !== false ||
            strpos($header, '质保') !== false ||
            strpos($header, '维护') !== false ||
            strpos($header, '使用年限') !== false ||
            strpos($header, '地址') !== false ||
            strpos($header, '区县') !== false) {
            $assetFields[] = $header;
        } elseif (strpos($header, '主体') !== false ||
                  strpos($header, '公司') !== false ||
                  strpos($header, '单位') !== false ||
                  strpos($header, '税号') !== false ||
                  strpos($header, '类型') !== false) {
            $entityFields[] = $header;
        } elseif (strpos($header, '联系人') !== false ||
                  strpos($header, '姓名') !== false ||
                  strpos($header, '电话') !== false ||
                  strpos($header, '职位') !== false ||
                  strpos($header, '部门') !== false) {
            $contactFields[] = $header;
        } else {
            $otherFields[] = $header;
        }
    }
    
    echo "资产相关字段 (" . count($assetFields) . " 个):\n";
    foreach ($assetFields as $field) {
        echo "  - {$field}\n";
    }
    
    echo "\n主体相关字段 (" . count($entityFields) . " 个):\n";
    foreach ($entityFields as $field) {
        echo "  - {$field}\n";
    }
    
    echo "\n联系人相关字段 (" . count($contactFields) . " 个):\n";
    foreach ($contactFields as $field) {
        echo "  - {$field}\n";
    }
    
    if (!empty($otherFields)) {
        echo "\n其他字段 (" . count($otherFields) . " 个):\n";
        foreach ($otherFields as $field) {
            echo "  - {$field}\n";
        }
    }
    
    // 生成字段映射建议
    echo "\n4. 字段映射建议:\n";
    echo "建议的字段映射关系:\n";
    
    $fieldMapping = [];
    foreach ($headers as $header) {
        $mapping = null;
        
        // 资产字段映射
        if (strpos($header, '资产名称') !== false || strpos($header, '设备名称') !== false) {
            $mapping = 'name (资产名称)';
        } elseif (strpos($header, '品牌') !== false) {
            $mapping = 'brand (资产品牌)';
        } elseif (strpos($header, '型号') !== false || strpos($header, '规格') !== false) {
            $mapping = 'model (规格型号)';
        } elseif (strpos($header, '序列号') !== false) {
            $mapping = 'serial_number (序列号)';
        } elseif (strpos($header, '分类') !== false) {
            $mapping = 'asset_category_ids (资产分类)';
        } elseif (strpos($header, '来源') !== false) {
            $mapping = 'asset_source (资产来源)';
        } elseif (strpos($header, '状态') !== false) {
            $mapping = 'asset_status (资产状态)';
        } elseif (strpos($header, '成色') !== false) {
            $mapping = 'asset_condition (成色)';
        } elseif (strpos($header, '区县') !== false) {
            $mapping = 'region_code (区县代码)';
        } elseif (strpos($header, '地址') !== false) {
            $mapping = 'detailed_address (详细地址)';
        } elseif (strpos($header, '启用') !== false) {
            $mapping = 'start_date (启用日期)';
        } elseif (strpos($header, '质保期') !== false && strpos($header, '月') !== false) {
            $mapping = 'warranty_period (合同质保期)';
        } elseif (strpos($header, '预警') !== false) {
            $mapping = 'warranty_alert (质保期预警)';
        } elseif (strpos($header, '维护') !== false) {
            $mapping = 'maintenance_cycle (维护周期)';
        } elseif (strpos($header, '使用年限') !== false) {
            $mapping = 'expected_years (预计使用年限)';
        }
        // 主体字段映射
        elseif (strpos($header, '主体名称') !== false || strpos($header, '公司名称') !== false) {
            $mapping = 'entity.name (主体名称)';
        } elseif (strpos($header, '税号') !== false) {
            $mapping = 'entity.tax_number (税号)';
        } elseif (strpos($header, '主体类型') !== false) {
            $mapping = 'entity.entity_type (主体类型)';
        } elseif (strpos($header, '主体地址') !== false) {
            $mapping = 'entity.address (主体地址)';
        } elseif (strpos($header, '主体电话') !== false) {
            $mapping = 'entity.phone (主体电话)';
        }
        // 联系人字段映射
        elseif (strpos($header, '联系人') !== false && strpos($header, '姓名') !== false) {
            $mapping = 'contact.name (联系人姓名)';
        } elseif (strpos($header, '联系人') !== false && strpos($header, '电话') !== false) {
            $mapping = 'contact.phone (联系人电话)';
        } elseif (strpos($header, '职位') !== false) {
            $mapping = 'contact.position (职位)';
        } elseif (strpos($header, '部门') !== false) {
            $mapping = 'contact.department (部门)';
        } elseif (strpos($header, '备注') !== false) {
            $mapping = 'remark (备注)';
        }
        
        if ($mapping) {
            $fieldMapping[$header] = $mapping;
            echo "  '{$header}' => {$mapping}\n";
        } else {
            echo "  '{$header}' => 需要手动映射\n";
        }
    }
    
    echo "\n=== 分析完成 ===\n";
    echo "总字段数: " . count($headers) . "\n";
    echo "已映射字段: " . count($fieldMapping) . "\n";
    echo "需要调整的字段: " . (count($headers) - count($fieldMapping)) . "\n";
    
} catch (Exception $e) {
    echo "✗ 分析失败：" . $e->getMessage() . "\n";
    echo "错误位置：" . $e->getFile() . ":" . $e->getLine() . "\n";
}

echo "\n=== 分析结束 ===\n";
