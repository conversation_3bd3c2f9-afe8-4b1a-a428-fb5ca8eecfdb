# 资产模板导出功能

## 功能概述

为资产管理模块添加了Excel模板导出功能，支持动态分类列，方便用户进行资产批量导入。

## 实现的功能

### 1. Excel模板导出
- **接口路径**: `GET /api/admin/assets/export-template`
- **功能**: 导出包含所有资产字段的Excel模板
- **认证**: 需要Bearer Token认证
- **文件格式**: .xlsx

### 2. 动态分类列
- **功能**: 根据系统中的分类数据动态生成分类列
- **层级显示**: 使用空格缩进表示分类层级关系
- **多选支持**: 支持同时选择多个分类
- **自动更新**: 分类数据变更后，导出的模板会自动更新

### 3. 完整的字段覆盖
模板包含以下字段：
- **基础信息**: 资产名称、品牌、规格型号、序列号
- **字典字段**: 资产来源、资产状态、成色
- **关系字段**: 主设备
- **地址信息**: 所在地区、详细地址
- **时间字段**: 启用日期
- **数值字段**: 合同质保期、质保期预警、维护周期、预计使用年限
- **相关主体**: 生产厂商、供应商、服务商、售后部的详细信息
- **备注**: 资产备注
- **动态分类**: 根据系统分类数据生成的分类列

## 技术实现

### 1. 依赖包
- `maatwebsite/excel`: Laravel Excel导出包

### 2. 核心文件
- `app/Exports/AssetTemplateExport.php`: 导出类
- `app/Http/Controllers/Admin/AssetController.php`: 控制器
- `routes/api.php`: 路由配置

### 3. 主要特性
- **动态列生成**: 根据数据库中的分类数据动态生成Excel列
- **样式美化**: 包含标题、表头样式、边框、颜色等
- **示例数据**: 提供两行示例数据，展示不同分类选择
- **层级显示**: 分类层级通过空格缩进清晰显示

## 使用方法

### 1. API调用
```bash
curl -X GET "http://your-domain.com/api/admin/assets/export-template" \
  -H "Authorization: Bearer your-token-here" \
  -H "Accept: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" \
  --output "资产导入模板.xlsx"
```

### 2. 前端集成
```javascript
fetch('/api/admin/assets/export-template', {
  method: 'GET',
  headers: {
    'Authorization': 'Bearer ' + token,
    'Accept': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
  }
})
.then(response => response.blob())
.then(blob => {
  const url = window.URL.createObjectURL(blob);
  const a = document.createElement('a');
  a.href = url;
  a.download = '资产导入模板.xlsx';
  a.click();
  window.URL.revokeObjectURL(url);
});
```

## 分类列说明

### 1. 分类显示格式
- 顶级分类: `医疗分类`
- 二级分类: `  科室` (前面2个空格)
- 三级分类: `    护理` (前面4个空格)

### 2. 分类选择方式
- 选中: 填写 `✓` 或 `是` 或 `1`
- 未选中: 留空或填写 `否` 或 `0`

### 3. 多选支持
- 可以同时选择多个分类
- 每个分类独立一列
- 支持跨层级选择

## 文件结构

```
apps/backend/
├── app/
│   ├── Exports/
│   │   └── AssetTemplateExport.php    # 导出类
│   └── Http/Controllers/Admin/
│       └── AssetController.php        # 控制器
├── routes/
│   └── api.php                        # 路由配置
├── API_ASSET_EXPORT.md                # API文档
└── README_ASSET_EXPORT.md             # 功能说明
```

## 注意事项

1. **分类数据**: 分类列会根据系统中的分类数据动态生成
2. **权限控制**: 需要管理员权限才能访问导出接口
3. **文件大小**: 生成的Excel文件大小约8KB
4. **兼容性**: 支持Excel 2007及以上版本
5. **编码**: 使用UTF-8编码，支持中文显示

## 扩展功能

### 1. 批量导入
可以基于此模板开发批量导入功能，解析Excel文件并创建资产记录。

### 2. 模板定制
可以根据不同用户角色或业务需求，生成不同的模板格式。

### 3. 数据验证
可以在导入时添加数据验证，确保导入数据的正确性。

## 测试验证

功能已通过以下测试：
- ✅ 导出文件生成
- ✅ 动态分类列显示
- ✅ 样式美化效果
- ✅ 示例数据展示
- ✅ 多分类选择
- ✅ 层级关系显示 
