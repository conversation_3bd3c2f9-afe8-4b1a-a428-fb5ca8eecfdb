<?php

namespace App\Services;

use App\Models\Asset;
use App\Models\AssetImportTask;
use App\Models\Entity;
use App\Models\EntityContact;
use App\Models\Region;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Maatwebsite\Excel\Facades\Excel;
use Maatwebsite\Excel\HeadingRow;
use Maatwebsite\Excel\Imports\HeadingRowFormatter;

class AssetImportService
{
    protected array $errors = [];
    protected int $totalRows = 0;
    protected int $successRows = 0;
    protected int $failedRows = 0;

    public function __construct()
    {
        // 设置标题行格式化器为不格式化，保持原样
        HeadingRowFormatter::default('none');
    }

    /**
     * 处理导入任务
     */
    public function processImport(AssetImportTask $importTask): array
    {
        $this->resetCounters();

        try {
            // 检查文件是否存在
            if (!Storage::exists($importTask->file_path)) {
                throw new \Exception("导入文件不存在: {$importTask->file_path}");
            }

            // 读取Excel文件
            $filePath = Storage::path($importTask->file_path);
            $data = Excel::toArray(new AssetImportReader(), $filePath);

            if (empty($data) || empty($data[0])) {
                throw new \Exception('Excel文件为空或格式不正确');
            }

            $rows = $data[0];
            $this->totalRows = count($rows) - 1; // 减去标题行

            if ($this->totalRows <= 0) {
                throw new \Exception('Excel文件中没有数据行');
            }

            // 获取标题行
            $headers = array_shift($rows);
            $this->validateHeaders($headers);

            // 处理数据行
            $this->processRows($rows, $headers, $importTask);

            // 更新任务进度
            $importTask->updateProgress($this->totalRows, $this->successRows, $this->failedRows);

            return [
                'total_rows' => $this->totalRows,
                'success_rows' => $this->successRows,
                'failed_rows' => $this->failedRows,
                'errors' => $this->errors,
                'summary' => $this->generateSummary(),
            ];
        } catch (\Exception $e) {
            Log::error('资产导入处理失败', [
                'task_id' => $importTask->id,
                'error' => $e->getMessage(),
            ]);
            throw $e;
        }
    }

    /**
     * 重置计数器
     */
    protected function resetCounters(): void
    {
        $this->errors = [];
        $this->totalRows = 0;
        $this->successRows = 0;
        $this->failedRows = 0;
    }

    /**
     * 验证Excel标题行
     */
    protected function validateHeaders(array $headers): void
    {
        $requiredHeaders = [
            '资产名称', '资产品牌', '规格型号', '序列号', '资产分类',
            '资产来源', '资产状态', '成色', '区县代码', '详细地址',
            '启用日期', '合同质保期(月)', '质保期预警(天)', '维护周期(天)', '预计使用年限(年)',
            '主体名称', '税号', '主体类型', '主体地址', '主体电话',
            '联系人姓名', '联系人电话', '职位', '部门', '备注'
        ];

        $missingHeaders = array_diff($requiredHeaders, $headers);
        if (!empty($missingHeaders)) {
            throw new \Exception('Excel文件缺少必要的列: ' . implode(', ', $missingHeaders));
        }
    }

    /**
     * 处理数据行
     */
    protected function processRows(array $rows, array $headers, AssetImportTask $importTask): void
    {
        foreach ($rows as $index => $row) {
            $rowNumber = $index + 2; // Excel行号（从2开始，因为第1行是标题）

            try {
                DB::beginTransaction();

                $rowData = array_combine($headers, $row);
                $this->processRow($rowData, $rowNumber);

                DB::commit();
                $this->successRows++;
            } catch (\Exception $e) {
                DB::rollBack();
                $this->failedRows++;
                $this->errors[] = [
                    'row' => $rowNumber,
                    'error' => $e->getMessage(),
                    'data' => $row,
                ];

                Log::warning('资产导入行处理失败', [
                    'task_id' => $importTask->id,
                    'row' => $rowNumber,
                    'error' => $e->getMessage(),
                ]);
            }
        }
    }

    /**
     * 处理单行数据
     */
    protected function processRow(array $rowData, int $rowNumber): void
    {
        // 验证必填字段
        if (empty($rowData['资产名称'])) {
            throw new \Exception('资产名称不能为空');
        }

        // 处理主体信息
        $entity = null;
        if (!empty($rowData['主体名称'])) {
            $entity = $this->processEntity($rowData);
        }

        // 处理资产信息
        $asset = $this->processAsset($rowData, $entity);

        // 处理联系人信息
        if ($entity && !empty($rowData['联系人姓名'])) {
            $this->processEntityContact($rowData, $entity);
        }
    }

    /**
     * 处理主体信息
     */
    protected function processEntity(array $rowData): Entity
    {
        $entityData = [
            'name' => $rowData['主体名称'],
            'tax_number' => $rowData['税号'] ?? null,
            'entity_type' => $rowData['主体类型'] ?? 'company',
            'address' => $rowData['主体地址'] ?? null,
            'phone' => $rowData['主体电话'] ?? null,
            'created_by' => auth()->id(),
            'updated_by' => auth()->id(),
        ];

        // 根据税号查找现有主体
        if (!empty($entityData['tax_number'])) {
            $entity = Entity::where('tax_number', $entityData['tax_number'])->first();
            if ($entity) {
                $entity->update($entityData);
                return $entity;
            }
        }

        // 根据名称查找现有主体
        $entity = Entity::where('name', $entityData['name'])->first();
        if ($entity) {
            $entity->update($entityData);
            return $entity;
        }

        // 创建新主体
        return Entity::create($entityData);
    }

    /**
     * 处理资产信息
     */
    protected function processAsset(array $rowData, ?Entity $entity): Asset
    {
        $assetData = [
            'name' => $rowData['资产名称'],
            'brand' => $rowData['资产品牌'] ?? null,
            'model' => $rowData['规格型号'] ?? null,
            'serial_number' => $rowData['序列号'] ?? null,
            'asset_source' => $rowData['资产来源'] ?? null,
            'asset_status' => $rowData['资产状态'] ?? null,
            'asset_condition' => $rowData['成色'] ?? null,
            'region_code' => $rowData['区县代码'] ?? null,
            'detailed_address' => $rowData['详细地址'] ?? null,
            'start_date' => $this->parseDate($rowData['启用日期'] ?? null),
            'warranty_period' => $this->parseInteger($rowData['合同质保期(月)'] ?? null),
            'warranty_alert' => $this->parseInteger($rowData['质保期预警(天)'] ?? null),
            'maintenance_cycle' => $this->parseInteger($rowData['维护周期(天)'] ?? null),
            'expected_years' => $this->parseInteger($rowData['预计使用年限(年)'] ?? null),
            'remark' => $rowData['备注'] ?? null,
            'created_by' => auth()->id(),
            'updated_by' => auth()->id(),
        ];

        // 处理资产分类
        if (!empty($rowData['资产分类'])) {
            $assetData['asset_category_ids'] = $this->parseAssetCategories($rowData['资产分类']);
        }

        // 处理相关主体信息
        if ($entity) {
            $assetData['related_entities'] = [
                [
                    'entity_id' => $entity->id,
                    'entity_name' => $entity->name,
                    'relation_type' => 'owner', // 默认为所有者关系
                ]
            ];
        }

        return Asset::create($assetData);
    }

    /**
     * 处理主体联系人信息
     */
    protected function processEntityContact(array $rowData, Entity $entity): EntityContact
    {
        $contactData = [
            'entity_id' => $entity->id,
            'name' => $rowData['联系人姓名'],
            'phone' => $rowData['联系人电话'] ?? '',
            'position' => $rowData['职位'] ?? null,
            'department' => $rowData['部门'] ?? null,
            'created_by' => auth()->id(),
            'updated_by' => auth()->id(),
        ];

        // 检查是否已存在相同的联系人
        $existingContact = EntityContact::where('entity_id', $entity->id)
            ->where('name', $contactData['name'])
            ->where('phone', $contactData['phone'])
            ->first();

        if ($existingContact) {
            $existingContact->update($contactData);
            return $existingContact;
        }

        return EntityContact::create($contactData);
    }

    /**
     * 解析日期
     */
    protected function parseDate(?string $date): ?int
    {
        if (empty($date)) {
            return null;
        }

        try {
            return strtotime($date);
        } catch (\Exception $e) {
            return null;
        }
    }

    /**
     * 解析整数
     */
    protected function parseInteger(?string $value): ?int
    {
        if (empty($value)) {
            return null;
        }

        return is_numeric($value) ? (int) $value : null;
    }

    /**
     * 解析资产分类
     */
    protected function parseAssetCategories(string $categories): array
    {
        // 这里可以根据实际需求解析分类字符串
        // 暂时返回空数组，需要根据具体的分类数据结构来实现
        return [];
    }

    /**
     * 生成导入摘要
     */
    protected function generateSummary(): string
    {
        return sprintf(
            '导入完成：总计 %d 行，成功 %d 行，失败 %d 行',
            $this->totalRows,
            $this->successRows,
            $this->failedRows
        );
    }
}

/**
 * Excel导入读取器
 */
class AssetImportReader
{
    // 这个类用于Excel::toArray()方法
}
