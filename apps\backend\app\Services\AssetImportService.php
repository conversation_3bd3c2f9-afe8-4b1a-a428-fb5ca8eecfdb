<?php

namespace App\Services;

use App\Models\Asset;
use App\Models\AssetImportTask;
use App\Models\Entity;
use App\Models\EntityContact;
use App\Models\Region;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Maatwebsite\Excel\Facades\Excel;
use Maatwebsite\Excel\HeadingRow;
use Maatwebsite\Excel\Imports\HeadingRowFormatter;

class AssetImportService
{
    protected array $errors = [];
    protected int $totalRows = 0;
    protected int $successRows = 0;
    protected int $failedRows = 0;

    // 批量处理大小
    protected int $batchSize = 1000;

    // 批量数据缓存
    protected array $assetBatch = [];
    protected array $entityBatch = [];
    protected array $contactBatch = [];

    // 已处理的实体缓存（避免重复创建）
    protected array $processedEntities = [];

    public function __construct()
    {
        // 设置标题行格式化器为不格式化，保持原样
        HeadingRowFormatter::default('none');
    }

    /**
     * 处理导入任务
     */
    public function processImport(AssetImportTask $importTask): array
    {
        $this->resetCounters();

        try {
            // 检查文件是否存在 - 支持多种存储方式
            $filePath = $this->getActualFilePath($importTask->file_path);
            if (!file_exists($filePath)) {
                throw new \Exception("导入文件不存在: {$importTask->file_path}");
            }

            // 读取Excel文件
            $data = Excel::toArray(new AssetImportReader(), $filePath);

            if (empty($data) || empty($data[0])) {
                throw new \Exception('Excel文件为空或格式不正确');
            }

            $rows = $data[0];
            $this->totalRows = count($rows) - 1; // 减去标题行

            if ($this->totalRows <= 0) {
                throw new \Exception('Excel文件中没有数据行');
            }

            // 获取标题行
            $headers = array_shift($rows);
            $this->validateHeaders($headers);

            // 处理数据行
            $this->processRows($rows, $headers, $importTask);

            // 更新任务进度
            $importTask->updateProgress($this->totalRows, $this->successRows, $this->failedRows);

            return [
                'total_rows' => $this->totalRows,
                'success_rows' => $this->successRows,
                'failed_rows' => $this->failedRows,
                'errors' => $this->errors,
                'summary' => $this->generateSummary(),
            ];
        } catch (\Exception $e) {
            Log::error('资产导入处理失败', [
                'task_id' => $importTask->id,
                'error' => $e->getMessage(),
            ]);
            throw $e;
        }
    }



    /**
     * 验证Excel标题行
     */
    protected function validateHeaders(array $headers): void
    {
        $requiredHeaders = [
            '资产名称', '资产品牌', '规格型号', '序列号', '资产分类',
            '资产来源', '资产状态', '成色', '区县代码', '详细地址',
            '启用日期', '合同质保期(月)', '质保期预警(天)', '维护周期(天)', '预计使用年限(年)',
            '主体名称', '税号', '主体类型', '主体地址', '主体电话',
            '联系人姓名', '联系人电话', '职位', '部门', '备注'
        ];

        $missingHeaders = array_diff($requiredHeaders, $headers);
        if (!empty($missingHeaders)) {
            throw new \Exception('Excel文件缺少必要的列: ' . implode(', ', $missingHeaders));
        }
    }

    /**
     * 处理数据行 - 批量处理版本
     */
    protected function processRows(array $rows, array $headers, AssetImportTask $importTask): void
    {
        $currentBatch = [];
        $batchStartRow = 2; // Excel行号从2开始

        foreach ($rows as $index => $row) {
            $rowNumber = $index + 2;
            $rowData = array_combine($headers, $row);

            // 验证和清理数据
            try {
                $cleanedData = $this->validateAndCleanRowData($rowData, $rowNumber);
                $currentBatch[] = [
                    'row_number' => $rowNumber,
                    'data' => $cleanedData,
                ];

                // 当批次达到指定大小或是最后一行时，执行批量处理
                if (count($currentBatch) >= $this->batchSize || $index === count($rows) - 1) {
                    $this->processBatch($currentBatch, $importTask, $batchStartRow);

                    // 重置批次
                    $currentBatch = [];
                    $batchStartRow = $rowNumber + 1;
                }
            } catch (\Exception $e) {
                $this->failedRows++;
                $this->errors[] = [
                    'row' => $rowNumber,
                    'error' => $e->getMessage(),
                    'data' => $row,
                ];

                Log::warning('数据验证失败', [
                    'task_id' => $importTask->id,
                    'row' => $rowNumber,
                    'error' => $e->getMessage(),
                ]);
            }
        }
    }

    /**
     * 验证和清理行数据 - 防SQL注入
     */
    protected function validateAndCleanRowData(array $rowData, int $rowNumber): array
    {
        // 验证必填字段
        if (empty($rowData['资产名称'])) {
            throw new \Exception('资产名称不能为空');
        }

        // 清理和验证数据
        $cleanedData = [];

        foreach ($rowData as $key => $value) {
            // 防SQL注入：清理字符串数据
            if (is_string($value)) {
                $value = trim($value);
                // 移除潜在的SQL注入字符
                $value = preg_replace('/[\'";\\\\]/', '', $value);
                // 限制长度
                $value = mb_substr($value, 0, 500);
            }

            $cleanedData[$key] = $value;
        }

        // 验证特定字段格式
        $this->validateFieldFormats($cleanedData, $rowNumber);

        return $cleanedData;
    }

    /**
     * 验证字段格式
     */
    protected function validateFieldFormats(array $data, int $rowNumber): void
    {
        // 验证税号格式
        if (!empty($data['税号']) && !preg_match('/^[0-9A-Z]{15,20}$/', $data['税号'])) {
            throw new \Exception("第{$rowNumber}行：税号格式不正确");
        }

        // 验证电话格式
        if (!empty($data['主体电话']) && !preg_match('/^[0-9\-\s\+\(\)]{7,20}$/', $data['主体电话'])) {
            throw new \Exception("第{$rowNumber}行：主体电话格式不正确");
        }

        if (!empty($data['联系人电话']) && !preg_match('/^[0-9\-\s\+\(\)]{7,20}$/', $data['联系人电话'])) {
            throw new \Exception("第{$rowNumber}行：联系人电话格式不正确");
        }

        // 验证数值字段
        $numericFields = ['合同质保期(月)', '质保期预警(天)', '维护周期(天)', '预计使用年限(年)'];
        foreach ($numericFields as $field) {
            if (!empty($data[$field]) && !is_numeric($data[$field])) {
                throw new \Exception("第{$rowNumber}行：{$field}必须是数字");
            }
        }
    }

    /**
     * 批量处理数据
     */
    protected function processBatch(array $batch, AssetImportTask $importTask, int $batchStartRow): void
    {
        try {
            DB::beginTransaction();

            Log::info("开始处理批次", [
                'task_id' => $importTask->id,
                'batch_size' => count($batch),
                'start_row' => $batchStartRow,
            ]);

            // 分别收集不同类型的数据
            $entities = [];
            $assets = [];
            $contacts = [];

            foreach ($batch as $item) {
                $rowData = $item['data'];
                $rowNumber = $item['row_number'];

                try {
                    // 准备主体数据
                    if (!empty($rowData['主体名称'])) {
                        $entityKey = $this->getEntityKey($rowData);
                        if (!isset($entities[$entityKey])) {
                            $entities[$entityKey] = $this->prepareEntityData($rowData);
                        }
                    }

                    // 准备资产数据
                    $assets[] = $this->prepareAssetData($rowData, $rowNumber);

                    // 准备联系人数据
                    if (!empty($rowData['主体名称']) && !empty($rowData['联系人姓名'])) {
                        $contacts[] = $this->prepareContactData($rowData, $rowNumber);
                    }

                } catch (\Exception $e) {
                    $this->failedRows++;
                    $this->errors[] = [
                        'row' => $rowNumber,
                        'error' => $e->getMessage(),
                        'data' => $rowData,
                    ];
                }
            }

            // 批量插入数据
            $this->batchInsertEntities($entities);
            $this->batchInsertAssets($assets);
            $this->batchInsertContacts($contacts);

            $this->successRows += count($batch) - count($this->errors);

            DB::commit();

            Log::info("批次处理完成", [
                'task_id' => $importTask->id,
                'processed' => count($batch),
                'success' => count($batch) - count($this->errors),
            ]);

        } catch (\Exception $e) {
            DB::rollBack();

            // 将整个批次标记为失败
            foreach ($batch as $item) {
                $this->failedRows++;
                $this->errors[] = [
                    'row' => $item['row_number'],
                    'error' => '批量处理失败: ' . $e->getMessage(),
                    'data' => $item['data'],
                ];
            }

            Log::error("批次处理失败", [
                'task_id' => $importTask->id,
                'error' => $e->getMessage(),
                'batch_start' => $batchStartRow,
            ]);
        }
    }

    /**
     * 获取主体唯一键
     */
    protected function getEntityKey(array $rowData): string
    {
        $taxNumber = $rowData['税号'] ?? '';
        $name = $rowData['主体名称'] ?? '';

        // 优先使用税号作为唯一键，否则使用名称
        return !empty($taxNumber) ? 'tax_' . $taxNumber : 'name_' . $name;
    }

    /**
     * 准备主体数据
     */
    protected function prepareEntityData(array $rowData): array
    {
        return [
            'name' => $rowData['主体名称'],
            'tax_number' => $rowData['税号'] ?? null,
            'entity_type' => $rowData['主体类型'] ?? 'company',
            'address' => $rowData['主体地址'] ?? null,
            'phone' => $rowData['主体电话'] ?? null,
            'created_by' => auth()->id(),
            'updated_by' => auth()->id(),
            'created_at' => time(),
            'updated_at' => time(),
        ];
    }

    /**
     * 准备资产数据
     */
    protected function prepareAssetData(array $rowData, int $rowNumber): array
    {
        $assetCategories = $this->parseAssetCategories($rowData['资产分类'] ?? '');

        return [
            'name' => $rowData['资产名称'],
            'brand' => $rowData['资产品牌'] ?? null,
            'model' => $rowData['规格型号'] ?? null,
            'serial_number' => $rowData['序列号'] ?? null,
            'asset_source' => $rowData['资产来源'] ?? null,
            'asset_status' => $rowData['资产状态'] ?? null,
            'asset_condition' => $rowData['成色'] ?? null,
            'region_code' => $rowData['区县代码'] ?? null,
            'detailed_address' => $rowData['详细地址'] ?? null,
            'start_date' => $this->parseDate($rowData['启用日期'] ?? null),
            'warranty_period' => $this->parseInteger($rowData['合同质保期(月)'] ?? null),
            'warranty_alert' => $this->parseInteger($rowData['质保期预警(天)'] ?? null),
            'maintenance_cycle' => $this->parseInteger($rowData['维护周期(天)'] ?? null),
            'expected_years' => $this->parseInteger($rowData['预计使用年限(年)'] ?? null),
            'asset_category_ids' => json_encode($assetCategories), // 转换为JSON字符串
            'remark' => $rowData['备注'] ?? null,
            'created_by' => auth()->id(),
            'updated_by' => auth()->id(),
            'created_at' => time(),
            'updated_at' => time(),
            '_row_number' => $rowNumber, // 用于错误追踪
        ];
    }

    /**
     * 准备联系人数据
     */
    protected function prepareContactData(array $rowData, int $rowNumber): array
    {
        return [
            'entity_key' => $this->getEntityKey($rowData), // 用于关联主体
            'name' => $rowData['联系人姓名'],
            'phone' => $rowData['联系人电话'] ?? '',
            'position' => $rowData['职位'] ?? null,
            'department' => $rowData['部门'] ?? null,
            'created_by' => auth()->id(),
            'updated_by' => auth()->id(),
            'created_at' => time(),
            'updated_at' => time(),
            '_row_number' => $rowNumber, // 用于错误追踪
        ];
    }

    /**
     * 批量插入主体数据
     */
    protected function batchInsertEntities(array $entities): void
    {
        if (empty($entities)) {
            return;
        }

        foreach ($entities as $entityKey => $entityData) {
            // 检查是否已存在
            $existingEntity = null;

            if (!empty($entityData['tax_number'])) {
                $existingEntity = Entity::where('tax_number', $entityData['tax_number'])->first();
            }

            if (!$existingEntity) {
                $existingEntity = Entity::where('name', $entityData['name'])->first();
            }

            if ($existingEntity) {
                // 更新现有记录
                $existingEntity->update($entityData);
                $this->processedEntities[$entityKey] = $existingEntity->id;
            } else {
                // 创建新记录
                $newEntity = Entity::create($entityData);
                $this->processedEntities[$entityKey] = $newEntity->id;
            }
        }
    }

    /**
     * 批量插入资产数据
     */
    protected function batchInsertAssets(array $assets): void
    {
        if (empty($assets)) {
            return;
        }

        $insertData = [];

        foreach ($assets as $assetData) {
            $rowNumber = $assetData['_row_number'];
            unset($assetData['_row_number']);

            // 处理相关主体信息
            $relatedEntities = [];
            if (!empty($this->processedEntities)) {
                // 这里需要根据行数据找到对应的主体
                // 简化处理：使用第一个处理的主体
                $entityId = reset($this->processedEntities);

                // 安全地获取主体名称
                try {
                    $entity = Entity::find($entityId);
                    $entityName = $entity ? $entity->name : '';

                    if ($entityName) {
                        $relatedEntities = [
                            [
                                'entity_id' => $entityId,
                                'entity_name' => $entityName,
                                'relation_type' => 'owner',
                            ]
                        ];
                    }
                } catch (\Exception $e) {
                    Log::warning('获取主体信息失败', [
                        'entity_id' => $entityId,
                        'error' => $e->getMessage(),
                    ]);
                }
            }

            $assetData['related_entities'] = json_encode($relatedEntities);
            $insertData[] = $assetData;
        }

        // 使用批量插入
        if (!empty($insertData)) {
            Asset::insert($insertData);
        }
    }

    /**
     * 批量插入联系人数据
     */
    protected function batchInsertContacts(array $contacts): void
    {
        if (empty($contacts)) {
            return;
        }

        $insertData = [];

        foreach ($contacts as $contactData) {
            $entityKey = $contactData['entity_key'];
            $rowNumber = $contactData['_row_number'];

            unset($contactData['entity_key'], $contactData['_row_number']);

            // 获取对应的主体ID
            if (isset($this->processedEntities[$entityKey])) {
                $contactData['entity_id'] = $this->processedEntities[$entityKey];

                // 检查是否已存在相同联系人
                $existingContact = EntityContact::where('entity_id', $contactData['entity_id'])
                    ->where('name', $contactData['name'])
                    ->where('phone', $contactData['phone'])
                    ->first();

                if (!$existingContact) {
                    $insertData[] = $contactData;
                }
            }
        }

        // 使用批量插入
        if (!empty($insertData)) {
            EntityContact::insert($insertData);
        }
    }

    /**
     * 获取实际文件路径
     */
    protected function getActualFilePath(string $filePath): string
    {
        // 如果是绝对路径且文件存在，直接返回
        if (file_exists($filePath)) {
            return $filePath;
        }

        // 处理附件系统的相对路径
        // 附件路径格式：attachments/YYYY/mm/dd/filename.ext
        $possiblePaths = [
            // Laravel public disk 路径 (storage/app/public/)
            storage_path('app/public/' . $filePath),
            // Laravel local disk 路径 (storage/app/)
            storage_path('app/' . $filePath),
            // 公共存储路径 (public/storage/)
            public_path('storage/' . $filePath),
            // 直接在public目录
            public_path($filePath),
        ];

        foreach ($possiblePaths as $path) {
            if (file_exists($path)) {
                return $path;
            }
        }

        // 如果都不存在，记录日志并返回原路径
        Log::warning('导入文件路径查找失败', [
            'original_path' => $filePath,
            'tried_paths' => $possiblePaths,
        ]);

        return $filePath;
    }

    /**
     * 重置计数器和缓存
     */
    protected function resetCounters(): void
    {
        $this->errors = [];
        $this->totalRows = 0;
        $this->successRows = 0;
        $this->failedRows = 0;
        $this->assetBatch = [];
        $this->entityBatch = [];
        $this->contactBatch = [];
        $this->processedEntities = [];
    }

    /**
     * 解析日期
     */
    protected function parseDate(?string $date): ?int
    {
        if (empty($date)) {
            return null;
        }

        try {
            return strtotime($date);
        } catch (\Exception $e) {
            return null;
        }
    }

    /**
     * 解析整数
     */
    protected function parseInteger(?string $value): ?int
    {
        if (empty($value)) {
            return null;
        }

        return is_numeric($value) ? (int) $value : null;
    }

    /**
     * 解析资产分类
     */
    protected function parseAssetCategories(string $categories): array
    {
        if (empty($categories)) {
            return [];
        }

        // 支持多种分隔符：逗号、分号、竖线
        $categoryNames = preg_split('/[,;|]/', $categories);
        $categoryIds = [];

        foreach ($categoryNames as $categoryName) {
            $categoryName = trim($categoryName);
            if (empty($categoryName)) {
                continue;
            }

            // 防SQL注入：清理分类名称
            $categoryName = preg_replace('/[\'";\\\\]/', '', $categoryName);
            $categoryName = mb_substr($categoryName, 0, 100);

            // 这里可以根据实际需求查找分类ID
            // 暂时使用分类名称的哈希值作为ID（仅用于演示）
            $categoryIds[] = abs(crc32($categoryName)) % 1000000;
        }

        return array_unique($categoryIds);
    }

    /**
     * 生成导入摘要
     */
    protected function generateSummary(): string
    {
        return sprintf(
            '导入完成：总计 %d 行，成功 %d 行，失败 %d 行',
            $this->totalRows,
            $this->successRows,
            $this->failedRows
        );
    }
}

/**
 * Excel导入读取器
 */
class AssetImportReader
{
    // 这个类用于Excel::toArray()方法
}
