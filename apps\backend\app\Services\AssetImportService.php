<?php

namespace App\Services;

use App\Models\Asset;
use App\Models\AssetImportTask;
use App\Models\Entity;
use App\Models\EntityContact;
use App\Models\Region;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Maatwebsite\Excel\Facades\Excel;
use Maatwebsite\Excel\HeadingRow;
use Maatwebsite\Excel\Imports\HeadingRowFormatter;

class AssetImportService
{
    protected array $errors = [];
    protected int $totalRows = 0;
    protected int $successRows = 0;
    protected int $failedRows = 0;
    protected string $templateType = 'unknown_template';

    // 批量处理大小
    protected int $batchSize = 1000;

    // 批量数据缓存
    protected array $assetBatch = [];
    protected array $entityBatch = [];
    protected array $contactBatch = [];

    // 已处理的实体缓存（避免重复创建）
    protected array $processedEntities = [];

    public function __construct()
    {
        // 设置标题行格式化器为不格式化，保持原样
        HeadingRowFormatter::default('none');
    }

    /**
     * 处理导入任务
     */
    public function processImport(AssetImportTask $importTask): array
    {
        $this->resetCounters();

        try {
            // 检查文件是否存在 - 支持多种存储方式
            $filePath = $this->getActualFilePath($importTask->file_path);
            if (!file_exists($filePath)) {
                throw new \Exception("导入文件不存在: {$importTask->file_path}");
            }

            // 读取Excel文件
            $data = Excel::toArray(new AssetImportReader(), $filePath);

            if (empty($data) || empty($data[0])) {
                throw new \Exception('Excel文件为空或格式不正确');
            }

            $rows = $data[0];

            // 找到标题行（包含"资产名称"的行）
            $headerRowIndex = -1;
            $headers = [];

            for ($i = 0; $i < count($rows); $i++) {
                if (in_array('资产名称', $rows[$i])) {
                    $headerRowIndex = $i;
                    $headers = $rows[$i];
                    break;
                }
            }

            if ($headerRowIndex === -1) {
                throw new \Exception('Excel文件中未找到标题行（包含"资产名称"的行）');
            }

            // 移除标题行之前的所有行，只保留数据行
            $dataRows = array_slice($rows, $headerRowIndex + 1);
            $this->totalRows = count($dataRows);

            if ($this->totalRows <= 0) {
                throw new \Exception('Excel文件中没有数据行');
            }

            $this->validateHeaders($headers);

            // 处理数据行
            $this->processRows($dataRows, $headers, $importTask);

            // 更新任务进度
            $importTask->updateProgress($this->totalRows, $this->successRows, $this->failedRows);

            return [
                'total_rows' => $this->totalRows,
                'success_rows' => $this->successRows,
                'failed_rows' => $this->failedRows,
                'errors' => $this->errors,
                'summary' => $this->generateSummary(),
            ];
        } catch (\Exception $e) {
            Log::error('资产导入处理失败', [
                'task_id' => $importTask->id,
                'error' => $e->getMessage(),
            ]);
            throw $e;
        }
    }



    /**
     * 验证Excel标题行
     */
    protected function validateHeaders(array $headers): void
    {
        // 必填字段（只检查关键字段）
        $requiredHeaders = [
            '资产名称', // 必填字段
        ];

        $missingHeaders = array_diff($requiredHeaders, $headers);
        if (!empty($missingHeaders)) {
            throw new \Exception('Excel文件缺少必要的列: ' . implode(', ', $missingHeaders));
        }

        // 自动检测模板类型
        $this->templateType = $this->detectTemplateType($headers);

        Log::info('检测到模板类型', [
            'template_type' => $this->templateType,
            'headers_count' => count($headers),
            'sample_headers' => array_slice($headers, 0, 10),
        ]);
    }

    /**
     * 检测模板类型
     */
    protected function detectTemplateType(array $headers): string
    {
        // 新模板特征字段
        $newTemplateHeaders = [
            '生产厂商名称', '供应商名称', '服务商名称', '售后部名称',
            '医疗分类', '科室', '行业分类'
        ];

        // 旧模板特征字段
        $oldTemplateHeaders = [
            '主体名称', '税号', '主体类型', '主体地址', '主体电话',
            '联系人姓名', '联系人电话', '职位', '部门'
        ];

        $newMatches = count(array_intersect($newTemplateHeaders, $headers));
        $oldMatches = count(array_intersect($oldTemplateHeaders, $headers));

        if ($newMatches >= 3) {
            return 'new_template';
        } elseif ($oldMatches >= 3) {
            return 'old_template';
        } else {
            return 'unknown_template';
        }
    }

    /**
     * 处理数据行 - 批量处理版本
     */
    protected function processRows(array $rows, array $headers, AssetImportTask $importTask): void
    {
        $currentBatch = [];
        $batchStartRow = 2; // Excel行号从2开始

        foreach ($rows as $index => $row) {
            $rowNumber = $index + 2;
            $rowData = array_combine($headers, $row);

            // 验证和清理数据
            try {
                $cleanedData = $this->validateAndCleanRowData($rowData, $rowNumber);
                $currentBatch[] = [
                    'row_number' => $rowNumber,
                    'data' => $cleanedData,
                ];

                // 当批次达到指定大小或是最后一行时，执行批量处理
                if (count($currentBatch) >= $this->batchSize || $index === count($rows) - 1) {
                    $this->processBatch($currentBatch, $importTask, $batchStartRow);

                    // 重置批次
                    $currentBatch = [];
                    $batchStartRow = $rowNumber + 1;
                }
            } catch (\Exception $e) {
                $this->failedRows++;
                $this->errors[] = [
                    'row' => $rowNumber,
                    'error' => $e->getMessage(),
                    'data' => $row,
                ];

                Log::warning('数据验证失败', [
                    'task_id' => $importTask->id,
                    'row' => $rowNumber,
                    'error' => $e->getMessage(),
                ]);
            }
        }
    }

    /**
     * 验证和清理行数据 - 防SQL注入
     */
    protected function validateAndCleanRowData(array $rowData, int $rowNumber): array
    {
        // 验证必填字段
        if (empty($rowData['资产名称'])) {
            throw new \Exception('资产名称不能为空');
        }

        // 清理和验证数据
        $cleanedData = [];

        foreach ($rowData as $key => $value) {
            // 防SQL注入：清理字符串数据
            if (is_string($value)) {
                $value = trim($value);
                // 移除潜在的SQL注入字符
                $value = preg_replace('/[\'";\\\\]/', '', $value);
                // 限制长度
                $value = mb_substr($value, 0, 500);
            }

            $cleanedData[$key] = $value;
        }

        // 验证特定字段格式
        $this->validateFieldFormats($cleanedData, $rowNumber);

        return $cleanedData;
    }

    /**
     * 验证字段格式
     */
    protected function validateFieldFormats(array $data, int $rowNumber): void
    {
        // 验证电话格式（新模板中的各种联系电话）
        $phoneFields = [
            '生产厂商联系电话', '供应商联系电话', '服务商联系电话', '售后部联系电话'
        ];

        foreach ($phoneFields as $field) {
            if (!empty($data[$field]) && !preg_match('/^[0-9\-\s\+\(\)]{7,20}$/', $data[$field])) {
                throw new \Exception("第{$rowNumber}行：{$field}格式不正确");
            }
        }

        // 验证数值字段
        $numericFields = ['合同质保期(月)', '质保期预警(天)', '维护周期(天)', '预计使用年限(年)'];
        foreach ($numericFields as $field) {
            if (!empty($data[$field]) && !is_numeric($data[$field])) {
                throw new \Exception("第{$rowNumber}行：{$field}必须是数字");
            }
        }

        // 验证日期格式
        if (!empty($data['启用日期'])) {
            $date = $data['启用日期'];
            if (!preg_match('/^\d{4}-\d{2}-\d{2}$/', $date) && !is_numeric($date)) {
                throw new \Exception("第{$rowNumber}行：启用日期格式不正确，应为YYYY-MM-DD格式");
            }
        }
    }

    /**
     * 批量处理数据
     */
    protected function processBatch(array $batch, AssetImportTask $importTask, int $batchStartRow): void
    {
        try {
            DB::beginTransaction();

            Log::info("开始处理批次", [
                'task_id' => $importTask->id,
                'batch_size' => count($batch),
                'start_row' => $batchStartRow,
            ]);

            // 分别收集不同类型的数据
            $entities = [];
            $assets = [];
            $contacts = [];

            foreach ($batch as $item) {
                $rowData = $item['data'];
                $rowNumber = $item['row_number'];

                try {
                    // 根据模板类型选择不同的处理方式
                    if ($this->templateType === 'new_template') {
                        // 新模板：准备多个主体数据（生产厂商、供应商、服务商、售后部）
                        $this->prepareMultipleEntities($rowData, $entities, $contacts, $rowNumber);
                    } else {
                        // 旧模板：准备单个主体数据
                        $this->prepareSingleEntity($rowData, $entities, $contacts, $rowNumber);
                    }

                    // 准备资产数据
                    $assets[] = $this->prepareAssetData($rowData, $rowNumber);

                } catch (\Exception $e) {
                    $this->failedRows++;
                    $this->errors[] = [
                        'row' => $rowNumber,
                        'error' => $e->getMessage(),
                        'data' => $rowData,
                    ];
                }
            }

            // 批量插入数据
            $this->batchInsertEntities($entities);
            $this->batchInsertAssets($assets);
            $this->batchInsertContacts($contacts);

            $this->successRows += count($batch) - count($this->errors);

            DB::commit();

            Log::info("批次处理完成", [
                'task_id' => $importTask->id,
                'processed' => count($batch),
                'success' => count($batch) - count($this->errors),
            ]);

        } catch (\Exception $e) {
            DB::rollBack();

            // 将整个批次标记为失败
            foreach ($batch as $item) {
                $this->failedRows++;
                $this->errors[] = [
                    'row' => $item['row_number'],
                    'error' => '批量处理失败: ' . $e->getMessage(),
                    'data' => $item['data'],
                ];
            }

            Log::error("批次处理失败", [
                'task_id' => $importTask->id,
                'error' => $e->getMessage(),
                'batch_start' => $batchStartRow,
            ]);
        }
    }

    /**
     * 准备单个主体数据（旧模板）
     */
    protected function prepareSingleEntity(array $rowData, array &$entities, array &$contacts, int $rowNumber): void
    {
        // 处理单个主体（旧模板格式）
        if (!empty($rowData['主体名称'])) {
            $entityKey = $this->getEntityKey($rowData);

            if (!isset($entities[$entityKey])) {
                $entities[$entityKey] = [
                    'name' => $rowData['主体名称'],
                    'tax_number' => $rowData['税号'] ?? null,
                    'entity_type' => $rowData['主体类型'] ?? '企业',
                    'address' => $rowData['主体地址'] ?? null,
                    'phone' => $rowData['主体电话'] ?? null,
                    'created_by' => auth()->id(),
                    'updated_by' => auth()->id(),
                    'created_at' => time(),
                    'updated_at' => time(),
                ];
            }

            // 准备联系人数据
            if (!empty($rowData['联系人姓名'])) {
                $contacts[] = [
                    'entity_key' => $entityKey,
                    'name' => $rowData['联系人姓名'],
                    'phone' => $rowData['联系人电话'] ?? null,
                    'position' => $rowData['职位'] ?? null,
                    'department' => $rowData['部门'] ?? null,
                    'created_by' => auth()->id(),
                    'updated_by' => auth()->id(),
                    'created_at' => time(),
                    'updated_at' => time(),
                    '_row_number' => $rowNumber,
                ];
            }
        }
    }

    /**
     * 准备多个主体数据（新模板）
     */
    protected function prepareMultipleEntities(array $rowData, array &$entities, array &$contacts, int $rowNumber): void
    {
        // 定义主体类型映射
        $entityTypes = [
            'manufacturer' => [
                'name_field' => '生产厂商名称',
                'contact_name_field' => '生产厂商联系人',
                'contact_phone_field' => '生产厂商联系电话',
                'contact_position_field' => '生产厂商职位',
                'type' => 'manufacturer'
            ],
            'supplier' => [
                'name_field' => '供应商名称',
                'contact_name_field' => '供应商联系人',
                'contact_phone_field' => '供应商联系电话',
                'contact_position_field' => '供应商职位',
                'type' => 'supplier'
            ],
            'service_provider' => [
                'name_field' => '服务商名称',
                'contact_name_field' => '服务商联系人',
                'contact_phone_field' => '服务商联系电话',
                'contact_position_field' => '服务商职位',
                'type' => 'service_provider'
            ],
            'after_sales' => [
                'name_field' => '售后部名称',
                'contact_name_field' => '售后部联系人',
                'contact_phone_field' => '售后部联系电话',
                'contact_position_field' => '售后部职位',
                'type' => 'after_sales'
            ]
        ];

        foreach ($entityTypes as $typeKey => $config) {
            $entityName = trim($rowData[$config['name_field']] ?? '');

            if (!empty($entityName)) {
                $entityKey = $typeKey . '_' . md5($entityName);

                if (!isset($entities[$entityKey])) {
                    $entities[$entityKey] = [
                        'name' => $entityName,
                        'entity_type' => $config['type'],
                        'created_by' => auth()->id(),
                        'updated_by' => auth()->id(),
                        'created_at' => time(),
                        'updated_at' => time(),
                    ];
                }

                // 准备联系人数据
                $contactName = trim($rowData[$config['contact_name_field']] ?? '');
                if (!empty($contactName)) {
                    $contacts[] = [
                        'entity_key' => $entityKey,
                        'name' => $contactName,
                        'phone' => trim($rowData[$config['contact_phone_field']] ?? ''),
                        'position' => trim($rowData[$config['contact_position_field']] ?? ''),
                        'created_by' => auth()->id(),
                        'updated_by' => auth()->id(),
                        'created_at' => time(),
                        'updated_at' => time(),
                        '_row_number' => $rowNumber,
                    ];
                }
            }
        }
    }

    /**
     * 获取主体唯一键
     */
    protected function getEntityKey(array $rowData): string
    {
        $taxNumber = $rowData['税号'] ?? '';
        $name = $rowData['主体名称'] ?? '';

        // 优先使用税号作为唯一键，否则使用名称
        return !empty($taxNumber) ? 'tax_' . $taxNumber : 'name_' . $name;
    }

    /**
     * 准备主体数据
     */
    protected function prepareEntityData(array $rowData): array
    {
        return [
            'name' => $rowData['主体名称'],
            'tax_number' => $rowData['税号'] ?? null,
            'entity_type' => $rowData['主体类型'] ?? 'company',
            'address' => $rowData['主体地址'] ?? null,
            'phone' => $rowData['主体电话'] ?? null,
            'created_by' => auth()->id(),
            'updated_by' => auth()->id(),
            'created_at' => time(),
            'updated_at' => time(),
        ];
    }

    /**
     * 准备资产数据
     */
    protected function prepareAssetData(array $rowData, int $rowNumber): array
    {
        if ($this->templateType === 'new_template') {
            return $this->prepareNewTemplateAssetData($rowData, $rowNumber);
        } else {
            return $this->prepareOldTemplateAssetData($rowData, $rowNumber);
        }
    }

    /**
     * 准备新模板资产数据
     */
    protected function prepareNewTemplateAssetData(array $rowData, int $rowNumber): array
    {
        // 处理资产分类（新模板中可能包含医疗分类、行业分类等）
        $categories = [];

        // 处理带空格的字段名
        $medicalCategory = $rowData['医疗分类'] ?? $rowData['  医疗分类'] ?? '';
        $industryCategory = $rowData['行业分类'] ?? $rowData['  行业分类'] ?? '';

        if (!empty($medicalCategory)) {
            $categories[] = trim($medicalCategory);
        }
        if (!empty($industryCategory)) {
            $categories[] = trim($industryCategory);
        }
        $assetCategories = $this->parseAssetCategories(implode(',', $categories));

        return [
            'name' => $rowData['资产名称'],
            'brand' => $rowData['品牌'] ?? null,
            'model' => $rowData['规格型号'] ?? null,
            'serial_number' => $rowData['序列号'] ?? null,
            'asset_source' => $rowData['资产来源'] ?? null,
            'asset_status' => $rowData['资产状态'] ?? null,
            'asset_condition' => $rowData['成色'] ?? null,
            'parent_id' => $this->parseParentAsset($rowData['主设备'] ?? null),
            'region_code' => $this->parseRegionCode($rowData['所在地区'] ?? null),
            'detailed_address' => $rowData['详细地址'] ?? null,
            'start_date' => $this->parseDate($rowData['启用日期'] ?? null),
            'warranty_period' => $this->parseInteger($rowData['合同质保期(月)'] ?? null),
            'warranty_alert' => $this->parseInteger($rowData['质保期预警(天)'] ?? null),
            'maintenance_cycle' => $this->parseInteger($rowData['维护周期(天)'] ?? null),
            'expected_years' => $this->parseInteger($rowData['预计使用年限(年)'] ?? null),
            'asset_category_ids' => json_encode($assetCategories),
            'remark' => $this->buildRemark($rowData),
            'created_by' => auth()->id(),
            'updated_by' => auth()->id(),
            'created_at' => time(),
            'updated_at' => time(),
            '_row_number' => $rowNumber,
        ];
    }

    /**
     * 准备旧模板资产数据
     */
    protected function prepareOldTemplateAssetData(array $rowData, int $rowNumber): array
    {
        $assetCategories = $this->parseAssetCategories($rowData['资产分类'] ?? '');

        return [
            'name' => $rowData['资产名称'],
            'brand' => $rowData['资产品牌'] ?? null,
            'model' => $rowData['规格型号'] ?? null,
            'serial_number' => $rowData['序列号'] ?? null,
            'asset_source' => $rowData['资产来源'] ?? null,
            'asset_status' => $rowData['资产状态'] ?? null,
            'asset_condition' => $rowData['成色'] ?? null,
            'region_code' => $rowData['区县代码'] ?? null,
            'detailed_address' => $rowData['详细地址'] ?? null,
            'start_date' => $this->parseDate($rowData['启用日期'] ?? null),
            'warranty_period' => $this->parseInteger($rowData['合同质保期(月)'] ?? null),
            'warranty_alert' => $this->parseInteger($rowData['质保期预警(天)'] ?? null),
            'maintenance_cycle' => $this->parseInteger($rowData['维护周期(天)'] ?? null),
            'expected_years' => $this->parseInteger($rowData['预计使用年限(年)'] ?? null),
            'asset_category_ids' => json_encode($assetCategories),
            'remark' => $rowData['备注'] ?? null,
            'created_by' => auth()->id(),
            'updated_by' => auth()->id(),
            'created_at' => time(),
            'updated_at' => time(),
            '_row_number' => $rowNumber,
        ];
    }

    /**
     * 准备联系人数据
     */
    protected function prepareContactData(array $rowData, int $rowNumber): array
    {
        return [
            'entity_key' => $this->getEntityKey($rowData), // 用于关联主体
            'name' => $rowData['联系人姓名'],
            'phone' => $rowData['联系人电话'] ?? '',
            'position' => $rowData['职位'] ?? null,
            'department' => $rowData['部门'] ?? null,
            'created_by' => auth()->id(),
            'updated_by' => auth()->id(),
            'created_at' => time(),
            'updated_at' => time(),
            '_row_number' => $rowNumber, // 用于错误追踪
        ];
    }

    /**
     * 批量插入主体数据
     */
    protected function batchInsertEntities(array $entities): void
    {
        if (empty($entities)) {
            return;
        }

        foreach ($entities as $entityKey => $entityData) {
            // 检查是否已存在
            $existingEntity = null;

            if (!empty($entityData['tax_number'])) {
                $existingEntity = Entity::where('tax_number', $entityData['tax_number'])->first();
            }

            if (!$existingEntity) {
                $existingEntity = Entity::where('name', $entityData['name'])->first();
            }

            if ($existingEntity) {
                // 更新现有记录
                $existingEntity->update($entityData);
                $this->processedEntities[$entityKey] = $existingEntity->id;
            } else {
                // 创建新记录
                $newEntity = Entity::create($entityData);
                $this->processedEntities[$entityKey] = $newEntity->id;
            }
        }
    }

    /**
     * 批量插入资产数据
     */
    protected function batchInsertAssets(array $assets): void
    {
        if (empty($assets)) {
            return;
        }

        $insertData = [];

        foreach ($assets as $assetData) {
            $rowNumber = $assetData['_row_number'];
            unset($assetData['_row_number']);

            // 处理相关主体信息
            $relatedEntities = $this->buildRelatedEntitiesData($assetData, $rowNumber);

            $assetData['related_entities'] = json_encode($relatedEntities);
            $insertData[] = $assetData;
        }

        // 使用批量插入
        if (!empty($insertData)) {
            Asset::insert($insertData);
        }
    }

    /**
     * 批量插入联系人数据
     */
    protected function batchInsertContacts(array $contacts): void
    {
        if (empty($contacts)) {
            return;
        }

        $insertData = [];

        foreach ($contacts as $contactData) {
            $entityKey = $contactData['entity_key'];
            $rowNumber = $contactData['_row_number'];

            unset($contactData['entity_key'], $contactData['_row_number']);

            // 获取对应的主体ID
            if (isset($this->processedEntities[$entityKey])) {
                $contactData['entity_id'] = $this->processedEntities[$entityKey];

                // 检查是否已存在相同联系人
                $existingContact = EntityContact::where('entity_id', $contactData['entity_id'])
                    ->where('name', $contactData['name'])
                    ->where('phone', $contactData['phone'])
                    ->first();

                if (!$existingContact) {
                    $insertData[] = $contactData;
                }
            }
        }

        // 使用批量插入
        if (!empty($insertData)) {
            EntityContact::insert($insertData);
        }
    }

    /**
     * 获取实际文件路径
     */
    protected function getActualFilePath(string $filePath): string
    {
        // 如果是绝对路径且文件存在，直接返回
        if (file_exists($filePath)) {
            return $filePath;
        }

        // 处理附件系统的相对路径
        // 附件路径格式：attachments/YYYY/mm/dd/filename.ext
        $possiblePaths = [
            // Laravel public disk 路径 (storage/app/public/)
            storage_path('app/public/' . $filePath),
            // Laravel local disk 路径 (storage/app/)
            storage_path('app/' . $filePath),
            // 公共存储路径 (public/storage/)
            public_path('storage/' . $filePath),
            // 直接在public目录
            public_path($filePath),
        ];

        foreach ($possiblePaths as $path) {
            if (file_exists($path)) {
                return $path;
            }
        }

        // 如果都不存在，记录日志并返回原路径
        Log::warning('导入文件路径查找失败', [
            'original_path' => $filePath,
            'tried_paths' => $possiblePaths,
        ]);

        return $filePath;
    }

    /**
     * 重置计数器和缓存
     */
    protected function resetCounters(): void
    {
        $this->errors = [];
        $this->totalRows = 0;
        $this->successRows = 0;
        $this->failedRows = 0;
        $this->assetBatch = [];
        $this->entityBatch = [];
        $this->contactBatch = [];
        $this->processedEntities = [];
    }

    /**
     * 解析日期
     */
    protected function parseDate(?string $date): ?int
    {
        if (empty($date)) {
            return null;
        }

        try {
            return strtotime($date);
        } catch (\Exception $e) {
            return null;
        }
    }

    /**
     * 解析整数
     */
    protected function parseInteger(?string $value): ?int
    {
        if (empty($value)) {
            return null;
        }

        return is_numeric($value) ? (int) $value : null;
    }

    /**
     * 解析资产分类
     */
    protected function parseAssetCategories(string $categories): array
    {
        if (empty($categories)) {
            return [];
        }

        // 支持多种分隔符：逗号、分号、竖线
        $categoryNames = preg_split('/[,;|]/', $categories);
        $categoryIds = [];

        foreach ($categoryNames as $categoryName) {
            $categoryName = trim($categoryName);
            if (empty($categoryName)) {
                continue;
            }

            // 防SQL注入：清理分类名称
            $categoryName = preg_replace('/[\'";\\\\]/', '', $categoryName);
            $categoryName = mb_substr($categoryName, 0, 100);

            // 这里可以根据实际需求查找分类ID
            // 暂时使用分类名称的哈希值作为ID（仅用于演示）
            $categoryIds[] = abs(crc32($categoryName)) % 1000000;
        }

        return array_unique($categoryIds);
    }

    /**
     * 解析主设备
     */
    protected function parseParentAsset(?string $parentName): ?int
    {
        if (empty($parentName)) {
            return null;
        }

        // 这里可以根据资产名称查找主设备ID
        // 暂时返回null，实际使用时需要实现查找逻辑
        return null;
    }

    /**
     * 解析地区代码
     */
    protected function parseRegionCode(?string $regionName): ?string
    {
        if (empty($regionName)) {
            return null;
        }

        // 这里可以根据地区名称查找地区代码
        // 暂时返回简化的代码
        $regionMap = [
            '北京' => '110000',
            '上海' => '310000',
            '深圳' => '440300',
            '广州' => '440100',
            '杭州' => '330100',
        ];

        return $regionMap[$regionName] ?? null;
    }

    /**
     * 构建备注信息
     */
    protected function buildRemark(array $rowData): ?string
    {
        $remarks = [];

        // 添加原始备注
        if (!empty($rowData['备注'])) {
            $remarks[] = $rowData['备注'];
        }

        // 添加科室信息（处理带空格的字段名）
        $department = $rowData['科室'] ?? $rowData['  科室'] ?? '';
        if (!empty($department)) {
            $remarks[] = '科室：' . trim($department);
        }

        return empty($remarks) ? null : implode('；', $remarks);
    }

    /**
     * 构建相关主体数据
     */
    protected function buildRelatedEntitiesData(array $assetData, int $rowNumber): array
    {
        $relatedEntities = [];

        if ($this->templateType === 'new_template') {
            // 新模板：处理多个主体类型
            $relatedEntities = $this->buildNewTemplateRelatedEntities($assetData, $rowNumber);
        } else {
            // 旧模板：处理单个主体
            $relatedEntities = $this->buildOldTemplateRelatedEntities($assetData, $rowNumber);
        }

        return $relatedEntities;
    }

    /**
     * 构建新模板相关主体数据
     */
    protected function buildNewTemplateRelatedEntities(array $assetData, int $rowNumber): array
    {
        $relatedEntities = [];

        // 定义主体类型映射
        $entityTypes = [
            'manufacturer' => [
                'name_field' => '生产厂商名称',
                'contact_name_field' => '生产厂商联系人',
                'contact_phone_field' => '生产厂商联系电话',
                'contact_position_field' => '生产厂商职位',
            ],
            'supplier' => [
                'name_field' => '供应商名称',
                'contact_name_field' => '供应商联系人',
                'contact_phone_field' => '供应商联系电话',
                'contact_position_field' => '供应商职位',
            ],
            'service_provider' => [
                'name_field' => '服务商名称',
                'contact_name_field' => '服务商联系人',
                'contact_phone_field' => '服务商联系电话',
                'contact_position_field' => '服务商职位',
            ],
            'after_sales' => [
                'name_field' => '售后部名称',
                'contact_name_field' => '售后部联系人',
                'contact_phone_field' => '售后部联系电话',
                'contact_position_field' => '售后部职位',
            ]
        ];

        foreach ($entityTypes as $entityType => $config) {
            $entityName = trim($assetData[$config['name_field']] ?? '');

            if (!empty($entityName)) {
                // 查找对应的主体ID
                $entityId = $this->findEntityIdByNameAndType($entityName, $entityType);

                if ($entityId) {
                    $relatedEntity = [
                        'entity_id' => $entityId,
                        'entity_type' => $entityType,
                        'contact_name' => trim($assetData[$config['contact_name_field']] ?? ''),
                        'contact_phone' => trim($assetData[$config['contact_phone_field']] ?? ''),
                        'position' => trim($assetData[$config['contact_position_field']] ?? ''),
                        'department' => null, // 新模板中没有部门字段
                    ];

                    $relatedEntities[] = $relatedEntity;
                }
            }
        }

        return $relatedEntities;
    }

    /**
     * 构建旧模板相关主体数据
     */
    protected function buildOldTemplateRelatedEntities(array $assetData, int $rowNumber): array
    {
        $relatedEntities = [];

        $entityName = trim($assetData['主体名称'] ?? '');
        if (!empty($entityName)) {
            // 查找对应的主体ID
            $entityId = $this->findEntityIdByNameAndType($entityName, '企业');

            if ($entityId) {
                $relatedEntity = [
                    'entity_id' => $entityId,
                    'entity_type' => $assetData['主体类型'] ?? '企业',
                    'contact_name' => trim($assetData['联系人姓名'] ?? ''),
                    'contact_phone' => trim($assetData['联系人电话'] ?? ''),
                    'position' => trim($assetData['职位'] ?? ''),
                    'department' => trim($assetData['部门'] ?? ''),
                ];

                $relatedEntities[] = $relatedEntity;
            }
        }

        return $relatedEntities;
    }

    /**
     * 根据名称和类型查找主体ID
     */
    protected function findEntityIdByNameAndType(string $entityName, string $entityType): ?int
    {
        try {
            // 首先从已处理的主体中查找（使用正确的键格式）
            $entityKey = $entityType . '_' . md5($entityName);
            if (isset($this->processedEntities[$entityKey])) {
                return $this->processedEntities[$entityKey];
            }

            // 从数据库中查找
            $entity = Entity::where('name', $entityName)
                           ->where('entity_type', $entityType)
                           ->first();

            return $entity ? $entity->id : null;

        } catch (\Exception $e) {
            Log::warning('查找主体ID失败', [
                'entity_name' => $entityName,
                'entity_type' => $entityType,
                'processed_entities_keys' => array_keys($this->processedEntities),
                'error' => $e->getMessage(),
            ]);

            return null;
        }
    }

    /**
     * 生成导入摘要
     */
    protected function generateSummary(): string
    {
        return sprintf(
            '导入完成：总计 %d 行，成功 %d 行，失败 %d 行',
            $this->totalRows,
            $this->successRows,
            $this->failedRows
        );
    }
}

/**
 * Excel导入读取器
 */
class AssetImportReader
{
    // 这个类用于Excel::toArray()方法
}
