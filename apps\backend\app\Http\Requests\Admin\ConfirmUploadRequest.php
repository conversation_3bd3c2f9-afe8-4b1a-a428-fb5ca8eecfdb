<?php

namespace App\Http\Requests\Admin;

use Illuminate\Foundation\Http\FormRequest;

class ConfirmUploadRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'upload_id' => 'required|uuid',
            'object_key' => 'required|string|max:500',
            'filename' => 'nullable|string|max:255',
            'filesize' => 'nullable|integer|min:1',
            'mime_type' => 'nullable|string|max:100',
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array
     */
    public function messages(): array
    {
        return [
            'upload_id.required' => '上传ID不能为空',
            'upload_id.uuid' => '上传ID格式错误',
            'object_key.required' => '文件路径不能为空',
            'object_key.max' => '文件路径过长',
            'filename.max' => '文件名过长',
            'filesize.integer' => '文件大小必须是整数',
            'filesize.min' => '文件大小无效',
            'mime_type.max' => 'MIME类型过长',
        ];
    }
}