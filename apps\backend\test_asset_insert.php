<?php

/**
 * 测试资产插入和related_entities字段
 */

require_once __DIR__ . '/vendor/autoload.php';

use Illuminate\Foundation\Application;
use App\Models\Asset;

// 启动Laravel应用
$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "=== 测试资产插入和related_entities字段 ===\n\n";

try {
    // 1. 测试直接创建资产
    echo "1. 测试直接创建资产...\n";
    
    $relatedEntitiesData = [
        [
            "entity_id" => 188,
            "entity_type" => "manufacturer",
            "contact_name" => "张三",
            "contact_phone" => "13800138001",
            "position" => "技术经理",
            "department" => null
        ],
        [
            "entity_id" => 189,
            "entity_type" => "supplier",
            "contact_name" => "李四",
            "contact_phone" => "13800138002",
            "position" => "销售经理",
            "department" => null
        ]
    ];
    
    $assetData = [
        'name' => '测试资产_直接创建_' . date('His'),
        'brand' => '测试品牌',
        'model' => 'TEST-002',
        'serial_number' => 'SN' . date('His'),
        'related_entities' => $relatedEntitiesData, // 直接传递数组
        'created_by' => 1,
        'updated_by' => 1,
        'created_at' => time(),
        'updated_at' => time(),
    ];
    
    echo "创建数据: " . json_encode($assetData, JSON_UNESCAPED_UNICODE) . "\n";
    
    $asset1 = Asset::create($assetData);
    echo "✓ 直接创建成功，ID: {$asset1->id}\n";
    
    // 验证数据
    $verifyAsset1 = Asset::find($asset1->id);
    echo "验证 related_entities: ";
    if (is_null($verifyAsset1->related_entities)) {
        echo "NULL\n";
    } elseif (empty($verifyAsset1->related_entities)) {
        echo "空\n";
    } else {
        echo json_encode($verifyAsset1->related_entities, JSON_UNESCAPED_UNICODE) . "\n";
    }
    
    // 2. 测试使用JSON字符串创建
    echo "\n2. 测试使用JSON字符串创建...\n";
    
    $assetData2 = [
        'name' => '测试资产_JSON字符串_' . date('His'),
        'brand' => '测试品牌',
        'model' => 'TEST-003',
        'serial_number' => 'SN' . date('His') . '2',
        'related_entities' => json_encode($relatedEntitiesData), // 传递JSON字符串
        'created_by' => 1,
        'updated_by' => 1,
        'created_at' => time(),
        'updated_at' => time(),
    ];
    
    echo "创建数据: " . json_encode($assetData2, JSON_UNESCAPED_UNICODE) . "\n";
    
    $asset2 = Asset::create($assetData2);
    echo "✓ JSON字符串创建成功，ID: {$asset2->id}\n";
    
    // 验证数据
    $verifyAsset2 = Asset::find($asset2->id);
    echo "验证 related_entities: ";
    if (is_null($verifyAsset2->related_entities)) {
        echo "NULL\n";
    } elseif (empty($verifyAsset2->related_entities)) {
        echo "空\n";
    } else {
        echo json_encode($verifyAsset2->related_entities, JSON_UNESCAPED_UNICODE) . "\n";
    }
    
    // 3. 测试批量插入
    echo "\n3. 测试批量插入...\n";
    
    $batchData = [
        [
            'name' => '测试资产_批量1_' . date('His'),
            'brand' => '测试品牌',
            'model' => 'TEST-004',
            'serial_number' => 'SN' . date('His') . '3',
            'related_entities' => json_encode($relatedEntitiesData),
            'created_by' => 1,
            'updated_by' => 1,
            'created_at' => time(),
            'updated_at' => time(),
        ],
        [
            'name' => '测试资产_批量2_' . date('His'),
            'brand' => '测试品牌',
            'model' => 'TEST-005',
            'serial_number' => 'SN' . date('His') . '4',
            'related_entities' => json_encode($relatedEntitiesData),
            'created_by' => 1,
            'updated_by' => 1,
            'created_at' => time(),
            'updated_at' => time(),
        ]
    ];
    
    echo "批量插入数据: " . json_encode($batchData, JSON_UNESCAPED_UNICODE) . "\n";
    
    Asset::insert($batchData);
    echo "✓ 批量插入成功\n";
    
    // 验证批量插入的数据
    $batchAssets = Asset::whereIn('name', [
        $batchData[0]['name'],
        $batchData[1]['name']
    ])->get();
    
    foreach ($batchAssets as $index => $asset) {
        echo "批量插入资产 " . ($index + 1) . " (ID: {$asset->id}):\n";
        echo "  related_entities: ";
        if (is_null($asset->related_entities)) {
            echo "NULL\n";
        } elseif (empty($asset->related_entities)) {
            echo "空\n";
        } else {
            echo json_encode($asset->related_entities, JSON_UNESCAPED_UNICODE) . "\n";
        }
    }
    
    // 4. 检查数据库中的原始数据
    echo "\n4. 检查数据库中的原始数据...\n";
    
    $rawData = \DB::select("SELECT id, name, related_entities FROM assets WHERE name LIKE '测试资产_%' ORDER BY id DESC LIMIT 5");
    
    foreach ($rawData as $row) {
        echo "ID: {$row->id}, 名称: {$row->name}\n";
        echo "  原始 related_entities: ";
        if (is_null($row->related_entities)) {
            echo "NULL\n";
        } elseif (empty($row->related_entities)) {
            echo "空字符串\n";
        } else {
            echo "'{$row->related_entities}'\n";
        }
    }
    
    // 5. 测试修复方案
    echo "\n5. 测试修复方案...\n";
    
    // 尝试直接更新数据库
    $testAssetId = $batchAssets->first()->id;
    $updateResult = \DB::table('assets')
        ->where('id', $testAssetId)
        ->update(['related_entities' => json_encode($relatedEntitiesData)]);
    
    echo "直接更新数据库结果: {$updateResult}\n";
    
    // 验证更新结果
    $updatedAsset = Asset::find($testAssetId);
    echo "更新后的 related_entities: ";
    if (is_null($updatedAsset->related_entities)) {
        echo "NULL\n";
    } elseif (empty($updatedAsset->related_entities)) {
        echo "空\n";
    } else {
        echo json_encode($updatedAsset->related_entities, JSON_UNESCAPED_UNICODE) . "\n";
    }
    
    echo "\n=== 测试完成 ===\n";
    
} catch (Exception $e) {
    echo "✗ 测试失败：" . $e->getMessage() . "\n";
    echo "错误位置：" . $e->getFile() . ":" . $e->getLine() . "\n";
}

echo "\n=== 测试结束 ===\n";
