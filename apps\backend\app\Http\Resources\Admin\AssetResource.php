<?php

namespace App\Http\Resources\Admin;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class AssetResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,

            // 基础信息
            'name' => $this->name,
            'brand' => $this->brand,
            'model' => $this->model,
            'serial_number' => $this->serial_number,

            // 分类关联
            'asset_category_ids' => $this->asset_category_ids,
            'asset_category' => $this->assetCategory->map(function ($category) {
                return [
                    'id' => $category->id,
                    'name' => $category->name,
                    'code' => $category->code,
                ];
            }),

            // 字典字段
            'asset_source' => $this->asset_source,
            'asset_status' => $this->asset_status,
            'asset_condition' => $this->asset_condition,

            // 父子关系
            'parent_id' => $this->parent_id,
            'parent' => $this->whenLoaded('parent', function () {
                return [
                    'id' => $this->parent->id,
                    'name' => $this->parent->name,
                    'brand' => $this->parent->brand,
                    'model' => $this->parent->model,
                ];
            }),
            'children_count' => $this->when(isset($this->children_count), $this->children_count),
            'children' => $this->whenLoaded('children', function () {
                return $this->children->map(function ($child) {
                    return [
                        'id' => $child->id,
                        'name' => $child->name,
                        'brand' => $child->brand,
                        'model' => $child->model,
                        'asset_status' => $child->asset_status,
                    ];
                });
            }),

            // 地址信息
            'region_code' => $this->region_code,
            'detailed_address' => $this->detailed_address,
            // 完整地址（包含地区信息）
            'full_address' => $this->when($this->relationLoaded('region'), function () {
                return $this->full_address;
            }),
            // 地区路径
            'region_path' => $this->when($this->relationLoaded('region'), function () {
                return $this->region_path;
            }),

            // 时间和数值字段
            'start_date' => $this->start_date,
            'warranty_period' => $this->warranty_period,
            'warranty_alert' => $this->warranty_alert,
            'maintenance_cycle' => $this->maintenance_cycle,
            'expected_years' => $this->expected_years,

            // JSON字段
            'related_entities' => $this->related_entities,

            // 其他字段
            'remark' => $this->remark,

            // 附件
            'attachments' => $this->whenLoaded('attachments', function () {
                return $this->attachments->map(function ($attachment) {
                    return [
                        'id' => $attachment->id,
                        'file_name' => $attachment->file_name,
                        'file_url' => $attachment->file_url,
                        'file_size' => $attachment->file_size,
                        'formatted_file_size' => $attachment->formatted_file_size,
                        'mime_type' => $attachment->mime_type,
                        'storage_type' => $attachment->storage_type,
                        'category' => $attachment->pivot->category ?? null,
                        'sort' => $attachment->pivot->sort ?? 0,
                        'description' => $attachment->pivot->description ?? null,
                        'created_at' => $attachment->created_at,
                    ];
                });
            }),
            'attachments_count' => $this->when(isset($this->attachments_count), $this->attachments_count),

            // 审计字段
            'created_by' => $this->created_by,
            'updated_by' => $this->updated_by,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ];
    }
}
