<?php

/**
 * 生成大数据量Excel模板（基于新模板格式）
 */

require_once __DIR__ . '/vendor/autoload.php';

use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Font;
use PhpOffice\PhpSpreadsheet\Style\Fill;

echo "=== 生成大数据量Excel模板 ===\n\n";

try {
    // 创建新的电子表格
    $spreadsheet = new Spreadsheet();
    $sheet = $spreadsheet->getActiveSheet();
    $sheet->setTitle('资产导入数据');

    // 设置标题行（第1行）
    $sheet->setCellValue('A1', '资产导入模板（大数据测试版）');
    $sheet->mergeCells('A1:AI1');

    // 设置标题样式
    $titleStyle = [
        'font' => [
            'bold' => true,
            'size' => 16,
            'color' => ['rgb' => 'FFFFFF']
        ],
        'fill' => [
            'fillType' => Fill::FILL_SOLID,
            'startColor' => ['rgb' => '4472C4']
        ],
        'alignment' => [
            'horizontal' => Alignment::HORIZONTAL_CENTER,
            'vertical' => Alignment::VERTICAL_CENTER
        ]
    ];
    $sheet->getStyle('A1:AI1')->applyFromArray($titleStyle);
    $sheet->getRowDimension(1)->setRowHeight(30);

    // 定义所有35个字段（第2行）
    $headers = [
        '资产名称', '品牌', '规格型号', '序列号', '资产来源', '资产状态', '成色', '主设备', '所在地区', '详细地址',
        '启用日期', '合同质保期(月)', '质保期预警(天)', '维护周期(天)', '预计使用年限(年)',
        '生产厂商名称', '生产厂商联系人', '生产厂商联系电话', '生产厂商职位',
        '供应商名称', '供应商联系人', '供应商联系电话', '供应商职位',
        '服务商名称', '服务商联系人', '服务商联系电话', '服务商职位',
        '售后部名称', '售后部联系人', '售后部联系电话', '售后部职位',
        '备注', '医疗分类', '科室', '行业分类'
    ];

    // 写入标题行
    $column = 'A';
    foreach ($headers as $header) {
        $sheet->setCellValue($column . '2', $header);
        $column++;
    }

    // 设置标题行样式
    $headerStyle = [
        'font' => [
            'bold' => true,
            'color' => ['rgb' => 'FFFFFF']
        ],
        'fill' => [
            'fillType' => Fill::FILL_SOLID,
            'startColor' => ['rgb' => '70AD47']
        ],
        'alignment' => [
            'horizontal' => Alignment::HORIZONTAL_CENTER,
            'vertical' => Alignment::VERTICAL_CENTER
        ]
    ];
    $sheet->getStyle('A2:AI2')->applyFromArray($headerStyle);
    $sheet->getRowDimension(2)->setRowHeight(25);

    // 生成测试数据模板
    $assetTypes = [
        '办公台式电脑', '笔记本电脑', '激光打印机', '喷墨打印机', '复印机', '扫描仪',
        '投影仪', '服务器', '交换机', '路由器', '防火墙', '存储设备',
        '监控摄像头', '门禁系统', 'UPS电源', '空调设备', '医疗设备', '实验仪器'
    ];

    $brands = [
        '联想', '戴尔', '惠普', '华为', '小米', '苹果', '微软', '华硕', '宏碁', '神舟',
        '思科', '锐捷', '海康威视', '大华', '飞利浦', '西门子', 'GE', '迈瑞'
    ];

    $models = [
        'ThinkCentre M720', 'OptiPlex 7090', 'EliteDesk 800', 'MateStation B515',
        'LaserJet Pro M404', 'Color LaserJet Pro M454', 'WorkCentre 3335',
        'PowerEdge R740', 'ProLiant DL380', 'FusionServer 2288H',
        'Catalyst 2960', 'RG-S5750C', 'DS-2CD2T86FWDV2-I8S'
    ];

    $sources = ['采购', '租赁', '捐赠', '调拨', '自制', '维修更换'];
    $statuses = ['在用', '闲置', '维修', '报废', '封存', '借出'];
    $conditions = ['全新', '九成新', '八成新', '七成新', '六成新', '五成新'];

    $regions = [
        '北京市', '上海市', '深圳市', '广州市', '杭州市', '成都市', '武汉市', '西安市',
        '南京市', '苏州市', '天津市', '重庆市', '青岛市', '大连市', '厦门市', '长沙市'
    ];

    $companies = [
        '北京科技有限公司', '上海信息技术有限公司', '深圳软件开发有限公司', '广州网络科技有限公司',
        '杭州数据服务有限公司', '成都智能科技有限公司', '武汉电子商务有限公司', '西安通信技术有限公司',
        '南京医疗设备有限公司', '苏州精密仪器有限公司', '天津自动化设备有限公司', '重庆工业控制有限公司'
    ];

    $contacts = [
        '张三', '李四', '王五', '赵六', '钱七', '孙八', '周九', '吴十',
        '郑十一', '王十二', '冯十三', '陈十四', '褚十五', '卫十六', '蒋十七', '沈十八'
    ];

    $positions = [
        '技术经理', '销售经理', '客服专员', '技术支持', '维修工程师', '项目经理',
        '产品经理', '运维工程师', '网络管理员', '系统管理员', '数据库管理员', '安全工程师'
    ];

    $medicalCategories = [
        '诊断设备', '治疗设备', '监护设备', '急救设备', '康复设备', '手术设备',
        '检验设备', '影像设备', '消毒设备', '制药设备', '实验设备', '办公设备'
    ];

    $departments = [
        '内科', '外科', '儿科', '妇产科', '眼科', '耳鼻喉科', '口腔科', '皮肤科',
        '神经科', '心血管科', '呼吸科', '消化科', '肾内科', '内分泌科', '血液科', '肿瘤科',
        '急诊科', 'ICU', '手术室', '放射科', '检验科', '药剂科', '行政部', '信息科'
    ];

    $industryCategories = [
        'IT设备', '医疗器械', '办公设备', '网络设备', '安防设备', '实验设备',
        '生产设备', '检测设备', '通信设备', '电力设备', '暖通设备', '消防设备'
    ];

    // 生成指定数量的测试数据
    $totalRows = 2000; // 生成2000行数据（减少内存使用）
    echo "正在生成 {$totalRows} 行测试数据...\n";

    // 设置内存限制
    ini_set('memory_limit', '512M');

    for ($i = 1; $i <= $totalRows; $i++) {
        $row = $i + 2; // Excel行号从3开始（第1行标题，第2行字段名）

        // 随机选择数据
        $assetType = $assetTypes[array_rand($assetTypes)];
        $brand = $brands[array_rand($brands)];
        $model = $models[array_rand($models)];
        $region = $regions[array_rand($regions)];
        $company = $companies[array_rand($companies)];
        $contact = $contacts[array_rand($contacts)];
        $position = $positions[array_rand($positions)];
        $medicalCategory = $medicalCategories[array_rand($medicalCategories)];
        $department = $departments[array_rand($departments)];
        $industryCategory = $industryCategories[array_rand($industryCategories)];

        // 生成数据行
        $testData = [
            $assetType . sprintf('%04d', $i),                    // 资产名称
            $brand,                                               // 品牌
            $model . '-' . sprintf('%03d', rand(100, 999)),     // 规格型号
            strtoupper(substr($brand, 0, 2)) . sprintf('%010d', $i), // 序列号
            $sources[array_rand($sources)],                      // 资产来源
            $statuses[array_rand($statuses)],                    // 资产状态
            $conditions[array_rand($conditions)],                // 成色
            $i > 1 ? $assetTypes[array_rand($assetTypes)] . sprintf('%04d', rand(1, $i-1)) : '', // 主设备
            $region,                                             // 所在地区
            $region . 'XX街道' . $i . '号',                      // 详细地址
            '2024-' . sprintf('%02d', rand(1, 12)) . '-' . sprintf('%02d', rand(1, 28)), // 启用日期
            rand(12, 60),                                        // 合同质保期(月)
            rand(15, 90),                                        // 质保期预警(天)
            rand(30, 180),                                       // 维护周期(天)
            rand(3, 15),                                         // 预计使用年限(年)
            $company . '制造部',                                 // 生产厂商名称
            $contact . '(制造)',                                 // 生产厂商联系人
            '138' . sprintf('%08d', rand(10000000, 99999999)),   // 生产厂商联系电话
            $position,                                           // 生产厂商职位
            $company . '销售部',                                 // 供应商名称
            $contact . '(销售)',                                 // 供应商联系人
            '139' . sprintf('%08d', rand(10000000, 99999999)),   // 供应商联系电话
            $position,                                           // 供应商职位
            $company . '服务部',                                 // 服务商名称
            $contact . '(服务)',                                 // 服务商联系人
            '137' . sprintf('%08d', rand(10000000, 99999999)),   // 服务商联系电话
            $position,                                           // 服务商职位
            $company . '售后部',                                 // 售后部名称
            $contact . '(售后)',                                 // 售后部联系人
            '136' . sprintf('%08d', rand(10000000, 99999999)),   // 售后部联系电话
            $position,                                           // 售后部职位
            '测试数据第' . $i . '行，用于批量导入性能测试',        // 备注
            $medicalCategory,                                    // 医疗分类
            $department,                                         // 科室
            $industryCategory,                                   // 行业分类
        ];

        // 写入数据行
        $column = 'A';
        foreach ($testData as $value) {
            $sheet->setCellValue($column . $row, $value);
            $column++;
        }

        // 每1000行显示进度
        if ($i % 1000 === 0) {
            echo "已生成 {$i} 行数据...\n";
        }
    }

    // 设置固定列宽（避免自适应消耗内存）
    $columnWidths = [
        'A' => 20, 'B' => 15, 'C' => 20, 'D' => 15, 'E' => 12,
        'F' => 12, 'G' => 10, 'H' => 15, 'I' => 12, 'J' => 25
    ];

    foreach ($columnWidths as $col => $width) {
        $sheet->getColumnDimension($col)->setWidth($width);
    }

    // 冻结标题行
    $sheet->freezePane('A3');

    // 保存文件
    $writer = new Xlsx($spreadsheet);
    $filename = 'storage/app/public/大数据资产导入模板_' . date('Y-m-d_H-i-s') . '.xlsx';

    // 确保目录存在
    $dir = dirname($filename);
    if (!is_dir($dir)) {
        mkdir($dir, 0755, true);
    }

    echo "正在保存Excel文件...\n";
    $writer->save($filename);

    $fileSize = round(filesize($filename) / 1024 / 1024, 2);

    echo "\n=== 大数据Excel模板生成完成 ===\n";
    echo "文件路径: {$filename}\n";
    echo "数据行数: {$totalRows} 行\n";
    echo "总字段数: " . count($headers) . " 个\n";
    echo "文件大小: {$fileSize} MB\n";

    echo "\n性能测试建议：\n";
    echo "1. 批量大小: 1000 行/批次\n";
    echo "2. 预计批次: " . ceil($totalRows / 1000) . " 个批次\n";
    echo "3. 内存使用: 建议设置 memory_limit >= 1024M\n";
    echo "4. 执行时间: 建议设置 max_execution_time >= 600s\n";
    echo "5. 预计处理时间: 约 " . ceil($totalRows / 100) . " 分钟\n";

    echo "\n数据特点：\n";
    echo "- 包含完整的35个字段数据\n";
    echo "- 每行包含4种主体类型（生产厂商、供应商、服务商、售后部）\n";
    echo "- 每种主体类型包含联系人信息\n";
    echo "- 预计生成 " . ($totalRows * 4) . " 个主体记录\n";
    echo "- 预计生成 " . ($totalRows * 4) . " 个联系人记录\n";

    echo "\n测试用途：\n";
    echo "✓ 批量处理性能测试\n";
    echo "✓ 内存使用优化验证\n";
    echo "✓ 数据库批量插入性能\n";
    echo "✓ 错误处理机制验证\n";
    echo "✓ 队列处理稳定性测试\n";

    echo "\n使用方法：\n";
    echo "1. 上传文件到附件系统\n";
    echo "2. 启动队列处理器: php artisan asset:process-import-queue\n";
    echo "3. 调用导入API: POST /api/admin/assets/import/{attachment_id}\n";
    echo "4. 监控导入进度: GET /api/admin/assets/import-tasks/{task_id}\n";

    echo "\n文件生成完成！\n";

} catch (Exception $e) {
    echo "✗ 生成失败：" . $e->getMessage() . "\n";
    echo "错误位置：" . $e->getFile() . ":" . $e->getLine() . "\n";
}

echo "\n=== 生成结束 ===\n";
