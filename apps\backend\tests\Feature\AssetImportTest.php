<?php

namespace Tests\Feature;

use App\Jobs\ProcessAssetImport;
use App\Models\AssetImportTask;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Queue;
use Illuminate\Support\Facades\Storage;
use Tests\TestCase;

class AssetImportTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // 创建测试用户
        $this->user = User::factory()->create();
        $this->actingAs($this->user, 'sanctum');
        
        // 设置存储为测试环境
        Storage::fake('local');
        Queue::fake();
    }

    /** @test */
    public function it_can_upload_excel_file_and_create_import_task()
    {
        // 创建一个假的Excel文件
        $file = UploadedFile::fake()->create('assets.xlsx', 100, 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');

        $response = $this->postJson('/api/admin/assets/import', [
            'file' => $file,
        ]);

        $response->assertStatus(201)
            ->assertJsonStructure([
                'message',
                'task_id',
            ]);

        // 验证任务已创建
        $this->assertDatabaseHas('asset_import_tasks', [
            'original_filename' => 'assets.xlsx',
            'status' => 'pending',
            'created_by' => $this->user->id,
        ]);

        // 验证Job已分发
        Queue::assertPushed(ProcessAssetImport::class);
    }

    /** @test */
    public function it_validates_file_upload()
    {
        // 测试没有文件
        $response = $this->postJson('/api/admin/assets/import', []);
        $response->assertStatus(422)
            ->assertJsonValidationErrors(['file']);

        // 测试错误的文件类型
        $file = UploadedFile::fake()->create('assets.txt', 100, 'text/plain');
        $response = $this->postJson('/api/admin/assets/import', [
            'file' => $file,
        ]);
        $response->assertStatus(422)
            ->assertJsonValidationErrors(['file']);
    }

    /** @test */
    public function it_can_get_import_task_status()
    {
        $task = AssetImportTask::create([
            'file_path' => 'test/file.xlsx',
            'original_filename' => 'test.xlsx',
            'status' => 'completed',
            'total_rows' => 10,
            'success_rows' => 8,
            'failed_rows' => 2,
            'created_by' => $this->user->id,
        ]);

        $response = $this->getJson("/api/admin/assets/import-tasks/{$task->id}");

        $response->assertStatus(200)
            ->assertJson([
                'id' => $task->id,
                'status' => 'completed',
                'original_filename' => 'test.xlsx',
                'total_rows' => 10,
                'success_rows' => 8,
                'failed_rows' => 2,
            ]);
    }

    /** @test */
    public function it_can_get_import_tasks_list()
    {
        // 创建几个测试任务
        AssetImportTask::factory()->count(3)->create([
            'created_by' => $this->user->id,
        ]);

        $response = $this->getJson('/api/admin/assets/import-tasks');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'data' => [
                    '*' => [
                        'id',
                        'status',
                        'original_filename',
                        'total_rows',
                        'success_rows',
                        'failed_rows',
                        'created_at',
                    ]
                ],
                'meta' => [
                    'total',
                    'current_page',
                    'per_page',
                ]
            ]);
    }
}
