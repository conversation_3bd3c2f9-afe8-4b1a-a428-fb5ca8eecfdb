<?php

/**
 * 获取模板的所有标题字段
 */

require_once __DIR__ . '/vendor/autoload.php';

use PhpOffice\PhpSpreadsheet\IOFactory;
use PhpOffice\PhpSpreadsheet\Cell\Coordinate;

echo "=== 获取模板所有标题字段 ===\n\n";

try {
    $templatePath = 'storage/app/public/资产导入模板_2025-08-14_10-26-23.xlsx';
    $fullPath = __DIR__ . '/' . $templatePath;
    
    $spreadsheet = IOFactory::load($fullPath);
    $worksheet = $spreadsheet->getActiveSheet();
    
    // 第2行是标题行
    $headerRow = 2;
    $highestColumn = $worksheet->getHighestDataColumn();
    $highestColumnIndex = Coordinate::columnIndexFromString($highestColumn);
    
    echo "标题行（第{$headerRow}行）所有字段：\n\n";
    
    $headers = [];
    for ($col = 1; $col <= $highestColumnIndex; $col++) {
        $cellValue = $worksheet->getCellByColumnAndRow($col, $headerRow)->getCalculatedValue();
        $columnLetter = Coordinate::stringFromColumnIndex($col);
        
        if (!empty($cellValue)) {
            $headers[$col] = $cellValue;
            echo sprintf("%2d. %s: %s\n", $col, $columnLetter, $cellValue);
        }
    }
    
    echo "\n总计 " . count($headers) . " 个字段\n\n";
    
    // 按类别分组
    echo "=== 字段分类 ===\n\n";
    
    $assetFields = [];
    $manufacturerFields = [];
    $supplierFields = [];
    $serviceFields = [];
    $afterSalesFields = [];
    $otherFields = [];
    
    foreach ($headers as $index => $header) {
        $category = null;
        
        if (strpos($header, '资产') !== false || 
            in_array($header, ['品牌', '规格型号', '序列号', '资产来源', '资产状态', '成色', '主设备', '所在地区', '详细地址', '启用日期', '合同质保期(月)', '质保期预警(天)', '维护周期(天)', '预计使用年限(年)'])) {
            $assetFields[] = $header;
            $category = '资产信息';
        } elseif (strpos($header, '生产厂商') !== false) {
            $manufacturerFields[] = $header;
            $category = '生产厂商';
        } elseif (strpos($header, '供应商') !== false) {
            $supplierFields[] = $header;
            $category = '供应商';
        } elseif (strpos($header, '服务商') !== false) {
            $serviceFields[] = $header;
            $category = '服务商';
        } elseif (strpos($header, '售后') !== false) {
            $afterSalesFields[] = $header;
            $category = '售后服务';
        } else {
            $otherFields[] = $header;
            $category = '其他';
        }
        
        echo sprintf("%2d. %-30s [%s]\n", $index, $header, $category);
    }
    
    echo "\n=== 分类统计 ===\n";
    echo "资产信息字段: " . count($assetFields) . " 个\n";
    echo "生产厂商字段: " . count($manufacturerFields) . " 个\n";
    echo "供应商字段: " . count($supplierFields) . " 个\n";
    echo "服务商字段: " . count($serviceFields) . " 个\n";
    echo "售后服务字段: " . count($afterSalesFields) . " 个\n";
    echo "其他字段: " . count($otherFields) . " 个\n";
    
    // 生成字段映射数组
    echo "\n=== PHP数组格式 ===\n";
    echo "<?php\n\n";
    echo "\$templateHeaders = [\n";
    foreach ($headers as $index => $header) {
        echo "    {$index} => '{$header}',\n";
    }
    echo "];\n\n";
    
    echo "// 字段映射建议\n";
    echo "\$fieldMapping = [\n";
    foreach ($headers as $header) {
        $mapping = '';
        
        // 资产字段映射
        switch ($header) {
            case '资产名称':
                $mapping = 'name';
                break;
            case '品牌':
                $mapping = 'brand';
                break;
            case '规格型号':
                $mapping = 'model';
                break;
            case '序列号':
                $mapping = 'serial_number';
                break;
            case '资产来源':
                $mapping = 'asset_source';
                break;
            case '资产状态':
                $mapping = 'asset_status';
                break;
            case '成色':
                $mapping = 'asset_condition';
                break;
            case '主设备':
                $mapping = 'parent_id';
                break;
            case '所在地区':
                $mapping = 'region_code';
                break;
            case '详细地址':
                $mapping = 'detailed_address';
                break;
            case '启用日期':
                $mapping = 'start_date';
                break;
            case '合同质保期(月)':
                $mapping = 'warranty_period';
                break;
            case '质保期预警(天)':
                $mapping = 'warranty_alert';
                break;
            case '维护周期(天)':
                $mapping = 'maintenance_cycle';
                break;
            case '预计使用年限(年)':
                $mapping = 'expected_years';
                break;
            default:
                if (strpos($header, '生产厂商') !== false) {
                    if (strpos($header, '名称') !== false) $mapping = 'manufacturer.name';
                    elseif (strpos($header, '联系人') !== false) $mapping = 'manufacturer.contact_name';
                    elseif (strpos($header, '电话') !== false) $mapping = 'manufacturer.contact_phone';
                    elseif (strpos($header, '职位') !== false) $mapping = 'manufacturer.contact_position';
                } elseif (strpos($header, '供应商') !== false) {
                    if (strpos($header, '名称') !== false) $mapping = 'supplier.name';
                    elseif (strpos($header, '联系人') !== false) $mapping = 'supplier.contact_name';
                    elseif (strpos($header, '电话') !== false) $mapping = 'supplier.contact_phone';
                    elseif (strpos($header, '职位') !== false) $mapping = 'supplier.contact_position';
                } elseif (strpos($header, '服务商') !== false) {
                    if (strpos($header, '名称') !== false) $mapping = 'service_provider.name';
                    elseif (strpos($header, '联系人') !== false) $mapping = 'service_provider.contact_name';
                    elseif (strpos($header, '电话') !== false) $mapping = 'service_provider.contact_phone';
                    elseif (strpos($header, '职位') !== false) $mapping = 'service_provider.contact_position';
                } elseif (strpos($header, '售后') !== false) {
                    if (strpos($header, '名称') !== false) $mapping = 'after_sales.name';
                    elseif (strpos($header, '联系人') !== false) $mapping = 'after_sales.contact_name';
                    elseif (strpos($header, '电话') !== false) $mapping = 'after_sales.contact_phone';
                    elseif (strpos($header, '职位') !== false) $mapping = 'after_sales.contact_position';
                }
                break;
        }
        
        if ($mapping) {
            echo "    '{$header}' => '{$mapping}',\n";
        } else {
            echo "    '{$header}' => '', // 需要映射\n";
        }
    }
    echo "];\n";
    
} catch (Exception $e) {
    echo "✗ 分析失败：" . $e->getMessage() . "\n";
}

echo "\n=== 完成 ===\n";
