<?php

namespace App\Services;

use App\Models\Menu;
use App\Models\User;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\DB;

class MenuService
{
    /**
     * 获取菜单列表（返回扁平数组）
     * 参考 CategoryController 的实现方式
     */
    public function getMenuList(): array
    {
        // 获取扁平化的菜单数据，按照 parent_id 和 sort 排序
        $menus = Menu::with('permissions')
            ->where('status', true)
            ->orderBy('parent_id', 'asc')
            ->orderBy('sort', 'desc')  // 数字越大越靠前
            ->orderBy('id', 'desc')
            ->get();

        // 直接返回扁平数组，前端会自己构建树形结构
        return ['menuList' => $menus];
    }

    /**
     * 获取菜单树（用于选择父级菜单）
     */
    public function getMenuTree(): Collection
    {
        return Menu::select(['id', 'parent_id', 'title', 'name', 'path'])
            ->where('status', true)
            ->orderBy('parent_id', 'asc')
            ->orderBy('sort', 'desc')
            ->orderBy('id', 'desc')
            ->get();
    }

    /**
     * 根据用户权限获取菜单列表
     */
    public function getMenuListByUser(User $user): array
    {
        // 获取用户有权限访问的菜单
        $accessibleMenus = $user->getAccessibleMenus();

        // 记录日志失败，记录到系统日志
        \Log::error('MenuService getMenuListByUser: ', [
            'accessibleMenus' => $accessibleMenus
        ]);

        // 获取菜单ID列表
        $menuIds = $accessibleMenus->pluck('id')->toArray();

        // 如果用户没有任何菜单权限，返回空数组
        if (empty($menuIds)) {
            return ['menuList' => []];
        }

        // 获取有权限的菜单及其父级菜单
        $allMenuIds = $this->getMenuIdsWithParents($menuIds);

        // 获取最终的菜单列表
        $menus = Menu::with('permissions')
            ->whereIn('id', $allMenuIds)
            ->where('status', true)
            ->orderBy('parent_id', 'asc')
            ->orderBy('sort', 'desc')
            ->orderBy('id', 'desc')
            ->get();

        // 过滤菜单权限，只显示用户有权限的操作
        $menus = $menus->map(function ($menu) use ($user) {
            // 获取用户对该菜单的权限
            $userPermissions = $user->getAllPermissions()
                ->where('menu_id', $menu->id)
                ->pluck('menu_permission_id')
                ->filter()
                ->toArray();

            // 过滤菜单的权限列表
            $menu->permissions = $menu->permissions->filter(function ($permission) use ($userPermissions) {
                return in_array($permission->id, $userPermissions);
            });

            return $menu;
        });

        return ['menuList' => $menus];
    }

    /**
     * 获取菜单ID及其所有父级菜单ID
     */
    private function getMenuIdsWithParents(array $menuIds): array
    {
        $allMenuIds = $menuIds;
        $parentIds = [];

        // 递归获取所有父级菜单ID
        $currentIds = $menuIds;
        while (!empty($currentIds)) {
            $parents = Menu::whereIn('id', $currentIds)
                ->whereNotNull('parent_id')
                ->pluck('parent_id')
                ->unique()
                ->toArray();

            $newParents = array_diff($parents, $allMenuIds);
            if (empty($newParents)) {
                break;
            }

            $allMenuIds = array_merge($allMenuIds, $newParents);
            $currentIds = $newParents;
        }

        return array_unique($allMenuIds);
    }

    /**
     * 创建菜单
     */
    public function create(array $data): Menu
    {
        return DB::transaction(function () use ($data) {
            // 生成自动默认值（如果字段为空）
            $timestamp = time();
            $randomStr = substr(str_shuffle('abcdefghijklmnopqrstuvwxyz'), 0, 4);
            
            // 自动生成路由名称（带AUTO前缀标识）
            if (empty($data['name'])) {
                $data['name'] = 'AUTO_MENU_' . $randomStr . '_' . $timestamp;
            }
            
            // 自动生成路由路径（带auto前缀标识）
            if (empty($data['path'])) {
                $data['path'] = '/auto_path_' . $randomStr . '_' . $timestamp;
            } else {
                // 标准化路径
                $data['path'] = $this->normalizePath($data['path']);
            }

            // 验证父级菜单
            if (!empty($data['parent_id'])) {
                $parentMenu = Menu::find($data['parent_id']);
                if (!$parentMenu) {
                    throw new \Exception('父级菜单不存在');
                }
            }

            // 提取权限数据
            $permissions = $data['permissions'] ?? [];
            unset($data['permissions']);
            
            // 为权限生成自动标识（如果为空）
            foreach ($permissions as &$permission) {
                if (empty($permission['auth_mark'])) {
                    $permRandomStr = substr(str_shuffle('abcdefghijklmnopqrstuvwxyz'), 0, 4);
                    $permission['auth_mark'] = 'AUTO_PERM_' . $permRandomStr . '_' . time();
                }
            }

            // 设置默认值
            $data['status'] = $data['status'] ?? true;

            try {
                // 创建菜单
                $menu = Menu::create($data);

                // 创建权限按钮
                foreach ($permissions as $permission) {
                    $menu->permissions()->create($permission);
                }

                return $menu->fresh(['permissions']);
            } catch (\Exception $e) {
                \Log::error('创建菜单失败: ' . $e->getMessage(), [
                    'data' => $data,
                    'error' => $e
                ]);
                throw $e;
            }
        });
    }

    /**
     * 更新菜单
     */
    public function update(Menu $menu, array $data): Menu
    {
        return DB::transaction(function () use ($menu, $data) {
            // 生成自动默认值（如果字段为空）
            $timestamp = time();
            $randomStr = substr(str_shuffle('abcdefghijklmnopqrstuvwxyz'), 0, 4);
            
            // 如果name为空，生成自动值
            if (isset($data['name']) && empty($data['name'])) {
                $data['name'] = 'AUTO_MENU_' . $randomStr . '_' . $timestamp;
            }
            
            // 标准化路径或生成自动值
            if (isset($data['path'])) {
                if (empty($data['path'])) {
                    $data['path'] = '/auto_path_' . $randomStr . '_' . $timestamp;
                } else {
                    $data['path'] = $this->normalizePath($data['path']);
                }
            }

            // 验证父级菜单
            if (!empty($data['parent_id']) && $data['parent_id'] !== $menu->parent_id) {
                // 检查是否将父菜单设置为自己的子菜单
                if ($this->isChildMenu($menu->id, $data['parent_id'])) {
                    throw new \Exception('不能将子菜单设置为父菜单');
                }

                $parentMenu = Menu::find($data['parent_id']);
                if (!$parentMenu) {
                    throw new \Exception('父级菜单不存在');
                }
            }

            // 提取权限数据
            $permissions = $data['permissions'] ?? [];
            unset($data['permissions']);
            
            // 为权限生成自动标识（如果为空）
            foreach ($permissions as &$permission) {
                if (empty($permission['auth_mark'])) {
                    $permRandomStr = substr(str_shuffle('abcdefghijklmnopqrstuvwxyz'), 0, 4);
                    $permission['auth_mark'] = 'AUTO_PERM_' . $permRandomStr . '_' . time();
                }
            }

            try {
                // 更新菜单
                $menu->update($data);

                // 更新权限按钮（先删除再创建）
                $menu->permissions()->delete();
                foreach ($permissions as $permission) {
                    $menu->permissions()->create($permission);
                }

                return $menu->fresh(['permissions']);
            } catch (\Exception $e) {
                \Log::error('更新菜单失败: ' . $e->getMessage(), [
                    'menu_id' => $menu->id,
                    'data' => $data,
                    'error' => $e
                ]);
                throw $e;
            }
        });
    }

    /**
     * 标准化路径
     */
    private function normalizePath(string $path): string
    {
        // 确保路径以/开头
        $path = '/' . ltrim($path, '/');

        // 移除末尾的/
        $path = rtrim($path, '/');

        // 规范化多个/
        $path = preg_replace('/\/+/', '/', $path);

        return $path;
    }

    /**
     * 检查是否为子菜单
     */
    private function isChildMenu(int $parentId, int $childId): bool
    {
        $menu = Menu::find($childId);
        while ($menu && $menu->parent_id) {
            if ($menu->parent_id == $parentId) {
                return true;
            }
            $menu = Menu::find($menu->parent_id);
        }
        return false;
    }

    /**
     * 删除菜单
     */
    public function delete(Menu $menu): bool
    {
        return DB::transaction(function () use ($menu) {
            // 检查是否有子菜单
            $hasChildren = Menu::where('parent_id', $menu->id)->exists();
            if ($hasChildren) {
                throw new \Exception('该菜单下有子菜单，无法删除');
            }

            // 删除权限按钮
            $menu->permissions()->delete();

            // 删除菜单
            return $menu->delete();
        });
    }
}
