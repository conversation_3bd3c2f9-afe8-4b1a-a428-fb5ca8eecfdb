import request from '@/utils/http'
import type { AppRouteRecord } from '@/types/router'

// 后端返回的菜单数据类型
export interface BackendMenuItem extends AppRouteRecord {
  parent_id: number | null
  sort: number
  is_hide: boolean
  is_hide_tab: boolean
  is_iframe: boolean
  keep_alive: boolean
  is_first_level: boolean
  fixed_tab: boolean
  active_path: string | null
  is_full_page: boolean
  show_badge: boolean
  show_text_badge: string | null
  status: boolean
  created_at: string
  updated_at: string
  permissions: MenuPermission[]
}

// 菜单权限类型
export interface MenuPermission {
  id: number
  menu_id: number
  title: string
  auth_mark: string
  route_name: string
  sort: number
  created_at: string
  updated_at: string
}

// 菜单表单类型
export interface MenuForm {
  parent_id: number | null
  name: string
  path: string
  component?: string
  title: string
  icon?: string
  label?: string
  sort?: number
  is_hide?: boolean
  is_hide_tab?: boolean
  link?: string
  is_iframe?: boolean
  keep_alive?: boolean
  is_first_level?: boolean
  fixed_tab?: boolean
  active_path?: string
  is_full_page?: boolean
  show_badge?: boolean
  show_text_badge?: string
  status?: boolean
  permissions?: MenuPermissionForm[]
}

// 菜单权限表单类型
export interface MenuPermissionForm {
  title: string
  auth_mark: string
  sort?: number
}

/**
 * 构建树形结构
 */
const buildTree = (flatData: BackendMenuItem[]): BackendMenuItem[] => {
  const map = new Map<number, BackendMenuItem>()
  const tree: BackendMenuItem[] = []

  // 初始化所有节点，每个节点都有children数组
  flatData.forEach((item) => {
    map.set(item.id!, { ...item, children: item.children || [] })
  })

  // 构建树形结构
  flatData.forEach((item) => {
    const node = map.get(item.id!)!
    if (!item.parent_id || item.parent_id === null) {
      tree.push(node)
    } else {
      const parent = map.get(item.parent_id)
      if (parent) {
        if (!parent.children) {
          parent.children = []
        }
        parent.children.push(node)
      }
    }
  })

  return tree
}

/**
 * 获取菜单列表（树形结构）
 */
export const getMenuList = async (): Promise<BackendMenuItem[]> => {
  const response = await request.get<{ menuList: BackendMenuItem[] }>({
    url: '/admin/menus'
  })
  // 构建树形结构
  return buildTree(response.menuList)
}

/**
 * 获取菜单树（用于选择父级菜单）
 */
export const getMenuTree = async (): Promise<BackendMenuItem[]> => {
  const response = await request.get<BackendMenuItem[]>({
    url: '/admin/menus/tree'
  })
  return buildTree(response)
}

/**
 * 创建菜单
 */
export const createMenu = (data: MenuForm): Promise<BackendMenuItem> => {
  return request.post<BackendMenuItem>({
    url: '/admin/menus',
    data
  })
}

/**
 * 更新菜单
 */
export const updateMenu = (id: number | string, data: MenuForm): Promise<BackendMenuItem> => {
  return request.put<BackendMenuItem>({
    url: `/admin/menus/${id}`,
    data
  })
}

/**
 * 删除菜单
 */
export const deleteMenu = (id: number | string): Promise<void> => {
  return request.del<void>({
    url: `/admin/menus/${id}`
  })
}
