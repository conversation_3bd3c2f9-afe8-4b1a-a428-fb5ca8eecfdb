<template>
  <ElConfigProvider
    size="default"
    :locale="locales[language]"
    :z-index="3000"
    :empty-values="[null, undefined]"
  >
    <RouterView></RouterView>
  </ElConfigProvider>
</template>

<script setup lang="ts">
  import { useUserStore } from './store/modules/user'
  import { useDictionaryStore } from './store/modules/dictionary'
  import { useConfigStore } from './store/modules/config'
  import zh from 'element-plus/es/locale/lang/zh-cn'
  import en from 'element-plus/es/locale/lang/en'
  import { systemUpgrade } from './utils/sys'
  // import { UserService } from './api/usersApi' // 不再需要，登录时已获取完整用户信息
  import { setThemeTransitionClass } from './utils/theme/animation'
  import { checkStorageCompatibility } from './utils/storage'

  const userStore = useUserStore()
  const dictionaryStore = useDictionaryStore()
  const configStore = useConfigStore()
  const { language } = storeToRefs(userStore)

  const locales = {
    zh: zh,
    en: en
  }

  onBeforeMount(() => {
    setThemeTransitionClass(true)
  })

  onMounted(() => {
    // 检查存储兼容性
    checkStorageCompatibility()
    // 提升暗黑主题下页面刷新视觉体验
    setThemeTransitionClass(false)
    // 系统升级
    systemUpgrade()
    // 获取用户信息
    // getUserInfo() // 不再需要，登录时已获取完整用户信息
    // 初始化字典
    initializeDictionary()
    // 初始化配置
    initializeConfig()
  })

  // 获取用户信息
  // 注释：不再需要，登录时已获取完整用户信息并存储到 pinia
  // const getUserInfo = async () => {
  //   if (userStore.isLogin) {
  //     try {
  //       const data = await UserService.getUserInfo()
  //       userStore.setUserInfo(data)
  //     } catch (error) {
  //       console.error('获取用户信息失败', error)
  //     }
  //   }
  // }

  // 初始化字典
  const initializeDictionary = async () => {
    // 只有登录后才初始化字典
    if (userStore.isLogin) {
      try {
        await dictionaryStore.initialize()
        console.log('字典初始化成功')
      } catch (error) {
        console.error('字典初始化失败', error)
        // 不影响应用运行，字典将在使用时按需加载
      }
    }
  }

  // 初始化配置
  const initializeConfig = async () => {
    // 只有登录后才初始化配置
    if (userStore.isLogin) {
      try {
        await configStore.initialize()
        console.log('配置初始化成功')
      } catch (error) {
        console.error('配置初始化失败', error)
        // 不影响应用运行，配置将在使用时按需加载
      }
    }
  }
</script>
