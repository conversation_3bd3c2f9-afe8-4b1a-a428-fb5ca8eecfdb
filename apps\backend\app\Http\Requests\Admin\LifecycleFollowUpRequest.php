<?php

namespace App\Http\Requests\Admin;

use Illuminate\Foundation\Http\FormRequest;

class LifecycleFollowUpRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $isUpdate = $this->isMethod('put') || $this->isMethod('patch');

        $rules = [
            'date' => ($isUpdate ? 'sometimes|' : '') . 'required',
            'person_id' => ($isUpdate ? 'sometimes|' : '') . 'required|exists:users,id',
            'content' => ($isUpdate ? 'sometimes|' : '') . 'required|string',
            'attachments' => 'nullable|array',
            'attachments.*' => 'exists:attachments,id',
        ];

        return $rules;
    }

    /**
     * Get the error messages for the defined validation rules.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'date.required' => '日期不能为空',
            'person_id.required' => '跟进人不能为空',
            'person_id.exists' => '跟进人不存在',
            'content.required' => '内容不能为空',
            'attachments.*.exists' => '附件不存在',
        ];
    }
}
