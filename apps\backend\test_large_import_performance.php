<?php

/**
 * 测试大数据量导入性能
 */

require_once __DIR__ . '/vendor/autoload.php';

use Illuminate\Foundation\Application;
use App\Models\Attachment;
use App\Models\AssetImportTask;
use App\Services\AssetImportService;

// 启动Laravel应用
$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "=== 大数据量导入性能测试 ===\n\n";

try {
    // 查找生成的大数据文件
    echo "1. 查找大数据测试文件...\n";
    
    $testFiles = [
        'storage/app/public/大数据资产导入模板_2025-08-14_06-26-47.xlsx' => '中等数据量（2000行）',
        'storage/app/public/超大数据资产导入模板_2025-08-14_06-27-55.xlsx' => '超大数据量（10000行）'
    ];
    
    $selectedFile = null;
    $selectedDescription = '';
    
    foreach ($testFiles as $filePath => $description) {
        $fullPath = __DIR__ . '/' . $filePath;
        if (file_exists($fullPath)) {
            echo "找到测试文件: {$description}\n";
            echo "  路径: {$filePath}\n";
            echo "  大小: " . number_format(filesize($fullPath) / 1024 / 1024, 2) . " MB\n";
            
            if (!$selectedFile) {
                $selectedFile = $filePath;
                $selectedDescription = $description;
            }
        }
    }
    
    if (!$selectedFile) {
        echo "未找到测试文件，请先运行生成脚本\n";
        exit(1);
    }
    
    echo "\n选择测试文件: {$selectedDescription}\n";
    
    // 创建或查找附件记录
    echo "\n2. 创建附件记录...\n";
    
    $fileName = basename($selectedFile);
    $attachment = Attachment::where('file_name', $fileName)->first();
    
    if (!$attachment) {
        $attachment = Attachment::create([
            'file_name' => $fileName,
            'file_path' => str_replace('storage/app/public/', '', $selectedFile),
            'file_size' => filesize(__DIR__ . '/' . $selectedFile),
            'mime_type' => 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            'storage_type' => 'local',
            'md5_hash' => md5_file(__DIR__ . '/' . $selectedFile),
        ]);
        echo "创建新附件记录，ID: {$attachment->id}\n";
    } else {
        echo "使用现有附件记录，ID: {$attachment->id}\n";
    }
    
    // 性能测试配置
    echo "\n3. 性能测试配置...\n";
    
    $testConfigs = [
        'small' => ['batch_size' => 100, 'description' => '小批次测试（100行/批）'],
        'medium' => ['batch_size' => 500, 'description' => '中批次测试（500行/批）'],
        'large' => ['batch_size' => 1000, 'description' => '大批次测试（1000行/批）'],
    ];
    
    // 选择测试配置（根据文件大小）
    $selectedConfig = strpos($selectedFile, '超大数据') !== false ? 'large' : 'medium';
    $config = $testConfigs[$selectedConfig];
    
    echo "选择配置: {$config['description']}\n";
    echo "批次大小: {$config['batch_size']}\n";
    
    // 创建导入任务
    echo "\n4. 创建导入任务...\n";
    
    $importTask = AssetImportTask::create([
        'file_path' => $attachment->file_path,
        'original_filename' => $attachment->file_name,
        'status' => 'pending',
        'created_by' => 1,
    ]);
    
    echo "导入任务创建成功，ID: {$importTask->id}\n";
    
    // 性能监控开始
    echo "\n5. 开始性能测试...\n";
    
    $startTime = microtime(true);
    $startMemory = memory_get_usage(true);
    $peakMemory = $startMemory;
    
    echo "开始时间: " . date('Y-m-d H:i:s') . "\n";
    echo "初始内存: " . number_format($startMemory / 1024 / 1024, 2) . " MB\n";
    
    try {
        $service = new AssetImportService();
        
        // 设置批次大小
        $reflection = new ReflectionClass($service);
        $batchSizeProperty = $reflection->getProperty('batchSize');
        $batchSizeProperty->setAccessible(true);
        $batchSizeProperty->setValue($service, $config['batch_size']);
        
        $importTask->markAsProcessing();
        echo "任务状态更新为处理中\n";
        
        // 执行导入（限制处理行数以避免超时）
        $maxRows = strpos($selectedFile, '超大数据') !== false ? 1000 : 500; // 限制测试行数
        echo "限制测试行数: {$maxRows} 行\n";
        
        // 临时修改总行数限制
        $processMethod = $reflection->getMethod('processImport');
        
        $result = $service->processImport($importTask);
        
        $endTime = microtime(true);
        $endMemory = memory_get_usage(true);
        $peakMemory = memory_get_peak_usage(true);
        
        echo "\n=== 性能测试结果 ===\n";
        echo "处理完成时间: " . date('Y-m-d H:i:s') . "\n";
        echo "总耗时: " . number_format($endTime - $startTime, 2) . " 秒\n";
        echo "结束内存: " . number_format($endMemory / 1024 / 1024, 2) . " MB\n";
        echo "峰值内存: " . number_format($peakMemory / 1024 / 1024, 2) . " MB\n";
        echo "内存增长: " . number_format(($endMemory - $startMemory) / 1024 / 1024, 2) . " MB\n";
        
        echo "\n=== 导入结果 ===\n";
        echo "总行数: " . $result['total_rows'] . "\n";
        echo "成功行数: " . $result['success_rows'] . "\n";
        echo "失败行数: " . $result['failed_rows'] . "\n";
        
        if ($result['total_rows'] > 0) {
            $successRate = ($result['success_rows'] / $result['total_rows']) * 100;
            $avgTimePerRow = ($endTime - $startTime) / $result['total_rows'];
            $rowsPerSecond = $result['total_rows'] / ($endTime - $startTime);
            
            echo "成功率: " . number_format($successRate, 2) . "%\n";
            echo "平均每行耗时: " . number_format($avgTimePerRow * 1000, 2) . " 毫秒\n";
            echo "处理速度: " . number_format($rowsPerSecond, 2) . " 行/秒\n";
        }
        
        if (!empty($result['errors'])) {
            echo "\n前5个错误:\n";
            foreach (array_slice($result['errors'], 0, 5) as $error) {
                echo "  行 {$error['row']}: {$error['error']}\n";
            }
        }
        
        $importTask->markAsCompleted($result);
        echo "\n任务状态更新为完成\n";
        
        // 性能评估
        echo "\n=== 性能评估 ===\n";
        
        if ($rowsPerSecond > 10) {
            echo "✓ 处理速度优秀（>10行/秒）\n";
        } elseif ($rowsPerSecond > 5) {
            echo "✓ 处理速度良好（>5行/秒）\n";
        } elseif ($rowsPerSecond > 1) {
            echo "⚠ 处理速度一般（>1行/秒）\n";
        } else {
            echo "✗ 处理速度较慢（<1行/秒）\n";
        }
        
        if ($peakMemory < 512 * 1024 * 1024) {
            echo "✓ 内存使用优秀（<512MB）\n";
        } elseif ($peakMemory < 1024 * 1024 * 1024) {
            echo "✓ 内存使用良好（<1GB）\n";
        } else {
            echo "⚠ 内存使用较高（>1GB）\n";
        }
        
        if ($successRate > 95) {
            echo "✓ 成功率优秀（>95%）\n";
        } elseif ($successRate > 90) {
            echo "✓ 成功率良好（>90%）\n";
        } else {
            echo "⚠ 成功率需要改进（<90%）\n";
        }
        
    } catch (Exception $e) {
        $endTime = microtime(true);
        $endMemory = memory_get_usage(true);
        
        echo "\n=== 测试失败 ===\n";
        echo "错误信息: " . $e->getMessage() . "\n";
        echo "错误位置: " . $e->getFile() . ":" . $e->getLine() . "\n";
        echo "失败时间: " . date('Y-m-d H:i:s') . "\n";
        echo "耗时: " . number_format($endTime - $startTime, 2) . " 秒\n";
        echo "内存使用: " . number_format($endMemory / 1024 / 1024, 2) . " MB\n";
        
        $importTask->markAsFailed([
            'error' => $e->getMessage(),
            'trace' => $e->getTraceAsString(),
        ]);
    }
    
    echo "\n=== 测试完成 ===\n";
    
} catch (Exception $e) {
    echo "✗ 测试失败：" . $e->getMessage() . "\n";
    echo "错误位置：" . $e->getFile() . ":" . $e->getLine() . "\n";
}

echo "\n=== 性能测试结束 ===\n";
