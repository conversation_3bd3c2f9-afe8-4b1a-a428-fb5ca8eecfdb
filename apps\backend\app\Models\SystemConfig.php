<?php

namespace App\Models;

use App\Models\BaseModel;
use Illuminate\Database\Eloquent\Casts\Attribute;

/**
 * 系统配置模型
 *
 * @property int $id
 * @property string $key 配置键
 * @property string|null $value 配置值
 * @property string $type 配置类型
 * @property string $group 配置分组
 * @property string|null $description 配置描述
 * @property bool $is_public 是否公开配置
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon $updated_at
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SystemConfig newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SystemConfig newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SystemConfig query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SystemConfig whereGroup($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SystemConfig whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SystemConfig whereKey($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SystemConfig whereValue($value)
 * @mixin \Eloquent
 */
class SystemConfig extends BaseModel
{

    // 禁用自动维护时间戳
    public $timestamps = false;

    protected $fillable = [
        'key',
        'value',
        'group',
    ];

    protected $casts = [];

    /**
     * 获取配置值（根据类型转换）
     */
    protected function value(): Attribute
    {
        return Attribute::make(
            get: function ($value) {
                if ($value === null) {
                    return null;
                }

                return $value;
            },
            set: function ($value) {
                if ($value === null) {
                    return null;
                }

                return (string) $value;
            }
        );
    }

    /**
     * 根据分组获取配置
     */
    public static function getByGroup(string $group): array
    {
        return static::where('group', $group)
            ->get()
            ->keyBy('key')
            ->map(fn($config) => $config->value)
            ->toArray();
    }

    /**
     * 设置配置值
     */
    public static function set(string $key, mixed $value, string $group = 'system'): void
    {
        static::updateOrCreate(
            ['key' => $key],
            [
                'value' => $value,
                'group' => $group,
            ]
        );
    }

    /**
     * 获取配置值
     */
    public static function get(string $key, mixed $default = null): mixed
    {
        $config = static::where('key', $key)->first();

        return $config ? $config->value : $default;
    }

    /**
     * 批量设置配置
     */
    public static function setMultiple(array $configs, string $group = 'system'): void
    {
        foreach ($configs as $key => $value) {
            static::set($key, $value, $group);
        }
    }

}
