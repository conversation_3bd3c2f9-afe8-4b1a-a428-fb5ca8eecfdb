<?php

namespace App\Models;

use App\Traits\HasAttachments;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\Models\BaseModel;

/**
 * @property int $id
 * @property string $name 资产名称
 * @property string|null $brand 资产品牌
 * @property string|null $model 规格型号
 * @property string|null $serial_number 序列号
 * @property array<array-key, mixed>|null $asset_category_ids 资产分类ID
 * @property string|null $asset_source 资产来源
 * @property string|null $asset_status 资产状态
 * @property string|null $asset_condition 成色
 * @property int|null $parent_id 主设备ID
 * @property string|null $region_code 区县代码
 * @property string|null $detailed_address 详细地址
 * @property int|null $start_date 启用日期
 * @property int|null $warranty_period 合同质保期(月)
 * @property int|null $warranty_alert 质保期预警(天)
 * @property int|null $maintenance_cycle 维护周期(天)
 * @property int|null $expected_years 预计使用年限(年)
 * @property array<array-key, mixed>|null $related_entities 相关主体信息
 * @property string|null $remark 备注
 * @property int|null $created_by 创建人
 * @property int|null $updated_by 更新人
 * @property int|null $created_at 创建时间
 * @property int|null $updated_at 更新时间
 * @property \Illuminate\Support\Carbon|null $deleted_at 删除时间
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Attachment> $attachments
 * @property-read int|null $attachments_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, Asset> $children
 * @property-read int $children_count
 * @property-read \App\Models\User|null $creator
 * @property-read mixed $asset_category
 * @property-read string $full_address
 * @property-read array $region_path
 * @property-read Asset|null $parent
 * @property-read \App\Models\Region|null $region
 * @property-read \App\Models\User|null $updater
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Asset accessory()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Asset active()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Asset main()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Asset newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Asset newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Asset onlyTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Asset query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Asset whereAssetCategoryIds($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Asset whereAssetCondition($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Asset whereAssetSource($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Asset whereAssetStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Asset whereBrand($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Asset whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Asset whereCreatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Asset whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Asset whereDetailedAddress($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Asset whereExpectedYears($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Asset whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Asset whereMaintenanceCycle($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Asset whereModel($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Asset whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Asset whereParentId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Asset whereRegionCode($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Asset whereRelatedEntities($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Asset whereRemark($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Asset whereSerialNumber($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Asset whereStartDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Asset whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Asset whereUpdatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Asset whereWarrantyAlert($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Asset whereWarrantyPeriod($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Asset withTrashed(bool $withTrashed = true)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Asset withoutTrashed()
 * @mixin \Eloquent
 */
class Asset extends BaseModel
{
    use HasAttachments, HasFactory, SoftDeletes;

    protected $fillable = [
        'name',
        'brand',
        'model',
        'serial_number',
        'asset_category_ids',
        'asset_source',
        'asset_status',
        'asset_condition',
        'parent_id',
        'region_code',
        'detailed_address',
        'start_date',
        'warranty_period',
        'warranty_alert',
        'maintenance_cycle',
        'expected_years',
        'related_entities',
        'remark',
        'created_by',
        'updated_by',
    ];

    protected $hidden = ['deleted_at'];

    protected $casts = [
        'asset_category_ids' => 'array',
        'start_date' => 'integer',
        'warranty_period' => 'integer',
        'warranty_alert' => 'integer',
        'maintenance_cycle' => 'integer',
        'expected_years' => 'integer',
        'related_entities' => 'array',
        'created_at' => 'integer',
        'updated_at' => 'integer',
    ];

    /**
     * 获取资产分类 - 访问器
     */
    public function getAssetCategoryAttribute()
    {
        // 由于asset_category_ids是数组字段，不能使用标准的Eloquent关系
        // 需要手动查询分类
        if (empty($this->asset_category_ids)) {
            return collect();
        }

        return Category::whereIn('id', $this->asset_category_ids)->get();
    }

    /**
     * 获取主设备
     */
    public function parent(): BelongsTo
    {
        return $this->belongsTo(Asset::class, 'parent_id');
    }

    /**
     * 获取附属设备
     */
    public function children(): HasMany
    {
        return $this->hasMany(Asset::class, 'parent_id');
    }

    /**
     * 获取创建人
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * 获取更新人
     */
    public function updater(): BelongsTo
    {
        return $this->belongsTo(User::class, 'updated_by');
    }

    /**
     * 获取地区信息
     */
    public function region(): BelongsTo
    {
        return $this->belongsTo(Region::class, 'region_code', 'ext_id');
    }

    /**
     * 获取完整地址
     */
    public function getFullAddressAttribute(): string
    {
        $region = $this->region;
        if (! $region) {
            return $this->detailed_address ?? '';
        }

        $path = $region->getFullPath();
        $regionNames = array_column($path, 'name');

        return implode('', $regionNames) . ($this->detailed_address ?? '');
    }

    /**
     * 获取地区路径
     */
    public function getRegionPathAttribute(): array
    {
        $region = $this->region;

        return $region ? $region->getFullPath() : [];
    }

    /**
     * 作用域：只查询启用的资产
     */
    public function scopeActive($query)
    {
        return $query->whereNotIn('asset_status', ['scrap_registered']);
    }

    /**
     * 作用域：只查询主设备（非附属设备）
     */
    public function scopeMain($query)
    {
        return $query->whereNull('parent_id');
    }

    /**
     * 作用域：只查询附属设备
     */
    public function scopeAccessory($query)
    {
        return $query->whereNotNull('parent_id');
    }

    /**
     * 判断是否有附属设备
     */
    public function hasChildren(): bool
    {
        return $this->children()->exists();
    }

    /**
     * 获取附属设备数量
     */
    public function getChildrenCountAttribute(): int
    {
        return $this->children()->count();
    }
}
