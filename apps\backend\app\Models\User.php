<?php

namespace App\Models;

use App\Traits\HasAttachments;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Laravel\Sanctum\HasApiTokens;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * @property int $id
 * @property string $account 用户账号
 * @property string $password 密码
 * @property string|null $nickname 用户昵称
 * @property string|null $email 邮箱
 * @property string $status 状态
 * @property bool $is_super_admin 是否超级管理员
 * @property \Illuminate\Support\Carbon|null $created_at 创建时间
 * @property \Illuminate\Support\Carbon|null $updated_at 更新时间
 * @property \Illuminate\Support\Carbon|null $deleted_at 删除时间
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Attachment> $attachments
 * @property-read int|null $attachments_count
 * @property-read \Illuminate\Notifications\DatabaseNotificationCollection<int, \Illuminate\Notifications\DatabaseNotification> $notifications
 * @property-read int|null $notifications_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Role> $roles
 * @property-read int|null $roles_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \Laravel\Sanctum\PersonalAccessToken> $tokens
 * @property-read int|null $tokens_count
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User onlyTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User whereAccount($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User whereEmail($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User whereIsSuperAdmin($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User whereNickname($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User wherePassword($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User withTrashed(bool $withTrashed = true)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User withoutTrashed()
 * @mixin \Eloquent
 */
class User extends Authenticatable
{
    use HasApiTokens, HasAttachments, Notifiable, SoftDeletes;


    // 设置时间格式为 Unix 时间戳
    protected $dateFormat = 'U';

    /**
     * 可批量赋值的属性
     *
     * @var list<string>
     */
    protected $fillable = [
        'account',
        'nickname',
        'email',
        'avatar',
        'password',
        'status',
        'is_super_admin',
    ];

    /**
     * 应该被隐藏的属性
     *
     * @var list<string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * 应该被转换成原生类型的属性
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
            'is_super_admin' => 'boolean',
        ];
    }

    /**
     * 获取用户的角色
     */
    public function roles(): BelongsToMany
    {
        return $this->belongsToMany(Role::class, 'user_roles');
    }

    /**
     * 检查用户是否有指定角色
     */
    public function hasRole(string $roleName): bool
    {
        return $this->roles()->where('name', $roleName)->exists();
    }

    /**
     * 分配角色给用户
     */
    public function assignRole($roles): void
    {
        if (is_string($roles)) {
            $roles = [$roles];
        }

        $roleIds = Role::whereIn('name', $roles)->pluck('id')->toArray();
        $this->roles()->syncWithoutDetaching($roleIds);
    }

    /**
     * 移除用户的角色
     */
    public function removeRole($roles): void
    {
        if (is_string($roles)) {
            $roles = [$roles];
        }

        $roleIds = Role::whereIn('name', $roles)->pluck('id')->toArray();
        $this->roles()->detach($roleIds);
    }

    /**
     * 同步用户角色
     */
    public function syncRoles($roles): void
    {
        if (is_string($roles)) {
            $roles = [$roles];
        }

        $roleIds = Role::whereIn('name', $roles)->pluck('id')->toArray();
        $this->roles()->sync($roleIds);
    }

    /**
     * 检查是否为超级管理员
     */
    public function isSuperAdmin(): bool
    {
        return $this->is_super_admin;
    }

    /**
     * 获取用户通过角色拥有的所有菜单权限
     */
    public function getAllPermissions()
    {
        // 超级管理员拥有所有权限
        if ($this->isSuperAdmin()) {
            return RoleMenuPermission::with(['menu', 'menuPermission'])->get();
        }

        $roleIds = $this->roles()->pluck('roles.id');

        return RoleMenuPermission::whereIn('role_id', $roleIds)
            ->with(['menu', 'menuPermission'])
            ->get();
    }

    /**
     * 获取用户有权限访问的菜单列表
     */
    public function getAccessibleMenus()
    {
        // 超级管理员可以访问所有菜单
        if ($this->isSuperAdmin()) {
            return Menu::with('permissions')->get();
        }

        $roleIds = $this->roles()->pluck('roles.id');

        \Log::error('User getAccessibleMenus: ', [
            'roleIds' => $roleIds
        ]);

        return Menu::whereIn('id', function ($query) use ($roleIds) {
            $query->select('menu_id')
                ->from('role_menu_permissions')
                ->whereIn('role_id', $roleIds);
        })->with('permissions')->get();
    }

    /**
     * 检查用户是否有指定菜单的访问权限
     */
    public function hasMenuAccess(int $menuId): bool
    {
        // 超级管理员拥有所有菜单访问权限
        if ($this->isSuperAdmin()) {
            return true;
        }

        $roleIds = $this->roles()->pluck('roles.id');

        return RoleMenuPermission::whereIn('role_id', $roleIds)
            ->where('menu_id', $menuId)
            ->exists();
    }

    /**
     * 检查用户是否有指定菜单的特定权限
     */
    public function hasMenuPermission(int $menuId, int $menuPermissionId): bool
    {
        // 超级管理员拥有所有菜单权限
        if ($this->isSuperAdmin()) {
            return true;
        }

        $roleIds = $this->roles()->pluck('roles.id');

        return RoleMenuPermission::whereIn('role_id', $roleIds)
            ->where('menu_id', $menuId)
            ->where('menu_permission_id', $menuPermissionId)
            ->exists();
    }

    /**
     * 检查用户是否有指定菜单的权限标识
     */
    public function hasMenuPermissionByMark(string $menuName, string $authMark): bool
    {
        // 超级管理员拥有所有菜单权限
        if ($this->isSuperAdmin()) {
            return true;
        }

        $roleIds = $this->roles()->pluck('roles.id');

        return RoleMenuPermission::whereIn('role_id', $roleIds)
            ->whereHas('menu', function ($query) use ($menuName) {
                $query->where('name', $menuName);
            })
            ->whereHas('menuPermission', function ($query) use ($authMark) {
                $query->where('auth_mark', $authMark);
            })
            ->exists();
    }
}
