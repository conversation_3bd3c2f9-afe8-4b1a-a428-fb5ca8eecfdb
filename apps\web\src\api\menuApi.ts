import request from '@/utils/http'
import { menuDataToRouter } from '@/router/utils/menuToRouter'
import { AppRouteRecord } from '@/types/router'

// 后端返回的菜单数据类型
interface BackendMenuItem extends AppRouteRecord {
  parent_id: number | null
}

interface MenuResponse {
  menuList: BackendMenuItem[]
}

/**
 * 构建树形结构
 */
const buildTree = (flatData: BackendMenuItem[]): BackendMenuItem[] => {
  const map = new Map<number, BackendMenuItem>()
  const tree: BackendMenuItem[] = []

  // 初始化所有节点，每个节点都有children数组
  flatData.forEach((item) => {
    map.set(item.id!, { ...item, children: item.children || [] })
  })

  // 构建树形结构
  flatData.forEach((item) => {
    const node = map.get(item.id!)!
    if (!item.parent_id || item.parent_id === null) {
      tree.push(node)
    } else {
      const parent = map.get(item.parent_id)
      if (parent) {
        if (!parent.children) {
          parent.children = []
        }
        parent.children.push(node)
      }
    }
  })

  return tree
}

// 菜单接口
export const menuService = {
  async getMenuList(): Promise<{ menuList: AppRouteRecord[] }> {
    try {
      // 调用真实的后端API
      const response = await request.get<MenuResponse>({
        url: '/admin/menus'
      })

      // 构建树形结构
      const treeData = buildTree(response.menuList)

      // 处理菜单数据，转换为路由格式
      const menuList = treeData.map((route) => menuDataToRouter(route))

      return { menuList }
    } catch (error) {
      throw error instanceof Error ? error : new Error('获取菜单失败')
    }
  }
}
