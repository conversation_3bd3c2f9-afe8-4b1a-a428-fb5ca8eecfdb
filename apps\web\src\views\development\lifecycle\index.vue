<template>
  <div class="lifecycle-page art-full-height art-page-view">
    <!-- 搜索栏 -->
    <ArtSearchBar
      v-model:filter="searchFormState"
      :items="searchFormItems"
      @reset="handleReset"
      @search="handleSearch"
      :showExpand="true"
    />

    <ElCard shadow="never" class="art-table-card">
      <!-- 表格头部 -->
      <ArtTableHeader v-model:columns="columnChecks" @refresh="refreshAll">
        <template #left>
          <ElButton
            type="primary"
            @click="showLifecycleDialog('add')"
            v-ripple
            v-if="hasAuth('add')"
          >
            新增生命周期
          </ElButton>
        </template>
      </ArtTableHeader>

      <!-- 表格 -->
      <ArtTable
        :loading="isLoading"
        :data="tableData"
        :columns="columns"
        :pagination="paginationState"
        :table-config="tableConfig"
        :layout="{ marginTop: 10 }"
        @pagination:size-change="onPageSizeChange"
        @pagination:current-change="onCurrentPageChange"
      >
        <!-- 关联资产列插槽 -->
        <template #asset_id="{ row }">
          <span>{{ row.asset?.name || '-' }}</span>
        </template>
        <!-- 操作列插槽 -->
        <template #operation="{ row }">
          <div style="display: flex; gap: 5px">
            <ArtButtonTable type="primary" @click="showFollowUpDialog(row)"> 跟进 </ArtButtonTable>
            <ArtButtonTable
              type="edit"
              @click="showLifecycleDialog('edit', row)"
              v-if="hasAuth('edit')"
            />
            <ArtButtonTable type="view" @click="showDetail()" v-if="hasAuth('view')" />
            <ArtButtonTable type="delete" @click="handleDelete(row)" v-if="hasAuth('delete')" />
          </div>
        </template>
      </ArtTable>

      <!-- 生命周期对话框 -->
      <LifecycleDialog
        v-model="lifecycleDialogVisible"
        :type="dialogType"
        :data="currentLifecycle"
        @success="handleLifecycleSuccess"
      />

      <!-- 跟进信息对话框 -->
      <FollowUpDialog
        v-model="followUpDialogVisible"
        :lifecycleId="currentLifecycleId"
        @success="handleFollowUpSuccess"
      />
    </ElCard>
  </div>
</template>

<script setup lang="ts">
  defineOptions({ name: 'Lifecycle' })

  // Vue 核心
  import { ref, computed, onMounted } from 'vue'

  // UI 框架
  import { ElButton, ElMessage, ElMessageBox } from 'element-plus'

  // 工具函数
  import { formatDate } from '@/utils/dataprocess/format'

  // 内部 hooks
  import { useTable } from '@/composables/useTable'
  import { useDictionaryStore } from '@/store/modules/dictionary'
  import { useAuth } from '@/composables/useAuth'

  // 内部组件
  import LifecycleDialog from './components/LifecycleDialog.vue'
  import FollowUpDialog from './components/FollowUpDialog.vue'
  import ArtButtonTable from '@/components/custom/art-button-table/index.vue'

  // API
  import { getLifecycleList, deleteLifecycle, getLifecycleDetail } from '@/api/admin/lifecycleApi'
  import type { Lifecycle } from '@/types/api'
  import { getEntityList } from '@/api/admin/entityApi'
  import { getUserList } from '@/api/admin/userApi'

  // 类型定义
  import type { SearchFormItem } from '@/types'

  // 表格配置
  const tableConfig = {
    rowKey: 'id'
  }

  // 权限控制
  const { hasAuth } = useAuth()

  // 字典数据
  const dictionaryStore = useDictionaryStore()
  const lifecycleTypes = ref<any[]>([])
  const userList = ref<any[]>([])
  const entityList = ref<any[]>([])

  // 对话框
  const lifecycleDialogVisible = ref(false)
  const followUpDialogVisible = ref(false)
  const dialogType = ref<'add' | 'edit'>('add')
  const currentLifecycle = ref<Lifecycle | null>(null)
  const currentLifecycleId = ref<number>(0)

  // 搜索表单状态
  const searchFormState = ref({
    type: null,
    initiator: null,
    acceptanceEntity: null
  })

  // 搜索表单配置
  const searchFormItems = computed<SearchFormItem[]>(() => [
    {
      label: '类型',
      prop: 'type',
      type: 'select',
      config: {
        clearable: true,
        placeholder: '请选择类型'
      },
      options: lifecycleTypes.value.map((item: any) => ({
        label: item.value,
        value: item.code
      }))
    },
    {
      label: '发起人',
      prop: 'initiator',
      type: 'select',
      config: {
        clearable: true,
        placeholder: '请选择发起人'
      },
      options: userList.value.map((item: any) => ({
        label: item.nickname || item.nickName || item.realName,
        value: item.id
      }))
    },
    {
      label: '验收主体',
      prop: 'acceptanceEntity',
      type: 'select',
      config: {
        clearable: true,
        placeholder: '请选择验收主体'
      },
      options: entityList.value.map((item: any) => ({
        label: item.name,
        value: item.id
      }))
    }
  ])

  // 使用 useTable composable
  const {
    tableData,
    columns,
    columnChecks,
    isLoading,
    paginationState,
    refreshAll,
    refreshAfterCreate,
    refreshAfterUpdate,
    refreshAfterRemove,
    onPageSizeChange,
    onCurrentPageChange,
    searchState,
    searchData,
    resetSearch
  } = useTable<Lifecycle>({
    core: {
      apiFn: async (params: any) => {
        // 处理参数格式
        const searchParams: any = {}
        if (params.type) {
          searchParams.type = params.type
        }
        if (params.initiator) {
          searchParams.initiator_id = params.initiator
        }
        if (params.acceptanceEntity) {
          searchParams.acceptance_entity_id = params.acceptanceEntity
        }

        const response = await getLifecycleList({
          page: params.current,
          per_page: params.size,
          ...searchParams
        })

        return {
          records: response.data || [],
          total: response.meta?.total || 0,
          current: params.current,
          size: params.size
        }
      },
      apiParams: {
        current: 1,
        size: 10,
        type: '',
        initiator: '',
        acceptanceEntity: ''
      },
      columnsFactory: () => [
        { prop: 'id', label: '周期表ID', width: 100 },
        {
          prop: 'asset_id',
          label: '关联资产',
          width: 200,
          showOverflowTooltip: true,
          useSlot: true,
          slotName: 'asset_id'
        },
        {
          prop: 'type',
          label: '类型',
          width: 120,
          formatter: (row: Lifecycle) => {
            const type = lifecycleTypes.value.find((item: any) => item.code === row.type)
            return type ? type.value : row.type
          }
        },
        {
          prop: 'date',
          label: '日期',
          width: 120,
          formatter: (row: Lifecycle) => (row.date ? formatDate(row.date, 'YYYY-MM-DD') : '-')
        },
        {
          prop: 'initiator',
          label: '发起人',
          width: 100,
          formatter: (row: Lifecycle) => {
            const user = userList.value.find((item: any) => item.id === row.initiator)
            return user ? user.nickname || user.nickName : '-'
          }
        },
        { prop: 'content', label: '内容', minWidth: 200, showOverflowTooltip: true },
        {
          prop: 'acceptance_entity_name',
          label: '验收主体',
          width: 150,
          formatter: (row: Lifecycle) => row.acceptance_entity_name || '-'
        },
        {
          prop: 'acceptance_personnel_name',
          label: '验收人员',
          width: 100,
          formatter: (row: Lifecycle) => row.acceptance_personnel_name || '-'
        },
        {
          prop: 'acceptance_time',
          label: '验收日期',
          width: 120,
          formatter: (row: Lifecycle) =>
            row.acceptance_time ? formatDate(row.acceptance_time, 'YYYY-MM-DD') : '-'
        },
        {
          prop: 'operation',
          label: '操作',
          width: 250,
          fixed: 'right',
          useSlot: true
        }
      ],
      immediate: true // 确保初始时加载数据
    }
  })

  // 初始化 - 只为搜索表单加载必要的数据
  onMounted(async () => {
    // 搜索表单需要这些数据
    await loadDictionaries()
    await loadUserList()
    await loadEntityList()
  })

  // 加载字典数据
  const loadDictionaries = async () => {
    const data = await dictionaryStore.fetchItemsByCode('lifecycle_config')
    lifecycleTypes.value = data || []
  }

  // 加载用户列表
  const loadUserList = async () => {
    const response = await getUserList({ current: 1, size: 1000 })
    userList.value = response.data || []
  }

  // 加载主体列表
  const loadEntityList = async () => {
    const response = await getEntityList({ current: 1, size: 1000 })
    entityList.value = response.data || []
  }

  // 处理搜索
  const handleSearch = () => {
    searchState.type = searchFormState.value.type
    searchState.initiator = searchFormState.value.initiator
    searchState.acceptanceEntity = searchFormState.value.acceptanceEntity
    searchData()
  }

  // 处理重置
  const handleReset = () => {
    searchFormState.value = {
      type: null,
      initiator: null,
      acceptanceEntity: null
    }
    resetSearch()
  }

  // 显示生命周期对话框
  const showLifecycleDialog = async (type: 'add' | 'edit', row?: Lifecycle) => {
    dialogType.value = type
    if (type === 'edit' && row?.id) {
      // 编辑时获取详情数据（包含附件详情）
      const detail = await getLifecycleDetail(row.id)
      currentLifecycle.value = detail
    } else {
      currentLifecycle.value = null
    }
    lifecycleDialogVisible.value = true
  }

  // 显示跟进对话框
  const showFollowUpDialog = (row: Lifecycle) => {
    currentLifecycleId.value = row.id!
    followUpDialogVisible.value = true
  }

  // 显示详情
  const showDetail = () => {
    // TODO: 实现详情页面
    ElMessage.info('详情功能开发中')
  }

  // 删除
  const handleDelete = async (row: Lifecycle) => {
    try {
      await ElMessageBox.confirm('确定要删除该生命周期记录吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
      await deleteLifecycle(row.id!)
      ElMessage.success('删除成功')
      refreshAfterRemove()
    } catch {
      // 用户取消删除，不需要处理
    }
  }

  // 生命周期对话框成功
  const handleLifecycleSuccess = () => {
    if (dialogType.value === 'add') {
      refreshAfterCreate()
    } else {
      refreshAfterUpdate()
    }
  }

  // 跟进对话框成功
  const handleFollowUpSuccess = () => {
    // 可以考虑是否需要刷新数据
  }
</script>

<style lang="scss" scoped>
  .lifecycle-page {
    :deep(.el-card__body) {
      display: flex;
      flex-direction: column;
      height: 100%;
    }
  }
</style>
