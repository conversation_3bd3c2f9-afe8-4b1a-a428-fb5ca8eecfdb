<template>
  <ElDialog
    v-model="visible"
    title="选择联系人"
    width="700px"
    :close-on-click-modal="false"
    @closed="handleClosed"
  >
    <!-- 搜索栏 -->
    <div class="search-bar">
      <ElInput
        v-model="searchKeyword"
        placeholder="搜索姓名或电话"
        clearable
        class="search-input"
        @input="handleSearch"
      >
        <template #prefix>
          <ElIcon><Search /></ElIcon>
        </template>
      </ElInput>
    </div>

    <!-- 联系人表格 -->
    <ElTable
      :data="filteredContacts"
      v-loading="loading"
      height="400"
      stripe
      highlight-current-row
      @row-dblclick="handleSelect"
    >
      <ElTableColumn prop="name" label="姓名" width="120" />
      <ElTableColumn prop="phone" label="电话" width="140" />
      <ElTableColumn prop="position" label="职位" width="120" />
      <ElTableColumn prop="department" label="部门" />
      <ElTableColumn label="操作" width="100" fixed="right" align="center">
        <template #default="{ row }">
          <ElButton type="primary" link @click="handleSelect(row)"> 选择 </ElButton>
        </template>
      </ElTableColumn>
    </ElTable>

    <!-- 提示信息 -->
    <div class="tips">
      <ElIcon><InfoFilled /></ElIcon>
      <span>双击行或点击"选择"按钮快速填充联系人信息</span>
    </div>

    <template #footer>
      <ElButton @click="handleManualInput">手动填写</ElButton>
      <ElButton @click="visible = false">取消</ElButton>
    </template>
  </ElDialog>
</template>

<script setup lang="ts">
  defineOptions({ name: 'ContactSelectDialog' })

  import { ref, computed } from 'vue'
  import { Search, InfoFilled } from '@element-plus/icons-vue'
  import type { Contact } from '@/types/api/entity'

  interface Props {
    modelValue: boolean
    contacts: Contact[]
    loading?: boolean
  }

  const props = withDefaults(defineProps<Props>(), {
    loading: false
  })

  const emit = defineEmits<{
    'update:modelValue': [value: boolean]
    select: [contact: Contact | null]
  }>()

  // 计算属性
  const visible = computed({
    get: () => props.modelValue,
    set: (val) => emit('update:modelValue', val)
  })

  // 搜索关键词
  const searchKeyword = ref('')

  // 过滤后的联系人列表
  const filteredContacts = computed(() => {
    if (!searchKeyword.value) {
      return props.contacts
    }

    const keyword = searchKeyword.value.toLowerCase()
    return props.contacts.filter(
      (contact) => contact.name.toLowerCase().includes(keyword) || contact.phone?.includes(keyword)
    )
  })

  // 选择联系人
  const handleSelect = (contact: Contact) => {
    emit('select', contact)
    visible.value = false
  }

  // 手动填写
  const handleManualInput = () => {
    emit('select', null)
    visible.value = false
  }

  // 搜索处理
  const handleSearch = () => {
    // 搜索逻辑已在 computed 中处理
  }

  // 对话框关闭后重置
  const handleClosed = () => {
    searchKeyword.value = ''
  }
</script>

<style lang="scss" scoped>
  .search-bar {
    margin-bottom: 16px;

    .search-input {
      width: 300px;
    }
  }

  .tips {
    display: flex;
    gap: 8px;
    align-items: center;
    margin-top: 12px;
    font-size: 12px;
    color: var(--el-text-color-secondary);

    .el-icon {
      color: var(--el-color-primary);
    }
  }

  :deep(.el-table) {
    .el-table__row {
      cursor: pointer;

      &:hover {
        background-color: var(--el-fill-color-light) !important;
      }
    }
  }
</style>
