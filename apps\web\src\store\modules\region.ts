import { defineStore } from 'pinia'
import { ref } from 'vue'
import type { RegionTreeItem, RegionSearchItem, RegionPathResponse } from '@/types/api'
import {
  getRegionTree,
  getRegionChildren,
  getRegionPath,
  searchRegions,
  getProvinces,
  getCities,
  getDistricts
} from '@/api/admin/regionApi'

export const useRegionStore = defineStore('region', () => {
  // 状态
  const regionTree = ref<RegionTreeItem[]>([])
  const provinces = ref<RegionTreeItem[]>([])
  const loading = ref(false)

  // 缓存时间：30分钟
  const CACHE_DURATION = 30 * 60 * 1000
  const treeLoadTime = ref(0)
  const provincesLoadTime = ref(0)

  /**
   * 检查缓存是否过期
   */
  const isCacheExpired = (loadTime: number): boolean => {
    return Date.now() - loadTime > CACHE_DURATION
  }

  /**
   * 获取完整地区树（带缓存）
   */
  const fetchRegionTree = async (forceRefresh = false): Promise<RegionTreeItem[]> => {
    // 如果有缓存且未过期，直接返回
    if (!forceRefresh && regionTree.value.length > 0 && !isCacheExpired(treeLoadTime.value)) {
      return regionTree.value
    }

    loading.value = true
    try {
      const data = await getRegionTree()
      regionTree.value = data
      treeLoadTime.value = Date.now()
      return data
    } catch (error) {
      console.error('获取地区树失败：', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  /**
   * 获取省份列表（带缓存）
   */
  const fetchProvinces = async (forceRefresh = false): Promise<RegionTreeItem[]> => {
    // 如果有缓存且未过期，直接返回
    if (!forceRefresh && provinces.value.length > 0 && !isCacheExpired(provincesLoadTime.value)) {
      return provinces.value
    }

    loading.value = true
    try {
      const data = await getProvinces()
      provinces.value = data
      provincesLoadTime.value = Date.now()
      return data
    } catch (error) {
      console.error('获取省份列表失败：', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  /**
   * 获取子地区（无缓存）
   */
  const fetchChildren = async (parentId: number | string): Promise<RegionTreeItem[]> => {
    try {
      return await getRegionChildren(parentId)
    } catch (error) {
      console.error('获取子地区失败：', error)
      throw error
    }
  }

  /**
   * 获取城市列表（无缓存）
   */
  const fetchCities = async (provinceId: number | string): Promise<RegionTreeItem[]> => {
    try {
      return await getCities(provinceId)
    } catch (error) {
      console.error('获取城市列表失败：', error)
      throw error
    }
  }

  /**
   * 获取区县列表（无缓存）
   */
  const fetchDistricts = async (cityId: number | string): Promise<RegionTreeItem[]> => {
    try {
      return await getDistricts(cityId)
    } catch (error) {
      console.error('获取区县列表失败：', error)
      throw error
    }
  }

  /**
   * 根据地区代码获取路径
   */
  const fetchRegionPath = async (code: string): Promise<RegionPathResponse> => {
    try {
      return await getRegionPath(code)
    } catch (error) {
      console.error('获取地区路径失败：', error)
      throw error
    }
  }

  /**
   * 搜索地区
   */
  const searchRegionByKeyword = async (params: {
    keyword: string
    limit?: number
    deep?: number
  }): Promise<RegionSearchItem[]> => {
    try {
      return await searchRegions(params)
    } catch (error) {
      console.error('搜索地区失败：', error)
      throw error
    }
  }

  /**
   * 根据地区代码查找地区名称
   */
  const getRegionNameByCode = async (code: string): Promise<string> => {
    try {
      const pathData = await fetchRegionPath(code)
      return pathData.full_name
    } catch (error) {
      console.error('获取地区名称失败：', error)
      return code
    }
  }

  /**
   * 根据地区代码数组查找级联路径
   */
  const getRegionPathByCodes = (
    codes: string[],
    tree: RegionTreeItem[] = regionTree.value
  ): string[] => {
    if (!codes || codes.length === 0) return []

    const findPath = (
      nodes: RegionTreeItem[],
      targetCodes: string[],
      currentPath: string[] = []
    ): string[] => {
      for (const node of nodes) {
        const newPath = [...currentPath, node.value]

        // 如果当前节点匹配目标路径中的某个代码
        const matchIndex = targetCodes.findIndex((code) => code === node.value)
        if (matchIndex !== -1) {
          // 如果是最后一个代码，返回完整路径
          if (matchIndex === targetCodes.length - 1) {
            return newPath
          }
          // 否则继续在子节点中查找
          if (node.children) {
            const result = findPath(node.children, targetCodes, newPath)
            if (result.length > 0) return result
          }
        }

        // 在子节点中递归查找
        if (node.children) {
          const result = findPath(node.children, targetCodes, currentPath)
          if (result.length > 0) return result
        }
      }
      return []
    }

    return findPath(tree, codes)
  }

  /**
   * 清除缓存
   */
  const clearCache = () => {
    regionTree.value = []
    provinces.value = []
    treeLoadTime.value = 0
    provincesLoadTime.value = 0
  }

  return {
    // 状态
    regionTree,
    provinces,
    loading,

    // 方法
    fetchRegionTree,
    fetchProvinces,
    fetchChildren,
    fetchCities,
    fetchDistricts,
    fetchRegionPath,
    searchRegionByKeyword,
    getRegionNameByCode,
    getRegionPathByCodes,
    clearCache,

    // 计算属性
    hasTreeData: () => regionTree.value.length > 0,
    hasProvincesData: () => provinces.value.length > 0
  }
})
