<template>
  <ElDialog
    v-model="visible"
    :title="type === 'add' ? '新增生命周期' : '编辑生命周期'"
    width="70%"
    align-center
    destroy-on-close
    @close="handleClose"
  >
    <ElForm ref="formRef" :model="formData" :rules="rules" label-width="120px">
      <ElRow :gutter="20">
        <ElCol :span="12">
          <ElFormItem label="关联资产" prop="asset_id">
            <NullableSelect
              v-model="formData.asset_id"
              placeholder="请选择资产"
              filterable
              clearable
            >
              <ElOption
                v-for="asset in assetList"
                :key="asset.id"
                :label="asset.name"
                :value="asset.id"
              />
            </NullableSelect>
          </ElFormItem>
        </ElCol>
        <ElCol :span="12">
          <ElFormItem label="类型" prop="type">
            <NullableSelect v-model="formData.type" placeholder="请选择类型">
              <ElOption
                v-for="item in lifecycleTypes"
                :key="item.code"
                :label="item.value"
                :value="item.code"
              />
            </NullableSelect>
          </ElFormItem>
        </ElCol>
      </ElRow>
      <ElRow :gutter="20">
        <ElCol :span="12">
          <ElFormItem label="日期" prop="date">
            <ElDatePicker
              v-model="formData.date"
              type="date"
              placeholder="选择日期"
              style="width: 100%"
              :value-format="'X'"
            />
          </ElFormItem>
        </ElCol>
        <ElCol :span="12">
          <ElFormItem label="发起人" prop="initiator">
            <NullableSelect
              v-model="formData.initiator"
              placeholder="请选择发起人"
              filterable
              clearable
            >
              <ElOption
                v-for="user in userList"
                :key="user.id"
                :label="user.nickname || user.nickName || user.realName"
                :value="user.id"
              />
            </NullableSelect>
          </ElFormItem>
        </ElCol>
      </ElRow>
      <ElRow :gutter="20">
        <ElCol :span="24">
          <ElFormItem label="内容" prop="content">
            <ElInput
              v-model="formData.content"
              type="textarea"
              :rows="4"
              placeholder="请输入内容"
            />
          </ElFormItem>
        </ElCol>
      </ElRow>
      <ElRow :gutter="20">
        <ElCol :span="12">
          <ElFormItem label="协助人员" prop="assistants">
            <ElSelect
              v-model="formData.assistants"
              :placeholder="formData.initiator ? '请选择协助人员' : '请先选择发起人'"
              multiple
              filterable
              clearable
              collapse-tags
              collapse-tags-tooltip
              :disabled="!formData.initiator"
            >
              <ElOption
                v-for="user in filteredAssistantsList"
                :key="user.id"
                :label="user.nickname || user.nickName || user.realName"
                :value="user.id"
              />
            </ElSelect>
          </ElFormItem>
        </ElCol>
        <ElCol :span="12">
          <ElFormItem label="验收主体" prop="acceptance_entity">
            <NullableSelect
              v-model="formData.acceptance_entity"
              placeholder="请选择验收主体"
              filterable
              clearable
              @change="handleEntityChange"
            >
              <ElOption
                v-for="entity in entityList"
                :key="entity.id"
                :label="entity.name"
                :value="entity.id"
              />
            </NullableSelect>
          </ElFormItem>
        </ElCol>
      </ElRow>
      <ElRow :gutter="20">
        <ElCol :span="12">
          <ElFormItem label="验收人员" prop="acceptance_personnel">
            <NullableSelect
              v-model="formData.acceptance_personnel"
              placeholder="请先选择验收主体"
              filterable
              clearable
              :disabled="!formData.acceptance_entity"
            >
              <ElOption
                v-for="contact in contactList"
                :key="contact.id"
                :label="contact.name"
                :value="contact.id"
              />
            </NullableSelect>
          </ElFormItem>
        </ElCol>
        <ElCol :span="12">
          <ElFormItem label="验收日期" prop="acceptance_time">
            <ElDatePicker
              v-model="formData.acceptance_time"
              type="date"
              placeholder="选择验收日期"
              style="width: 100%"
              :value-format="'X'"
            />
          </ElFormItem>
        </ElCol>
      </ElRow>
      <ElRow :gutter="20">
        <ElCol :span="24">
          <ElFormItem label="相关文件">
            <AttachmentUpload
              v-model="formData.attachments"
              :attachments="attachmentDetails"
              :limit="10"
            />
          </ElFormItem>
        </ElCol>
      </ElRow>
    </ElForm>
    <template #footer>
      <span class="dialog-footer">
        <ElButton @click="handleClose">取消</ElButton>
        <ElButton type="primary" @click="handleSubmit">确定</ElButton>
      </span>
    </template>
  </ElDialog>
</template>

<script setup lang="ts">
  defineOptions({ name: 'LifecycleDialog' })

  // Vue 核心
  import { ref, reactive, watch, computed } from 'vue'

  // UI 框架
  import { ElMessage, ElOption } from 'element-plus'
  import type { FormInstance, FormRules } from 'element-plus'

  // API
  import {
    createLifecycle,
    updateLifecycle,
    getAcceptancePersonnel
  } from '@/api/admin/lifecycleApi'
  import type { Lifecycle, Asset } from '@/types/api'
  import { getEntityList } from '@/api/admin/entityApi'
  import { getUserList } from '@/api/admin/userApi'
  import { getAssetList } from '@/api/admin/assetApi'

  // Store
  import { useDictionaryStore } from '@/store/modules/dictionary'

  // 组件
  import AttachmentUpload from '@/components/custom/upload/Attachment.vue'
  import NullableSelect from '@/components/custom/nullable-select/index.vue'

  // Props
  const props = defineProps<{
    type: 'add' | 'edit'
    data?: Lifecycle | null
  }>()

  // Emits
  const emit = defineEmits<{
    success: []
  }>()

  // 使用 defineModel 简化 v-model
  const visible = defineModel<boolean>({ default: false })

  // 过滤后的协助人员列表（排除已选择的发起人）
  const filteredAssistantsList = computed(() => {
    if (!formData.initiator) return userList.value
    return userList.value.filter((user: any) => user.id !== formData.initiator)
  })

  // 表单相关
  const formRef = ref<FormInstance>()
  const formData = reactive<Partial<Lifecycle>>({
    id: undefined,
    asset_id: null,
    type: null,
    date: '',
    initiator: null,
    content: '',
    assistants: [],
    acceptance_entity: null,
    acceptance_personnel: null,
    acceptance_time: '',
    attachments: []
  })

  // 表单规则
  const rules: FormRules = {
    type: [{ required: true, message: '请选择类型', trigger: 'change' }],
    date: [{ required: true, message: '请选择日期', trigger: 'change' }],
    initiator: [{ required: true, message: '请选择发起人', trigger: 'change' }],
    content: [{ required: true, message: '请输入内容', trigger: 'blur' }],
    assistants: [{ required: true, message: '请选择协助人员', trigger: 'change' }],
    acceptance_entity: [{ required: true, message: '请选择验收主体', trigger: 'change' }],
    acceptance_personnel: [{ required: true, message: '请选择验收人员', trigger: 'change' }],
    acceptance_time: [{ required: true, message: '请选择验收日期', trigger: 'change' }]
  }

  // 数据源
  const dictionaryStore = useDictionaryStore()
  const lifecycleTypes = ref<any[]>([])
  const userList = ref<any[]>([])
  const entityList = ref<any[]>([])
  const contactList = ref<any[]>([])
  const attachmentDetails = ref<any[]>([])
  const assetList = ref<Asset[]>([])

  // 重置表单
  const resetForm = () => {
    Object.assign(formData, {
      id: undefined,
      asset_id: null,
      type: null,
      date: '',
      initiator: null,
      content: '',
      assistants: [],
      acceptance_entity: null,
      acceptance_personnel: null,
      acceptance_time: '',
      attachments: []
    })
    contactList.value = []
    attachmentDetails.value = []
  }

  // 监听props变化
  watch(
    () => props.data,
    (newData) => {
      if (newData) {
        // 直接赋值，前后端都使用 snake_case
        Object.assign(formData, newData)

        // 如果有验收主体，加载对应的联系人
        if (formData.acceptance_entity) {
          loadContacts(formData.acceptance_entity)
        }

        // 处理附件数据
        if (newData.attachments && newData.attachments.length > 0) {
          // 设置附件详情供组件显示
          attachmentDetails.value = newData.attachments
          // formData.attachments 应该是 ID 数组
          formData.attachments = newData.attachments.map((item: any) => item.id)
        } else {
          attachmentDetails.value = []
          formData.attachments = []
        }
      } else {
        resetForm()
      }
    },
    { immediate: true }
  )

  // 监听弹窗显示状态
  watch(visible, async (newVal) => {
    if (newVal) {
      // 字典数据只在第一次显示或数据为空时加载（变化较少）
      if (lifecycleTypes.value.length === 0) {
        await loadDictionaries()
      }
      // 每次打开弹窗都重新加载用户列表和实体列表，确保数据最新
      await loadUserList()
      await loadEntityList()
      // 加载资产列表
      await loadAssets()
    }
  })

  // 监听发起人变化，自动移除协助人员中的发起人
  watch(
    () => formData.initiator,
    (newInitiator) => {
      if (newInitiator && formData.assistants && formData.assistants.includes(newInitiator)) {
        // 从协助人员中移除发起人
        formData.assistants = formData.assistants.filter((id: number) => id !== newInitiator)
      }
    }
  )

  // 加载字典数据
  const loadDictionaries = async () => {
    const data = await dictionaryStore.fetchItemsByCode('lifecycle_config')
    lifecycleTypes.value = data || []
  }

  // 加载用户列表
  const loadUserList = async () => {
    const response = await getUserList({ current: 1, size: 1000 })
    userList.value = response?.data || []
  }

  // 加载主体列表
  const loadEntityList = async () => {
    const response = await getEntityList({ current: 1, size: 1000 })
    entityList.value = response?.data || []
  }

  // 加载联系人列表
  const loadContacts = async (entityId: number) => {
    const response = await getAcceptancePersonnel(entityId)
    contactList.value = response || []
  }

  // 验收主体改变
  const handleEntityChange = (entityId: number | null) => {
    formData.acceptance_personnel = null
    contactList.value = []
    if (entityId) {
      loadContacts(entityId)
    }
  }

  // 加载资产列表
  const loadAssets = async () => {
    const response = await getAssetList({
      page: 1,
      per_page: 1000 // 一次性加载所有数据
    })
    assetList.value = response.data || []
  }

  // 关闭对话框
  const handleClose = () => {
    visible.value = false
    resetForm()
  }

  // 提交表单
  const handleSubmit = async () => {
    if (!formRef.value) return
    await formRef.value.validate(async (valid) => {
      if (valid) {
        if (props.type === 'add') {
          await createLifecycle({
            asset_id: formData.asset_id || undefined,
            type: formData.type!,
            date: formData.date || '',
            initiator_id: formData.initiator || undefined,
            content: formData.content || '',
            assistants: formData.assistants || [],
            acceptance_entity_id: formData.acceptance_entity || undefined,
            acceptance_personnel_id: formData.acceptance_personnel || undefined,
            acceptance_time: formData.acceptance_time || '',
            attachments: (formData.attachments || []).map((item: any) =>
              typeof item === 'number' ? item : item.id
            )
          })
          ElMessage.success('新增成功')
        } else {
          await updateLifecycle(formData.id!, {
            asset_id: formData.asset_id || undefined,
            type: formData.type!,
            date: formData.date || '',
            initiator_id: formData.initiator || undefined,
            content: formData.content || '',
            assistants: formData.assistants || [],
            acceptance_entity_id: formData.acceptance_entity || undefined,
            acceptance_personnel_id: formData.acceptance_personnel || undefined,
            acceptance_time: formData.acceptance_time || '',
            attachments: (formData.attachments || []).map((item: any) =>
              typeof item === 'number' ? item : item.id
            )
          })
          ElMessage.success('编辑成功')
        }
        emit('success')
        handleClose()
      }
    })
  }
</script>

<style lang="scss" scoped>
  .upload-demo {
    width: 100%;
  }
</style>
