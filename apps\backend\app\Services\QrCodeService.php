<?php

namespace App\Services;

use SimpleSoftwareIO\QrCode\Facades\QrCode;
use Illuminate\Support\Facades\Storage;

class QrCodeService
{
    /**
     * 生成二维码
     *
     * @param array $data 二维码配置参数
     * @return string
     */
    public function generate(array $data): string
    {
        // 设置二维码参数
        $size = $data['size'] ?? 300;
        $color = $data['color'] ?? '#000000';
        $backgroundColor = $data['background_color'] ?? '#ffffff';
        $margin = $data['margin'] ?? 1;

        // 创建二维码实例
        $qrCode = QrCode::format('png')
            ->size($size)
            ->margin($margin)
            ->color(
                hexdec(substr($color, 1, 2)),
                hexdec(substr($color, 3, 2)),
                hexdec(substr($color, 5, 2))
            )
            ->backgroundColor(
                hexdec(substr($backgroundColor, 1, 2)),
                hexdec(substr($backgroundColor, 3, 2)),
                hexdec(substr($backgroundColor, 5, 2))
            )
            ->errorCorrection('H');

        // 生成二维码
        return $qrCode->generate($data['content']);
    }

    /**
     * 生成二维码并返回 base64 编码
     *
     * @param array $data 二维码配置参数
     * @return string
     */
    public function generateBase64(array $data): string
    {
        // 设置二维码参数
        $size = $data['size'] ?? 300;
        $color = $data['color'] ?? '#000000';
        $backgroundColor = $data['background_color'] ?? '#ffffff';
        $margin = $data['margin'] ?? 1;

        // 创建二维码实例
        $qrCode = QrCode::format('png')
            ->size($size)
            ->margin($margin)
            ->color(
                hexdec(substr($color, 1, 2)),
                hexdec(substr($color, 3, 2)),
                hexdec(substr($color, 5, 2))
            )
            ->backgroundColor(
                hexdec(substr($backgroundColor, 1, 2)),
                hexdec(substr($backgroundColor, 3, 2)),
                hexdec(substr($backgroundColor, 5, 2))
            )
            ->errorCorrection('H');

        // 如果有logo，添加到二维码中心
        if (isset($data['logo']) && $data['logo']) {
            $logo = $data['logo'];
            $logoPath = Storage::disk('public')->putFile('qrcode/logos', $logo);
            $fullLogoPath = Storage::disk('public')->path($logoPath);
            $qrCode->merge($fullLogoPath, 0.3, true);

            // 生成完成后删除临时logo文件
            Storage::disk('public')->delete($logoPath);
        }

        // 生成二维码图片数据
        $qrCodeImage = $qrCode->generate($data['content']);

        // 转换为 base64
        return 'data:image/png;base64,' . base64_encode($qrCodeImage);
    }
}
