name: 自动部署（密码认证）

on:
  push:
    branches:
      - master

env:
  HTTP_PROXY: http://**********:10809
  HTTPS_PROXY: http://**********:10809

jobs:
  deploy:
    runs-on: ubuntu-24.04
    steps:
      - name: 检出代码
        uses: actions/checkout@v4
      
      - name: 设置 Node.js 环境
        uses: actions/setup-node@v4
        with:
          node-version: '22.13.1'
      
      - name: 安装 pnpm
        uses: pnpm/action-setup@v4
        with:
          version: 10.13.1
      
      - name: 创建远程目录
        uses: appleboy/ssh-action@v1.0.3
        with:
          host: *************
          username: tcadmin
          password: ${{ secrets.SSH_PASSWORD }}
          script: |
            # 创建项目目录
            mkdir -p /tcyl/cloud
            # 创建前端目录（在后端public/web目录下）
            mkdir -p /tcyl/cloud/apps/backend/public/web
      
      # 由于 burnett01/rsync-deployments 不支持密码认证，
      # 我们需要使用 appleboy/scp-action 或其他方式部署后端代码
      - name: 部署后端代码
        uses: appleboy/scp-action@v0.1.7
        with:
          host: *************
          username: tcadmin
          password: ${{ secrets.SSH_PASSWORD }}
          source: "./*"
          target: "/tcyl/cloud/"
          # 不删除现有文件，保护vendor、storage等目录
          rm: false
          strip_components: 0
      
      - name: 生成生产环境配置文件
        uses: appleboy/ssh-action@v1.0.3
        with:
          host: *************
          username: tcadmin
          password: ${{ secrets.SSH_PASSWORD }}
          script: |
            cd /tcyl/cloud/apps/backend
            # 直接使用 vars 变量写入 .env 文件
            echo "${{ vars.LARAVEL_ENV }}" > .env
      
      - name: 构建前端项目
        working-directory: ./apps/web
        run: |
          pnpm install --frozen-lockfile
          pnpm build
      
      - name: 部署前端文件
        uses: appleboy/scp-action@v0.1.7
        with:
          host: *************
          username: tcadmin
          password: ${{ secrets.SSH_PASSWORD }}
          source: "apps/web/dist/*"
          target: "/tcyl/cloud/apps/backend/public/web/"
          # 前端文件可以完全覆盖
          rm: true
          strip_components: 3
      
      - name: 启动 Docker 容器
        uses: appleboy/ssh-action@v1.0.3
        with:
          host: *************
          username: tcadmin
          password: ${{ secrets.SSH_PASSWORD }}
          script: |
            cd /tcyl/cloud
            # 停止并删除旧容器（如果存在）
            sudo docker-compose down || true
            # 启动新容器
            sudo docker-compose up -d
            # 等待容器启动
            sleep 10
      
      - name: 安装 Composer 依赖
        uses: appleboy/ssh-action@v1.0.3
        with:
          host: *************
          username: tcadmin
          password: ${{ secrets.SSH_PASSWORD }}
          script: |
            # 使用 root 用户执行，确保权限正常
            sudo docker exec -u root -e COMPOSER_HOME=/tmp/.composer device-cloud-saas bash -c "
              cd /www
              # 配置阿里云 Composer 镜像
              composer config -g repo.packagist composer https://mirrors.aliyun.com/composer/
              # 安装依赖
              composer install --no-dev --optimize-autoloader --no-interaction
            "
      
      - name: 执行生产环境命令
        uses: appleboy/ssh-action@v1.0.3
        with:
          host: *************
          username: tcadmin
          password: ${{ secrets.SSH_PASSWORD }}
          script: |
            sudo docker exec device-cloud-saas php artisan migrate --force
            # 清除缓存
            sudo docker exec device-cloud-saas php artisan config:cache
            sudo docker exec device-cloud-saas php artisan route:cache
            sudo docker exec device-cloud-saas php artisan view:cache
            # 生成字典枚举
            sudo docker exec device-cloud-saas php artisan dictionary:generate-enums
            # 生成API文档
            sudo docker exec device-cloud-saas php artisan scribe:generate