<?php

namespace App\Http\Resources\Admin;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

use function App\Support\string_to_timestamp;

class LifecycleFollowUpResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'lifecycle_id' => $this->lifecycle_id,
            'date' => $this->date,
            'person_id' => $this->person_id,
            'person_name' => $this->whenLoaded('person', fn() => $this->person->nickname),
            'content' => $this->content,
            'attachments' => $this->whenLoaded('attachments'),
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ];
    }
}
