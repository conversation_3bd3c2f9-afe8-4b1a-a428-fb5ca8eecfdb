# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## 🚀 快速参考

### 技术栈
- **前端**：Vue 3 + TypeScript + Element Plus + Vite
- **后端**：Laravel 12 + PHP 8.2 + MySQL + Sanctum

### 核心约定
- ✅ **API字段命名**：前后端统一使用 `snake_case`
- ✅ **API响应格式**：直接返回资源，不包装 `{data: {...}}`
- ✅ **Docker执行**：所有 Laravel 命令必须在容器内执行
- ✅ **类型管理**：API 类型统一在 `types/api/` 目录
- ✅ **验证方式**：必须使用 FormRequest，禁止控制器内验证

### 常用命令速查
```bash
# Docker 容器
PHP容器: ty-php-8.3
MySQL容器: ty-mysql-8.4.5

# 执行模板
docker exec ty-php-8.3 bash -c "cd /var/www/html/company/device-cloud-saas/apps/backend && php artisan [命令]"

# 常用命令
migrate:fresh --seed  # 重置数据库
dictionary:generate-enums  # 生成字典枚举
scribe:generate  # 生成API文档
optimize:clear  # 清除所有缓存
```

## 📁 项目结构

### 前端
```
apps/web/src/
├── api/         # API接口
├── components/  # 组件(core/custom)
├── composables/ # 组合式函数
├── store/       # Pinia状态管理
├── types/api/   # API类型定义(集中管理)
└── views/       # 页面视图
```

### 后端
```
apps/backend/app/
├── Http/
│   ├── Controllers/Admin/  # Admin控制器
│   ├── Requests/Admin/     # Admin请求验证
│   └── Resources/          # API资源
├── Models/                 # Eloquent模型
└── Services/               # 业务逻辑
```

## 🐍 命名规范（重要）

### Snake Case 统一规范
前后端所有API字段统一使用蛇形命名（snake_case），无需字段映射转换。

```typescript
// ✅ 正确示例
interface User {
  user_name: string
  created_at: string
  is_active: boolean
}
```

## 💻 前端开发

**重要**：前端开发相关任务请优先使用专门的 Agent 处理：
- **vue-frontend-developer**：负责开发、修改和优化 Vue 3 + Element Plus 前端代码
- **frontend-code-checker**：负责运行代码质量检查（ESLint、TypeScript、Prettier 等）

前端开发详细规范请参考：[`/apps/web/standard/frontend-development-guide.md`](./apps/web/standard/frontend-development-guide.md)

## 🔧 后端开发规范

### 1. 文件组织
```
Controllers/Admin/  # Admin控制器目录
Requests/Admin/     # Admin请求验证目录
```

### 2. 控制器规范
```php
class UserController extends Controller
{
    public function __construct(
        private UserService $userService
    ) {}

    public function index(UserIndexRequest $request)
    {
        return UserResource::collection(
            $this->userService->paginate($request->validated())
        );
    }
}
```

### 3. FormRequest 规范
```php
class UserRequest extends FormRequest
{
    public function rules(): array
    {
        return [
            'name' => 'required|string|max:50',
            'email' => 'required|email|unique:users,email'
        ];
    }
    
    public function messages(): array
    {
        return [
            'name.required' => '用户名不能为空',
            'email.unique' => '邮箱已被使用'
        ];
    }
}
```

### 4. RESTful API
```
GET    /api/admin/users         # 列表
GET    /api/admin/users/{id}    # 详情
POST   /api/admin/users         # 创建
PUT    /api/admin/users/{id}    # 更新
DELETE /api/admin/users/{id}    # 删除
```

## 🔍 代码质量检查

### 前端
使用 **frontend-code-checker** Agent 进行代码质量检查，它会自动运行所有必要的检查命令。

### 后端
```bash
# 代码格式化
docker exec ty-php-8.3 bash -c "cd /var/www/html/company/device-cloud-saas/apps/backend && ./vendor/bin/pint"

# 运行测试
docker exec ty-php-8.3 bash -c "cd /var/www/html/company/device-cloud-saas/apps/backend && ./vendor/bin/pest"
```

## 📦 功能模块清单

### ✅ 已完成
- 分类管理（无限级树形结构）
- 主体管理（多联系人、附件支持）
- 字典管理（系统配置字典）
- 用户管理（角色分配、头像上传）
- 角色管理（CRUD和用户分配）
- 角色菜单权限（权限分配、树形展示）
- 生命周期（项目跟踪、跟进记录）
- 附件管理（OSS/本地存储、MD5秒传）
- 菜单管理（权限控制、动态路由、自动生成技术字段）
- 资产管理（三级分类、地区选择）
- 地区管理（省市区三级联动）


## 🎯 核心实现要点

### 附件管理
- 前端提交纯ID数组：`attachments: [1, 2, 3]`
- 后端Service层自动设置分类
- 使用 `HasAttachments` trait 实现多态关联

### 菜单管理
- 技术字段自动生成：路由地址、权限标识等字段在前端禁用，后端自动生成
- 自动生成标识：`AUTO_MENU_`、`/auto_path_`、`AUTO_PERM_` 前缀标识需要开发人员后续修改
- 简化操作：用户只需填写菜单名称等基本信息，技术细节由系统处理

### 角色权限管理
- ElTree 父子联动问题：当有具体权限时，只勾选权限节点，不勾选菜单节点
- 权限数据格式：后端接收 `permissions` 数组，前端提交时需要转换格式
- 空权限处理：`permission_ids` 可以为空数组，表示只有菜单访问权限

### 其他实现细节
前端具体实现细节（如地区选择、搜索表单、树形数据、日期时间格式、弹窗优化等）请使用 **vue-frontend-developer** Agent 处理。

## 🛠️ 开发注意事项

1. **Docker环境**：所有Laravel命令必须在容器内执行
2. **前端开发**：优先使用 `vue-frontend-developer` Agent 处理前端任务
3. **代码质量**：使用 `frontend-code-checker` Agent 进行代码质量检查
4. **搜索限制**：搜索代码时限定在 `app/` 或 `src/` 目录

## 📊 数据库表

### 核心表
- `users` - 用户表
- `roles` - 角色表
- `menus` - 菜单表
- `categories` - 分类表
- `dictionary_categories/items` - 字典表
- `entities/entity_contacts` - 主体表
- `attachments/attachment_relations` - 附件表
- `lifecycles/lifecycle_follow_ups` - 生命周期表
- `assets` - 资产表
- `regions` - 地区表（3,597条数据）