import { defineStore } from 'pinia'
import { ref } from 'vue'
import type { AppConfig, SystemConfig, UploadConfig } from '@/types/api'

/**
 * 配置存储
 * 用于缓存和管理系统配置数据
 */
export const useConfigStore = defineStore('configStore', () => {
  // 状态定义
  const config = ref<AppConfig>({
    system: {
      system_name: '',
      system_logo: null
    },
    upload: {
      storage_type: null,
      aliyun_access_key: '',
      aliyun_secret_key: '',
      aliyun_bucket: '',
      aliyun_region: ''
    }
  })

  const lastFetchTime = ref<number>(0)
  const initialized = ref(false)
  const loading = ref(false)

  // 缓存有效期（30分钟）
  const CACHE_DURATION = 30 * 60 * 1000

  /**
   * 初始化配置数据
   * 在 App.vue 中调用，加载系统配置
   */
  const initialize = async () => {
    if (initialized.value) return

    try {
      // 这里暂时使用 import 来延迟加载，避免循环依赖
      const { getConfigs } = await import('@/api/admin/configApi')
      const data = await getConfigs()
      config.value = data
      lastFetchTime.value = Date.now()
      initialized.value = true
    } catch (error) {
      console.error('配置初始化失败:', error)
      throw error
    }
  }

  /**
   * 获取配置
   * 如果缓存有效则返回缓存，否则从后端获取
   */
  const fetchConfig = async (): Promise<AppConfig> => {
    const now = Date.now()

    // 检查缓存是否有效
    if (lastFetchTime.value && now - lastFetchTime.value < CACHE_DURATION) {
      return config.value
    }

    // 如果正在加载，等待加载完成
    if (loading.value) {
      return new Promise((resolve) => {
        const checkInterval = setInterval(() => {
          if (!loading.value) {
            clearInterval(checkInterval)
            resolve(config.value)
          }
        }, 100)
      })
    }

    // 开始加载
    loading.value = true

    try {
      const { getConfigs } = await import('@/api/admin/configApi')
      const data = await getConfigs()

      // 更新缓存
      config.value = data
      lastFetchTime.value = now

      return config.value
    } catch (error) {
      console.error('获取配置失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  /**
   * 更新系统配置
   */
  const updateSystemConfig = async (data: SystemConfig): Promise<void> => {
    const { updateConfigs } = await import('@/api/admin/configApi')
    await updateConfigs({
      configs: {
        ...config.value,
        system: data
      }
    })

    // 更新本地缓存
    config.value.system = { ...data }
  }

  /**
   * 更新上传配置
   */
  const updateUploadConfig = async (data: UploadConfig): Promise<void> => {
    const { updateConfigs } = await import('@/api/admin/configApi')
    await updateConfigs({
      configs: {
        ...config.value,
        upload: data
      }
    })

    // 更新本地缓存
    config.value.upload = { ...data }
  }

  /**
   * 更新所有配置（系统配置和上传配置）
   */
  const updateAllConfigs = async (data: AppConfig): Promise<void> => {
    const { updateConfigs } = await import('@/api/admin/configApi')
    await updateConfigs({ configs: data })

    // 更新本地缓存
    config.value = { ...data }
  }

  /**
   * 强制刷新配置
   */
  const refreshConfig = async (): Promise<AppConfig> => {
    // 清除缓存时间，强制重新获取
    lastFetchTime.value = 0
    return fetchConfig()
  }

  /**
   * 清除缓存
   */
  const clearCache = () => {
    config.value = {
      system: {
        system_name: '',
        system_logo: null
      },
      upload: {
        storage_type: null,
        aliyun_access_key: '',
        aliyun_secret_key: '',
        aliyun_bucket: '',
        aliyun_region: ''
      }
    }
    lastFetchTime.value = 0
    initialized.value = false
  }

  return {
    // 状态
    config,
    loading,
    initialized,

    // 方法
    initialize,
    fetchConfig,
    updateSystemConfig,
    updateUploadConfig,
    updateAllConfigs,
    refreshConfig,
    clearCache
  }
})
