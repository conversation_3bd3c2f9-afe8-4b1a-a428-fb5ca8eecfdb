import type { BackendMenuItem } from '@/api/admin/menuApi'

/**
 * 菜单类型枚举
 */
export enum MenuType {
  DIRECTORY = '目录',
  MENU = '菜单',
  IFRAME = '内嵌',
  EXTERNAL = '外链'
}

/**
 * 菜单类型对应的标签样式
 */
export enum MenuTypeTag {
  DIRECTORY = 'info',
  MENU = 'primary',
  IFRAME = 'success',
  EXTERNAL = 'warning'
}

/**
 * 菜单相关的组合式函数
 */
export function useMenu() {
  /**
   * 获取菜单类型
   * @param menu 菜单项
   * @returns 菜单类型
   */
  const getMenuType = (menu: BackendMenuItem): MenuType => {
    // 有子菜单的是目录
    if (menu.children && menu.children.length > 0) {
      return MenuType.DIRECTORY
    }
    // 有链接且是iframe的是内嵌
    else if (menu.meta?.link && menu.meta?.isIframe) {
      return MenuType.IFRAME
    }
    // 有路径的是菜单
    else if (menu.path) {
      return MenuType.MENU
    }
    // 有链接的是外链
    else if (menu.meta?.link) {
      return MenuType.EXTERNAL
    }
    // 默认返回菜单
    return MenuType.MENU
  }

  /**
   * 获取菜单类型对应的标签样式
   * @param menu 菜单项
   * @returns 标签样式
   */
  const getMenuTypeTagType = (menu: BackendMenuItem): string => {
    const type = getMenuType(menu)
    switch (type) {
      case MenuType.DIRECTORY:
        return MenuTypeTag.DIRECTORY
      case MenuType.IFRAME:
        return MenuTypeTag.IFRAME
      case MenuType.EXTERNAL:
        return MenuTypeTag.EXTERNAL
      case MenuType.MENU:
      default:
        return MenuTypeTag.MENU
    }
  }

  /**
   * 判断菜单项是否为目录（用于树形结构判断）
   * 注意：这里只判断是否有真正的子菜单，不包括权限节点
   * @param menu 菜单项
   * @returns 是否为目录
   */
  const isDirectory = (menu: BackendMenuItem): boolean => {
    return menu.children !== undefined && menu.children.length > 0
  }

  /**
   * 判断菜单项是否为菜单（有路径且不是目录）
   * @param menu 菜单项
   * @returns 是否为菜单
   */
  const isMenu = (menu: BackendMenuItem): boolean => {
    return !isDirectory(menu) && !!menu.path
  }

  return {
    MenuType,
    MenuTypeTag,
    getMenuType,
    getMenuTypeTagType,
    isDirectory,
    isMenu
  }
}
