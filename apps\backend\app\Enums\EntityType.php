<?php

namespace App\Enums;

/**
 * 主体类型
 *
 * 此文件由 php artisan dictionary:generate-enums 命令自动生成
 * 请勿手动修改，如需更改请在字典管理中修改后重新生成
 *
 * @generated
 */
enum EntityType: string
{
    case MANUFACTURER = 'manufacturer';
    case SUPPLIER = 'supplier';
    case END_CUSTOMER = 'end_customer';
    case SERVICE_PROVIDER = 'service_provider';
    case AFTER_SALES = 'after_sales';

    /**
     * 获取枚举对应的中文标签
     */
    public function label(): string
    {
        return match ($this) {
            self::MANUFACTURER => '生产厂',
            self::SUPPLIER => '供应商',
            self::END_CUSTOMER => '最终客户',
            self::SERVICE_PROVIDER => '服务商',
            self::AFTER_SALES => '售后部',
        };
    }

    /**
     * 根据值获取枚举实例
     */
    public static function tryFromValue(string $value): ?self
    {
        return self::tryFrom($value);
    }

    /**
     * 检查值是否有效
     */
    public static function isValid(string $value): bool
    {
        return self::tryFrom($value) !== null;
    }

    /**
     * 获取所有枚举值
     */
    public static function values(): array
    {
        return array_column(self::cases(), 'value');
    }
}
