<?php

namespace App\Http\Controllers\Admin;

use App\Http\Requests\Admin\QrCodeRequest;
use App\Http\Controllers\Controller;
use App\Services\QrCodeService;

/**
 * @group 二维码生成
 */
class QrCodeController extends Controller
{
    protected $qrCodeService;

    public function __construct(QrCodeService $qrCodeService)
    {
        $this->qrCodeService = $qrCodeService;
    }

    /**
     * 生成二维码
     *
     * @unauthenticated
     *
     * @param QrCodeRequest $request
     * @bodyParam content string 要生成二维码的内容 Example: 1222
     * @bodyParam size integer 二维码大小，默认300，范围50-1000 Example: 300
     * @bodyParam color string 二维码颜色，十六进制颜色码 Example: #ff0000
     * @bodyParam background_color string 背景颜色，十六进制颜色码 Example: #ffffff
     * @bodyParam margin integer 二维码边距，默认1，范围0-50 Example: 1
     *
     * @response 201
     * {
     *   "qrcode": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA..."
     * }
     */
    public function generate(QrCodeRequest $request)
    {
        // $request->validate([
        //     'content' => 'required|string',
        //     'size' => 'nullable|integer|min:50|max:1000',
        //     'color' => 'nullable|regex:/^#[a-fA-F0-9]{6}$/',
        //     'background_color' => 'nullable|regex:/^#[a-fA-F0-9]{6}$/',
        //     'margin' => 'nullable|integer|min:0|max:50',
        // ]);

        $base64QrCode = $this->qrCodeService->generateBase64($request->all());

        return response()->json([
            'qrcode' => $base64QrCode
        ])->setStatusCode(201);
    }
}
