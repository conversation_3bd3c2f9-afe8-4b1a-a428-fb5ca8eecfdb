<?php

namespace App\Services;

use App\Enums\AttachmentCategory;
use App\Models\User;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Auth;

class UserService
{
    /**
     * 分页查询用户列表
     */
    public function paginate(array $params): LengthAwarePaginator
    {

        $query = User::query()->with(['attachments']);

        // 关键词搜索（用户名、手机号、邮箱）
        if (! empty($params['keyword'])) {
            $keyword = $params['keyword'];
            $query->where(function ($q) use ($keyword) {
                $q->where('nickname', 'like', "%{$keyword}%")
                    ->orWhere('account', 'like', "%{$keyword}%")
                    ->orWhere('email', 'like', "%{$keyword}%");
            });
        }

        // 状态筛选
        if (! empty($params['status'])) {
            $query->where('status', $params['status']);
        }

        // 排序
        $query->orderBy('id', 'desc');

        // 加载附件和角色关系
        $query->with(['attachments', 'roles']);

        return $query->paginate($params['per_page'] ?? 20);
    }

    /**
     * 创建用户
     */
    public function create(array $data): User
    {

        return DB::transaction(function () use ($data) {

            // 密码加密
            if (! empty($data['password'])) {
                $data['password'] = Hash::make($data['password']);
            }

            // 处理头像附件ID
            $avatarId = $data['avatar_id'] ?? null;
            unset($data['avatar_id']);

            $user = User::create($data);

            // 关联头像附件
            if ($avatarId) {
                $attachmentData = [[
                    'id' => $avatarId,
                    'category' => AttachmentCategory::AVATAR->value,
                    'sort' => 0,
                    'description' => null,
                ]];
                $user->attachFiles($attachmentData);
            }

            return $user->load('attachments');
        });
    }

    /**
     * 更新用户
     */
    public function update(User $user, array $data): User
    {
        return DB::transaction(function () use ($user, $data) {
            // 如果有密码则加密
            if (! empty($data['password'])) {
                $data['password'] = Hash::make($data['password']);
            }

            // 处理头像附件ID
            $avatarId = $data['avatar_id'] ?? null;
            unset($data['avatar_id']);

            $user->update($data);

            // 更新头像附件
            if ($avatarId !== null) {
                if ($avatarId) {
                    // 有新头像，同步附件
                    $attachmentData = [[
                        'id' => $avatarId,
                        'category' => AttachmentCategory::AVATAR->value,
                        'sort' => 0,
                        'description' => null,
                    ]];
                    $user->syncAttachments($attachmentData);
                } else {
                    // 清空头像，解除所有头像类型的附件
                    $avatarAttachments = $user->attachments()
                        ->wherePivot('category', AttachmentCategory::AVATAR->value)
                        ->pluck('attachments.id');
                    foreach ($avatarAttachments as $attachmentId) {
                        $user->detachFile($attachmentId);
                    }
                }
            }

            return $user->fresh()->load('attachments');
        });
    }

    /**
     * 删除用户
     */
    public function delete(User $user): bool
    {
        return $user->delete();
    }

    /**
     * 为用户分配角色（追加式）
     */
    public function assignRoles(User $user, array $roleIds): void
    {
        DB::transaction(function () use ($user, $roleIds) {
            $user->roles()->syncWithoutDetaching($roleIds);
        });
    }

    /**
     * 同步用户角色（覆盖式）
     */
    public function syncRoles(User $user, array $roleIds): void
    {
        DB::transaction(function () use ($user, $roleIds) {
            $user->roles()->sync($roleIds);
        });
    }

    /**
     * 移除用户角色
     */
    public function removeRoles(User $user, array $roleIds): void
    {
        DB::transaction(function () use ($user, $roleIds) {
            $user->roles()->detach($roleIds);
        });
    }
}
