import type { AttachmentPageResponse, AttachmentSearchParams, AttachmentItem } from '@/types/api'
import request from '@/utils/http'
import type { STSCredentials, OSSConfig } from '@/utils/ossUpload'

/**
 * 获取附件列表
 */
export function getAttachmentList(
  params: AttachmentSearchParams & { page?: number; size?: number; per_page?: number }
): Promise<AttachmentPageResponse> {
  return request.get<AttachmentPageResponse>({
    url: '/admin/attachments',
    params: {
      ...params,
      page: params.page,
      per_page: params.per_page || params.size
    }
  })
}

/**
 * 删除附件
 */
export function deleteAttachment(id: string): Promise<void> {
  return request.del<void>({
    url: `/admin/attachments/${id}`
  })
}

/**
 * 上传附件（关联业务）
 */
export function uploadAttachmentWithBiz(data: {
  attachable_type: string
  attachable_id: string
  file: File
  category?: string
  description?: string
}): Promise<AttachmentItem> {
  const formData = new FormData()
  formData.append('file', data.file)
  formData.append('attachable_type', data.attachable_type)
  formData.append('attachable_id', data.attachable_id)
  if (data.category) {
    formData.append('category', data.category)
  }
  if (data.description) {
    formData.append('description', data.description)
  }

  return request.post<AttachmentItem>({
    url: '/admin/attachments/upload',
    data: formData
    // 不要手动设置 Content-Type，让浏览器自动设置，这样会包含 boundary
  })
}

/**
 * 上传附件（简单上传）
 */
export function uploadAttachment(formData: FormData): Promise<AttachmentItem> {
  return request.post<AttachmentItem>({
    url: '/admin/attachments/upload',
    data: formData
    // 不要手动设置 Content-Type，让浏览器自动设置，这样会包含 boundary
  })
}

/**
 * 根据业务ID获取附件列表
 */
export function getAttachmentsByBizId(
  attachable_type: string,
  attachable_id: string
): Promise<AttachmentItem[]> {
  return request.get<AttachmentItem[]>({
    url: '/admin/attachments/by-business',
    params: {
      attachable_type,
      attachable_id
    }
  })
}

/**
 * 获取附件统计信息
 */
export function getAttachmentStats(): Promise<{
  total: number
  byBizType: Record<string, number>
}> {
  return request.get<{
    total: number
    byBizType: Record<string, number>
  }>({
    url: '/admin/attachments/stats'
  })
}

/**
 * 下载附件
 */
export function downloadAttachment(id: string): Promise<Blob> {
  return request.get<Blob>({
    url: `/admin/attachments/${id}/download`,
    responseType: 'blob'
  })
}

/**
 * 获取附件详情
 */
export function getAttachmentDetail(id: string): Promise<AttachmentItem> {
  return request.get<AttachmentItem>({
    url: `/admin/attachments/${id}`
  })
}

/**
 * 更新附件描述
 */
export function updateAttachmentDescription(
  id: string,
  data: { description: string }
): Promise<AttachmentItem> {
  return request.put<AttachmentItem>({
    url: `/admin/attachments/${id}/description`,
    data
  })
}

/**
 * 获取STS临时凭证
 */
export interface GetSTSCredentialsParams {
  filename: string
  filesize: number
  mime_type: string
  md5_hash?: string
}

export interface GetSTSCredentialsResponse {
  quick_upload: boolean
  attachment?: AttachmentItem
  upload_id?: string
  credentials?: STSCredentials
  region?: string
  bucket?: string
  endpoint?: string
  prefix?: string
}

export function getSTSCredentials(
  params: GetSTSCredentialsParams
): Promise<GetSTSCredentialsResponse> {
  return request.post<GetSTSCredentialsResponse>({
    url: '/admin/attachments/sts/credentials',
    data: params
  })
}

/**
 * 确认上传完成
 */
export interface ConfirmUploadParams {
  upload_id: string
  object_key: string
  filename?: string
  filesize?: number
  mime_type?: string
}

export function confirmUpload(params: ConfirmUploadParams): Promise<AttachmentItem> {
  return request.post<AttachmentItem>({
    url: '/admin/attachments/sts/confirm',
    data: params
  })
}
