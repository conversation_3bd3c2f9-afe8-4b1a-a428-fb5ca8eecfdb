<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @property int $id
 * @property string $file_path 导入文件路径
 * @property string $original_filename 原始文件名
 * @property string $status 导入状态
 * @property int $total_rows 总行数
 * @property int $success_rows 成功行数
 * @property int $failed_rows 失败行数
 * @property array|null $error_details 错误详情
 * @property string|null $summary 导入摘要
 * @property int|null $created_by 创建人
 * @property int|null $started_at 开始时间
 * @property int|null $completed_at 完成时间
 * @property int|null $created_at 创建时间
 * @property int|null $updated_at 更新时间
 * @property-read \App\Models\User|null $creator
 */
class AssetImportTask extends BaseModel
{
    use HasFactory;

    protected $fillable = [
        'file_path',
        'original_filename',
        'status',
        'total_rows',
        'success_rows',
        'failed_rows',
        'error_details',
        'summary',
        'created_by',
        'started_at',
        'completed_at',
    ];

    protected $casts = [
        'error_details' => 'array',
        'started_at' => 'integer',
        'completed_at' => 'integer',
        'created_at' => 'integer',
        'updated_at' => 'integer',
    ];

    /**
     * 获取创建人
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * 标记任务为处理中
     */
    public function markAsProcessing(): void
    {
        $this->update([
            'status' => 'processing',
            'started_at' => time(),
        ]);
    }

    /**
     * 标记任务为完成
     */
    public function markAsCompleted(array $summary = []): void
    {
        $this->update([
            'status' => 'completed',
            'completed_at' => time(),
            'summary' => $summary['summary'] ?? null,
        ]);
    }

    /**
     * 标记任务为失败
     */
    public function markAsFailed(array $errors = []): void
    {
        $this->update([
            'status' => 'failed',
            'completed_at' => time(),
            'error_details' => $errors,
        ]);
    }

    /**
     * 更新进度
     */
    public function updateProgress(int $totalRows, int $successRows, int $failedRows): void
    {
        $this->update([
            'total_rows' => $totalRows,
            'success_rows' => $successRows,
            'failed_rows' => $failedRows,
        ]);
    }
}
