import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { DictionaryCategory, DictionaryItem } from '@/types/api'

/**
 * 字典存储
 * 用于缓存和管理系统字典数据
 */
export const useDictionaryStore = defineStore('dictionaryStore', () => {
  // 状态定义
  const categories = ref<DictionaryCategory[]>([])
  const itemsMap = ref<Map<string, DictionaryItem[]>>(new Map())
  const loadingMap = ref<Map<string, boolean>>(new Map())
  const lastFetchMap = ref<Map<string, number>>(new Map())
  const initialized = ref(false)

  // 缓存有效期（30分钟）
  const CACHE_DURATION = 30 * 60 * 1000

  /**
   * 初始化字典数据
   * 在 App.vue 中调用，加载所有字典分类
   */
  const initialize = async () => {
    if (initialized.value) return

    try {
      // 这里暂时使用 import 来延迟加载，避免循环依赖
      const { getDictionaryCategories } = await import('@/api/admin/dictionaryApi')
      categories.value = await getDictionaryCategories()
      initialized.value = true
    } catch (error) {
      console.error('字典初始化失败:', error)
      throw error
    }
  }

  /**
   * 根据分类代码获取字典项
   * 如果缓存有效则返回缓存，否则从后端获取
   */
  const fetchItemsByCode = async (categoryCode: string): Promise<DictionaryItem[]> => {
    // 检查缓存是否有效
    const lastFetch = lastFetchMap.value.get(categoryCode)
    const now = Date.now()

    if (lastFetch && now - lastFetch < CACHE_DURATION && itemsMap.value.has(categoryCode)) {
      return itemsMap.value.get(categoryCode) || []
    }

    // 检查是否正在加载
    if (loadingMap.value.get(categoryCode)) {
      // 等待加载完成
      return new Promise((resolve) => {
        const checkInterval = setInterval(() => {
          if (!loadingMap.value.get(categoryCode)) {
            clearInterval(checkInterval)
            resolve(itemsMap.value.get(categoryCode) || [])
          }
        }, 100)
      })
    }

    // 开始加载
    loadingMap.value.set(categoryCode, true)

    try {
      const { getDictionaryByCode } = await import('@/api/admin/dictionaryApi')
      const items = await getDictionaryByCode(categoryCode)

      // 更新缓存
      itemsMap.value.set(categoryCode, items)
      lastFetchMap.value.set(categoryCode, now)

      return items
    } catch (error) {
      console.error(`获取字典项失败 [${categoryCode}]:`, error)
      throw error
    } finally {
      loadingMap.value.set(categoryCode, false)
    }
  }

  /**
   * 获取缓存的字典项（不触发请求）
   */
  const getCachedItems = (categoryCode: string): DictionaryItem[] => {
    return itemsMap.value.get(categoryCode) || []
  }

  /**
   * 强制刷新指定字典
   */
  const refreshCategory = async (categoryCode: string): Promise<DictionaryItem[]> => {
    // 清除缓存时间，强制重新获取
    lastFetchMap.value.delete(categoryCode)
    return fetchItemsByCode(categoryCode)
  }

  /**
   * 字典增删改后的更新
   * 可以选择更新特定分类或全部刷新
   */
  const updateAfterChange = async (categoryCode?: string) => {
    if (categoryCode) {
      // 更新特定分类
      await refreshCategory(categoryCode)
    } else {
      // 更新所有已缓存的分类
      const codes = Array.from(itemsMap.value.keys())
      await Promise.all(codes.map((code) => refreshCategory(code)))
    }

    // 重新获取分类列表
    await initialize()
  }

  /**
   * 清除所有缓存
   */
  const clearCache = () => {
    itemsMap.value.clear()
    loadingMap.value.clear()
    lastFetchMap.value.clear()
    categories.value = []
    initialized.value = false
  }

  /**
   * 计算属性：获取所有分类的映射
   */
  const categoryMap = computed(() => {
    const map = new Map<string, DictionaryCategory>()
    categories.value.forEach((cat) => {
      map.set(cat.code, cat)
    })
    return map
  })

  /**
   * 计算属性：是否正在加载任何字典
   */
  const isLoading = computed(() => {
    return Array.from(loadingMap.value.values()).some((loading) => loading)
  })

  return {
    // 状态
    categories,
    itemsMap,
    loadingMap,
    initialized,

    // 计算属性
    categoryMap,
    isLoading,

    // 方法
    initialize,
    fetchItemsByCode,
    getCachedItems,
    refreshCategory,
    updateAfterChange,
    clearCache
  }
})
