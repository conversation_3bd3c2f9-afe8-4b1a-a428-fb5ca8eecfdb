<?php

/**
 * 调试Excel文件结构
 */

require_once __DIR__ . '/vendor/autoload.php';

$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "=== 调试Excel文件结构 ===\n\n";

try {
    $templatePath = 'storage/app/public/资产导入模板_2025-08-14_10-26-23.xlsx';
    $fullPath = __DIR__ . '/' . $templatePath;
    
    class DebugReader
    {
        // 空的读取器类
    }
    
    $data = \Maatwebsite\Excel\Facades\Excel::toArray(new DebugReader(), $fullPath);
    
    echo "工作表数量: " . count($data) . "\n\n";
    
    if (!empty($data)) {
        $sheet = $data[0];
        echo "第一个工作表行数: " . count($sheet) . "\n\n";
        
        // 显示前10行的结构
        for ($i = 0; $i < min(10, count($sheet)); $i++) {
            echo "第 " . ($i + 1) . " 行 (" . count($sheet[$i]) . " 列):\n";
            
            // 显示前10列的内容
            for ($j = 0; $j < min(10, count($sheet[$i])); $j++) {
                $value = $sheet[$i][$j] ?? '';
                if (!empty($value)) {
                    echo "  列 " . ($j + 1) . ": {$value}\n";
                }
            }
            echo "\n";
        }
        
        // 寻找包含"资产名称"的行
        echo "寻找包含'资产名称'的行:\n";
        for ($i = 0; $i < count($sheet); $i++) {
            $row = $sheet[$i];
            if (in_array('资产名称', $row)) {
                echo "找到'资产名称'在第 " . ($i + 1) . " 行\n";
                echo "该行内容: " . implode(', ', array_filter($row)) . "\n";
                break;
            }
        }
        
        // 检查第2行的具体内容
        if (isset($sheet[1])) {
            echo "\n第2行详细内容:\n";
            foreach ($sheet[1] as $index => $value) {
                if (!empty($value)) {
                    echo "  索引 {$index}: '{$value}'\n";
                }
            }
        }
    }
    
} catch (Exception $e) {
    echo "调试失败: " . $e->getMessage() . "\n";
    echo "错误位置: " . $e->getFile() . ":" . $e->getLine() . "\n";
}

echo "\n=== 调试结束 ===\n";
