<?php

namespace App\Jobs;

use App\Models\AssetImportTask;
use App\Services\AssetImportService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class ProcessAssetImport implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected AssetImportTask $importTask;

    /**
     * Create a new job instance.
     */
    public function __construct(AssetImportTask $importTask)
    {
        $this->importTask = $importTask;
    }

    /**
     * Execute the job.
     */
    public function handle(AssetImportService $importService): void
    {
        try {
            Log::info('开始处理资产导入任务', ['task_id' => $this->importTask->id]);

            // 标记任务为处理中
            $this->importTask->markAsProcessing();

            // 处理导入
            $result = $importService->processImport($this->importTask);

            // 标记任务为完成
            $this->importTask->markAsCompleted($result);

            Log::info('资产导入任务完成', [
                'task_id' => $this->importTask->id,
                'total_rows' => $result['total_rows'] ?? 0,
                'success_rows' => $result['success_rows'] ?? 0,
                'failed_rows' => $result['failed_rows'] ?? 0,
            ]);
        } catch (\Exception $e) {
            Log::error('资产导入任务失败', [
                'task_id' => $this->importTask->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            // 标记任务为失败
            $this->importTask->markAsFailed([
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            throw $e;
        }
    }

    /**
     * Handle a job failure.
     */
    public function failed(\Throwable $exception): void
    {
        Log::error('资产导入Job失败', [
            'task_id' => $this->importTask->id,
            'error' => $exception->getMessage(),
        ]);

        $this->importTask->markAsFailed([
            'error' => $exception->getMessage(),
            'trace' => $exception->getTraceAsString(),
        ]);
    }
}
