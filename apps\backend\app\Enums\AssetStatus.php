<?php

namespace App\Enums;

/**
 * 当前设备状态
 *
 * 此文件由 php artisan dictionary:generate-enums 命令自动生成
 * 请勿手动修改，如需更改请在字典管理中修改后重新生成
 *
 * @generated
 */
enum AssetStatus: string
{
    case NEW_UNSTOCKED = 'new_unstocked';
    case IN_USE = 'in_use';
    case PENDING_CHECK = 'pending_check';
    case SCRAP_REGISTERED = 'scrap_registered';
    case UNDER_REPAIR = 'under_repair';

    /**
     * 获取枚举对应的中文标签
     */
    public function label(): string
    {
        return match ($this) {
            self::NEW_UNSTOCKED => '全新未出库',
            self::IN_USE => '正常使用中',
            self::PENDING_CHECK => '待检测',
            self::SCRAP_REGISTERED => '报废登记',
            self::UNDER_REPAIR => '维修中',
        };
    }

    /**
     * 根据值获取枚举实例
     */
    public static function tryFromValue(string $value): ?self
    {
        return self::tryFrom($value);
    }

    /**
     * 检查值是否有效
     */
    public static function isValid(string $value): bool
    {
        return self::tryFrom($value) !== null;
    }

    /**
     * 获取所有枚举值
     */
    public static function values(): array
    {
        return array_column(self::cases(), 'value');
    }
}
