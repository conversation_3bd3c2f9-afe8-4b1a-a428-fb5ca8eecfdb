import request from '@/utils/http'
import type { Contact, ContactForm, ContactPageResponse } from '@/types/api'

/**
 * 获取主体的联系人列表
 */
export const getEntityContacts = (
  entityId: string,
  params: { current?: number; size?: number } = {}
): Promise<ContactPageResponse> => {
  return request
    .get<ContactPageResponse>({
      url: `/admin/entities/${entityId}/contacts`,
      params: {
        page: params.current,
        per_page: params.size
      }
    })
    .then((response: any) => {
      // 将后端响应格式转换为前端期望的格式
      const data = response.data.map((item: any) => ({
        ...item,
        createTime: item.createdAt || item.createTime
      }))

      return {
        data,
        total: response.meta.total || 0,
        current: response.meta.current_page || 1,
        size: response.meta.per_page || 10
      }
    })
}

/**
 * 创建联系人
 */
export const createContact = (entityId: string, data: ContactForm): Promise<Contact> => {
  return request.post<Contact>({
    url: `/admin/entities/${entityId}/contacts`,
    data: {
      name: data.name,
      phone: data.phone,
      position: data.position,
      department: data.department
    }
  })
}

/**
 * 更新联系人
 */
export const updateContact = (
  entityId: string,
  contactId: string,
  data: ContactForm
): Promise<Contact> => {
  return request.put<Contact>({
    url: `/admin/entities/${entityId}/contacts/${contactId}`,
    data: {
      name: data.name,
      phone: data.phone,
      position: data.position,
      department: data.department
    }
  })
}

/**
 * 删除联系人
 */
export const deleteContact = (entityId: string, contactId: string): Promise<void> => {
  return request.del<void>({
    url: `/admin/entities/${entityId}/contacts/${contactId}`
  })
}
