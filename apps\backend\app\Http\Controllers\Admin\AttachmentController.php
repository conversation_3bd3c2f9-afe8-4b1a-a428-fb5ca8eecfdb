<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\AttachmentBusinessRequest;
use App\Http\Requests\Admin\AttachmentRequest;
use App\Http\Requests\Admin\STSCredentialsRequest;
use App\Http\Requests\Admin\ConfirmUploadRequest;
use App\Http\Resources\Admin\AttachmentResource;
use App\Models\Attachment;
use App\Models\AttachmentRelation;
use App\Services\AttachmentService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

/**
 * @group 附件管理
 * 附件的上传、下载、删除等操作
 */
class AttachmentController extends Controller
{
    /**
     * 返回错误响应
     *
     * @param string $message 错误信息
     * @param int $status 状态码
     * @return \Illuminate\Http\JsonResponse
     */
    protected function error($message, $status = 400)
    {
        return response()->json(['message' => $message], $status);
    }

    public function __construct(
        private AttachmentService $attachmentService
    ) {}

    /**
     * 获取附件列表
     *
     * @queryParam page integer 页码 Example: 1
     * @queryParam per_page integer 每页数量 Example: 20
     * @queryParam file_name string 文件名（模糊搜索） Example: example.pdf
     * @queryParam start_time string 开始时间 Example: 2024-01-01
     * @queryParam end_time string 结束时间 Example: 2024-12-31
     *
     * @apiResourceCollection App\Http\Resources\Admin\AttachmentResource
     *
     * @apiResourceModel App\Models\Attachment paginate=20 with=attachables
     */
    public function index(Request $request)
    {
        $params = $request->only([
            'file_name',
            'start_time',
            'end_time',
            'attachable_type',
            'attachable_id',
            'category',
            'storage_type',
        ]);
        $perPage = $request->get('per_page', 20);

        $attachments = $this->attachmentService->paginate($params, $perPage);

        return AttachmentResource::collection($attachments);
    }

    /**
     * 上传附件（本地上传）
     *
     * @bodyParam file file required 要上传的文件（最大10MB）
     */
    public function upload(AttachmentRequest $request)
    {
        try {
            $file = $request->file('file');

            // 大文件建议使用OSS直传
            if ($file->getSize() > 5 * 1024 * 1024) {
                return $this->error('大文件请使用OSS直传接口', 422);
            }

            $attachment = $this->attachmentService->upload($file);

            return (new AttachmentResource($attachment))->response()->setStatusCode(201);
        } catch (\Exception $e) {
            Log::error('文件上传失败', ['error' => $e->getMessage()]);

            return response()->json(['message' => '文件上传失败'], 500);
        }
    }

    /**
     * 获取附件详情
     *
     * @urlParam id integer required 附件ID
     */
    public function show(string $id)
    {
        $attachment = Attachment::findOrFail($id);

        return new AttachmentResource($attachment);
    }

    /**
     * 下载附件
     *
     * @urlParam id integer required 附件ID
     */
    public function download(string $id)
    {
        $attachment = Attachment::findOrFail($id);

        $content = $this->attachmentService->getFileContent($attachment);

        if (! $content) {
            return $this->error('文件不存在', 404);
        }

        return response($content, 200, [
            'Content-Type' => $attachment->mime_type,
            'Content-Disposition' => 'attachment; filename="'.$attachment->file_name.'"',
        ]);
    }

    /**
     * 删除附件
     *
     * @urlParam id integer required 附件ID
     */
    public function destroy(string $id)
    {
        return $this->error('暂不支持删除', 405);
        // $attachment = Attachment::findOrFail($id);
        // try {
        //     $this->attachmentService->delete($attachment);

        //     return response()->noContent();
        // } catch (\Exception $e) {
        //     Log::error('文件删除失败', ['error' => $e->getMessage()]);
        //     return $this->error('文件删除失败', 500);
        // }
    }



    /**
     * 获取STS临时凭证
     *
     * @bodyParam filename string required 文件名 Example: example.pdf
     * @bodyParam filesize integer required 文件大小（字节） Example: 1024000
     * @bodyParam mime_type string required MIME类型 Example: application/pdf
     * @bodyParam md5_hash string 文件MD5值（用于秒传） Example: 5d41402abc4b2a76b9719d911017c592
     *
     * @response {
     *   "quick_upload": false,
     *   "upload_id": "550e8400-e29b-41d4-a716-446655440000",
     *   "credentials": {
     *     "AccessKeyId": "STS.xxx",
     *     "AccessKeySecret": "xxx",
     *     "SecurityToken": "xxx",
     *     "Expiration": "2025-08-04T12:00:00Z"
     *   },
     *   "region": "cn-hangzhou",
     *   "bucket": "my-bucket",
     *   "endpoint": "https://oss-cn-hangzhou.aliyuncs.com",
     *   "prefix": "attachments/"
     * }
     */
    public function getSTSCredentials(STSCredentialsRequest $request)
    {
        try {
            $result = $this->attachmentService->getSTSCredentials($request->validated());

            // 如果是秒传成功，返回格式化的附件资源
            if (!empty($result['quick_upload']) && !empty($result['attachment'])) {
                // attachment 是 Attachment 模型实例
                return new AttachmentResource($result['attachment']);
            }

            // 否则返回STS凭证
            return response()->json($result);
        } catch (\Exception $e) {
            Log::error('获取STS凭证失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return $this->error($e->getMessage(), 500);
        }
    }

    /**
     * 确认上传完成
     *
     * @bodyParam upload_id string required 上传ID Example: 550e8400-e29b-41d4-a716-446655440000
     * @bodyParam object_key string required OSS对象键值 Example: attachments/2025/08/04/xxx.pdf
     * @bodyParam filename string 文件名（可选） Example: example.pdf
     * @bodyParam filesize integer 文件大小（可选） Example: 1024000
     * @bodyParam mime_type string MIME类型（可选） Example: application/pdf
     *
     * @apiResource App\Http\Resources\Admin\AttachmentResource
     * @apiResourceModel App\Models\Attachment
     */
    public function confirmUpload(ConfirmUploadRequest $request)
    {
        try {
            $attachment = $this->attachmentService->confirmUpload($request->validated());

            return new AttachmentResource($attachment);
        } catch (\Exception $e) {
            Log::error('确认上传失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return $this->error($e->getMessage(), 500);
        }
    }

    /**
     * 根据业务ID获取附件列表
     *
     * @queryParam attachable_type string required 业务类型 Example: App\Models\Entity
     * @queryParam attachable_id integer required 业务ID Example: 1
     * @queryParam category string 附件分类 Example: contract
     */
    public function getByBusiness(AttachmentBusinessRequest $request)
    {

        try {
            $modelClass = $request->get('attachable_type');
            $modelId = $request->get('attachable_id');
            $category = $request->get('category');

            // 验证模型类是否存在
            if (! class_exists($modelClass)) {
                return $this->error('无效的业务类型', 422);
            }

            // 查找业务实体
            $model = $modelClass::find($modelId);
            if (! $model) {
                return $this->error('业务实体不存在', 404);
            }

            // 获取附件列表
            $attachments = $this->attachmentService->getByModel($model, $category);

            // 转换响应格式，包含关联信息
            $data = $attachments->map(function ($attachment) {
                $result = new AttachmentResource($attachment);
                $result = $result->toArray(request());

                // 添加关联信息
                $result['category'] = $attachment->pivot->category;
                $result['sort'] = $attachment->pivot->sort;
                $result['description'] = $attachment->pivot->description;

                return $result;
            });

            return response()->json($data);
        } catch (\Exception $e) {
            Log::error('获取业务附件失败', ['error' => $e->getMessage()]);

            return $this->error('获取业务附件失败', 500);
        }
    }

    /**
     * 更新文件关联描述
     *
     * @urlParam attachable_relation_id integer required 附件关联ID
     *
     *
     * @bodyParam description string 描述 Example: 这是我的描述
     */
    public function updateByAttachmentRelation(AttachmentRelation $attachmentRelation, Request $request)
    {
        $this->attachmentService->updateRelation($attachmentRelation->id, $request->only(['description']));

        return response()->json(['message' => '更新成功'])->setStatusCode(201);
    }
}
