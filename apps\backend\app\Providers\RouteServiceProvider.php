<?php

namespace App\Providers;

use Illuminate\Cache\RateLimiting\Limit;
use Illuminate\Foundation\Support\Providers\RouteServiceProvider as ServiceProvider;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\RateLimiter;

class RouteServiceProvider extends ServiceProvider
{
    /**
     * Define your route model bindings, pattern filters, and other route configuration.
     */
    public function boot(): void
    {
        // 配置路由模型绑定、模式过滤器等
        $this->configureRateLimiting();

        // Laravel 12中不需要手动注册路由，因为bootstrap/app.php已经配置了
        // 注释掉以避免路由重复注册
        // $this->routes(function () {
        //     Route::middleware('api')
        //         ->group(base_path('routes/api.php'));

        //     Route::middleware('web')
        //         ->group(base_path('routes/web.php'));
        // });
    }

    /**
     * 配置路由限流
     */
    protected function configureRateLimiting(): void
    {
        // 配置登录限流器
        RateLimiter::for('login', function (Request $request) {
            return Limit::perMinute(5)->by($request->ip());
        });

        // 配置 API 通用限流器
        RateLimiter::for('api', function (Request $request) {
            return Limit::perMinute(60)->by($request->user()?->id ?: $request->ip());
        });
    }
}
