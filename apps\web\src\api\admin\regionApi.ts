import request from '@/utils/http'
import type { RegionTreeItem, RegionSearchItem, RegionPathResponse } from '@/types/api'

/**
 * 获取地区树形结构
 */
export const getRegionTree = (deep = 3): Promise<RegionTreeItem[]> => {
  return request.get<RegionTreeItem[]>({
    url: '/admin/regions/tree',
    params: { deep }
  })
}

/**
 * 获取指定父级的子地区
 */
export const getRegionChildren = (parentId: number | string): Promise<RegionTreeItem[]> => {
  return request.get<RegionTreeItem[]>({
    url: `/admin/regions/children/${parentId}`
  })
}

/**
 * 根据地区代码获取完整路径
 */
export const getRegionPath = (code: string): Promise<RegionPathResponse> => {
  return request.get<RegionPathResponse>({
    url: `/admin/regions/path/${code}`
  })
}

/**
 * 搜索地区
 */
export const searchRegions = (params: {
  keyword: string
  limit?: number
  deep?: number
}): Promise<RegionSearchItem[]> => {
  return request.get<RegionSearchItem[]>({
    url: '/admin/regions/search',
    params
  })
}

/**
 * 获取省份列表
 */
export const getProvinces = (): Promise<RegionTreeItem[]> => {
  return request.get<RegionTreeItem[]>({
    url: '/admin/regions/provinces'
  })
}

/**
 * 获取指定省份的城市列表
 */
export const getCities = (provinceId: number | string): Promise<RegionTreeItem[]> => {
  return request.get<RegionTreeItem[]>({
    url: `/admin/regions/cities/${provinceId}`
  })
}

/**
 * 获取指定城市的区县列表
 */
export const getDistricts = (cityId: number | string): Promise<RegionTreeItem[]> => {
  return request.get<RegionTreeItem[]>({
    url: `/admin/regions/districts/${cityId}`
  })
}
