import request from '@/utils/http'
import type { Lifecycle, LifecycleFollowUp } from '@/types/api'

/**
 * 获取生命周期列表
 */
export const getLifecycleList = (params?: {
  page?: number
  per_page?: number
  asset_id?: number
  type?: string
  start_date?: string
  end_date?: string
  initiator_id?: number
  acceptance_entity_id?: number
}): Promise<{
  data: Lifecycle[]
  meta: {
    total: number
    per_page: number
    current_page: number
    last_page: number
  }
}> => {
  return request.get({
    url: '/admin/lifecycles',
    params
  })
}

/**
 * 获取生命周期详情
 */
export const getLifecycleDetail = (id: number): Promise<Lifecycle> => {
  return request.get({
    url: `/admin/lifecycles/${id}`
  })
}

/**
 * 创建生命周期
 */
export const createLifecycle = (data: {
  asset_id?: number
  type: string
  date: string
  initiator_id?: number
  content: string
  assistants: number[]
  acceptance_entity_id?: number
  acceptance_personnel_id?: number
  acceptance_time: string
  attachments?: number[]
}): Promise<Lifecycle> => {
  return request.post({
    url: '/admin/lifecycles',
    data
  })
}

/**
 * 更新生命周期
 */
export const updateLifecycle = (
  id: number,
  data: Partial<{
    asset_id?: number
    type: string
    date: string
    initiator_id: number
    content: string
    assistants: number[]
    acceptance_entity_id: number
    acceptance_personnel_id: number
    acceptance_time: string
    attachments?: number[]
  }>
): Promise<Lifecycle> => {
  return request.put({
    url: `/admin/lifecycles/${id}`,
    data
  })
}

/**
 * 删除生命周期
 */
export const deleteLifecycle = (id: number): Promise<void> => {
  return request.del({
    url: `/admin/lifecycles/${id}`
  })
}

/**
 * 获取验收人员列表
 */
export const getAcceptancePersonnel = (
  entityId: number
): Promise<
  Array<{
    id: number
    name: string
    phone: string
    position?: string
    department?: string
  }>
> => {
  return request.get({
    url: `/admin/lifecycles/entities/${entityId}/acceptance-personnel`
  })
}

/**
 * 获取跟进记录详情
 */
export const getFollowUpDetail = (lifecycleId: number, id: number): Promise<LifecycleFollowUp> => {
  return request.get({
    url: `/admin/lifecycles/${lifecycleId}/follow-ups/${id}`
  })
}

/**
 * 创建跟进记录
 */
export const createFollowUp = (
  lifecycleId: number,
  data: {
    date: string
    person_id: number
    content: string
    attachments?: number[]
  }
): Promise<LifecycleFollowUp> => {
  return request.post({
    url: `/admin/lifecycles/${lifecycleId}/follow-ups`,
    data
  })
}

/**
 * 更新跟进记录
 */
export const updateFollowUp = (
  lifecycleId: number,
  id: number,
  data: Partial<{
    date: string
    person_id: number
    content: string
    attachments?: number[]
  }>
): Promise<LifecycleFollowUp> => {
  return request.put({
    url: `/admin/lifecycles/${lifecycleId}/follow-ups/${id}`,
    data
  })
}

/**
 * 删除跟进记录
 */
export const deleteFollowUp = (lifecycleId: number, id: number): Promise<void> => {
  return request.del({
    url: `/admin/lifecycles/${lifecycleId}/follow-ups/${id}`
  })
}
