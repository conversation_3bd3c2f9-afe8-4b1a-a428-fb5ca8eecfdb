<?php

namespace App\Services;

use App\Models\Menu;
use App\Models\OperationLog;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class OperationLogService
{
    /**
     * 记录操作日志
     */
    public function logOperation(Request $request, array $extraData = [])
    {
        // 跳过GET请求的日志记录
        if ($request->method() === 'GET') {
            return;
        }

        try {
            $user = $request->user();
            if (!$user) {
                \Log::warning('No authenticated user found for operation log', [
                    'path' => $request->path(),
                    'method' => $request->method()
                ]);
                return;
            }

            $routeName = $request->route()->getName();

            $menuInfo = $this->getMenuInfoByRouteName($routeName, $user);

            if (empty($menuInfo)) {
                \Log::info('No menu found for route', [
                    'route_name' => $routeName,
                    'path' => $request->path()
                ]);
                return;
            }

            $operationInfo = $this->getOperationInfo($request, $extraData);
            $targetInfo = $this->extractTargetInfo($request->path(), $request->all());

            $logData = [
                'user_id' => $user->id,
                'user_name' => $user->nickname ?? $user->account ?? '未知用户',
                'menu_id' => $menuInfo['menu_id'],
                'menu_name' => $menuInfo['menu_name'],
                'operation_type' => $operationInfo['operation_type'] ?? 'unknown',
                'operation_description' => $operationInfo['operation_description'] ?? '未识别操作',
                'target_type' => $targetInfo['target_type'] ?? 'unknown',
                'target_id' => $targetInfo['target_id'] ?? 0,
                'target_name' => $targetInfo['target_name'] ?? '未知对象',
                'method' => $request->method(),
                'path' => $request->path(),
                'ip' => $request->ip(),
                'headers' => $this->filterHeaders($request->headers->all()),
                'params' => $this->filterParams($request->all()),
                'user_agent' => $request->userAgent(),
            ];

            OperationLog::create($logData);
        } catch (\Exception $e) {
            \Log::error('Failed to log operation: ' . $e->getMessage(), [
                'exception' => $e,
                'user' => $user ?? null,
                'request' => $request->all(),
                'headers' => $request->headers->all(),
                'path' => $request->path(),
            ]);
        }
    }

    /**
     * 通过路由名称获取菜单信息
     */
    protected function getMenuInfoByRouteName(?string $routeName, User $user): array
    {
        if (!$routeName) {
            return [];
        }

        // 通过关联查询获取菜单权限信息
        $menuPermission = \App\Models\MenuPermission::where('route_name', $routeName)->with(['menu'])->first();

        if (!$menuPermission || !$menuPermission->menu) {
            return [];
        }

        return [
            'menu_id' => $menuPermission->menu->id,
            'menu_name' => $menuPermission->menu->title,
            'menu_permission_id' => $menuPermission->id,
            'menu_permission_title' => $menuPermission->title,
        ];
    }

    /**
     * 获取菜单信息
     */
    protected function getMenuInfo(Request $request)
    {
        $path = $this->extractMenuPath($request->path());

        // 更精确的菜单匹配
        $menu = Menu::where(function($query) use ($path) {
            $query->where('path', 'like', '%' . $path)
                  ->orWhere('component', 'like', '%' . $path);
        })->where('status', true)->first();

        if ($menu) {
            return [
                'menu_id' => $menu->id,
                'menu_name' => $menu->title,
            ];
        }

        // 如果没有找到精确匹配，尝试查找父级菜单
        $pathParts = explode('/', trim($path, '/'));
        if (count($pathParts) > 1) {
            $parentPath = $pathParts[0];
            $menu = Menu::where(function($query) use ($parentPath) {
                $query->where('path', 'like', '%' . $parentPath)
                      ->orWhere('component', 'like', '%' . $parentPath);
            })->where('status', true)->first();

            if ($menu) {
                return [
                    'menu_id' => $menu->id,
                    'menu_name' => $menu->title,
                ];
            }
        }

        return [];
    }

    /**
     * 提取菜单路径
     */
    protected function extractMenuPath($path)
    {
        // 移除API前缀并清理路径
        $path = preg_replace('/^api\/admin\//', '', $path);

        // 移除ID参数
        $path = preg_replace('/\/\d+/', '', $path);

        // 移除查询参数
        $path = explode('?', $path)[0];

        return trim($path, '/');
    }

    /**
     * 获取操作信息
     */
    protected function getOperationInfo(Request $request, array $extraData)
    {
        $method = $request->method();
        $path = $request->path();
        $params = $request->all();

        // 从extraData中获取预定义的操作信息
        if (!empty($extraData['operation_type'])) {
            return [
                'operation_type' => $extraData['operation_type'],
                'operation_description' => isset($extraData['operation_description']) ? $extraData['operation_description'] : null,
                'target_type' => isset($extraData['target_type']) ? $extraData['target_type'] : null,
                'target_id' => isset($extraData['target_id']) ? $extraData['target_id'] : null,
                'target_name' => isset($extraData['target_name']) ? $extraData['target_name'] : null,
            ];
        }

        // 根据HTTP方法和路径推断操作类型
        $operationType = $this->inferOperationType($method, $path);
        $targetInfo = $this->extractTargetInfo($path, $params);

        return array_merge($operationType, $targetInfo);
    }

    /**
     * 推断操作类型
     */
    protected function inferOperationType($method, $path)
    {
        $operationType = '';
        $description = '';

        switch ($method) {
            case 'GET':
                if (str_contains($path, 'export')) {
                    $operationType = 'export';
                    $description = '导出数据';
                } elseif (str_contains($path, 'download')) {
                    $operationType = 'download';
                    $description = '下载文件';
                } else {
                    $operationType = 'view';
                    $description = '查看数据';
                }
                break;
            case 'POST':
                if (str_contains($path, 'upload')) {
                    $operationType = 'upload';
                    $description = '上传文件';
                } else {
                $operationType = 'create';
                $description = '创建数据';
                }
                break;
            case 'PUT':
            case 'PATCH':
                $operationType = 'update';
                $description = '更新数据';
                break;
            case 'DELETE':
                $operationType = 'delete';
                $description = '删除数据';
                break;
        }

        return [
            'operation_type' => $operationType,
            'operation_description' => $description,
        ];
    }

    /**
     * 提取目标信息
     */
    protected function extractTargetInfo($path, $params)
    {
        $targetType = '';
        $targetId = null;
        $targetName = '';

        // 从路径中提取目标类型
        if (preg_match('/\/([a-z-]+)(?:\/(\d+))?$/', $path, $matches)) {
            $resourceName = isset($matches[1]) ? $matches[1] : '';
            $targetId = isset($matches[2]) ? $matches[2] : null;

            // 映射资源名称到中文
            $typeMap = [
                'assets' => 'Asset',
                'users' => 'User',
                'menus' => 'Menu',
                'roles' => 'Role',
                'categories' => 'Category',
                'attachments' => 'Attachment',
                'operation-logs' => 'OperationLog',
                'configs' => 'Config',
            ];

            $targetType = isset($typeMap[$resourceName]) ? $typeMap[$resourceName] : ucfirst(rtrim($resourceName, 's'));
        }

        // 从参数中提取目标名称
        if (!empty($params['name'])) {
            $targetName = $params['name'];
        } elseif (!empty($params['title'])) {
            $targetName = $params['title'];
        }

        return [
            'target_type' => $targetType,
            'target_id' => $targetId,
            'target_name' => $targetName,
        ];
    }

    /**
     * 过滤请求头信息，移除敏感信息
     */
    protected function filterHeaders($headers)
    {
        $sensitiveHeaders = ['authorization', 'cookie', 'x-xsrf-token'];

        return collect($headers)
            ->filter(function($value, $key) use ($sensitiveHeaders) {
                return !in_array(strtolower($key), $sensitiveHeaders);
            })
            ->toArray();
    }

    /**
     * 过滤请求参数，移除敏感信息
     */
    protected function filterParams($params)
    {
        $sensitiveFields = ['password', 'password_confirmation', 'token'];

        return collect($params)
            ->map(function ($value, $key) {
                // 处理上传文件
                if ($value instanceof \Illuminate\Http\UploadedFile) {
                    return [
                        'name' => $value->getClientOriginalName(),
                        'size' => $value->getSize(),
                        'mime_type' => $value->getMimeType(),
                        'type' => 'file'
                    ];
                }
                return $value;
            })
            ->filter(function($value, $key) use ($sensitiveFields) {
                return !in_array(strtolower($key), $sensitiveFields);
            })
            ->toArray();
    }

    /**
     * 手动记录特定操作
     */
    public function logSpecificOperation($operationType, $description, array $targetInfo = [])
    {
        try {
            $user = Auth::user();
            $request = request();

            $logData = [
                'user_id' => $user ? $user->id : null,
                'user_name' => $user ? $user->name : null,
                'operation_type' => $operationType,
                'operation_description' => $description,
                'target_type' => isset($targetInfo['target_type']) ? $targetInfo['target_type'] : null,
                'target_id' => isset($targetInfo['target_id']) ? $targetInfo['target_id'] : null,
                'target_name' => isset($targetInfo['target_name']) ? $targetInfo['target_name'] : null,
                'method' => $request->method(),
                'path' => $request->path(),
                'ip' => $request->ip(),
                'headers' => $this->filterHeaders($request->headers->all()),
                'params' => $this->filterParams($request->all()),
                'user_agent' => $request->userAgent(),
            ];

            OperationLog::create($logData);
        } catch (\Exception $e) {
            \Log::error('Failed to log specific operation: ' . $e->getMessage(), [
                'exception' => $e,
                'operation_type' => $operationType,
            ]);
        }
    }

    protected function checkWarning($user, $menuInfo, $operationInfo, $targetInfo)
    {
        $warnings = [];
        if (!$user) $warnings[] = 'user';
        if (!isset($menuInfo['menu_id']) || empty($menuInfo['menu_id'])) $warnings[] = 'menu';
        if (!isset($operationInfo['operation_type']) || empty($operationInfo['operation_type'])) $warnings[] = 'operation_type';
        if (!isset($targetInfo['target_id']) || empty($targetInfo['target_id'])) $warnings[] = 'target';
        return implode(',', $warnings);
    }
}
