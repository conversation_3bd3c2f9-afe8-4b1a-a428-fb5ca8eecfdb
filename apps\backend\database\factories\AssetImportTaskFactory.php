<?php

namespace Database\Factories;

use App\Models\AssetImportTask;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\AssetImportTask>
 */
class AssetImportTaskFactory extends Factory
{
    protected $model = AssetImportTask::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $statuses = ['pending', 'processing', 'completed', 'failed'];
        $status = $this->faker->randomElement($statuses);
        
        $totalRows = $this->faker->numberBetween(10, 100);
        $successRows = $status === 'completed' ? $this->faker->numberBetween(5, $totalRows) : 0;
        $failedRows = $status === 'completed' ? $totalRows - $successRows : 0;

        return [
            'file_path' => 'asset_imports/' . $this->faker->uuid . '.xlsx',
            'original_filename' => $this->faker->word . '_assets.xlsx',
            'status' => $status,
            'total_rows' => $totalRows,
            'success_rows' => $successRows,
            'failed_rows' => $failedRows,
            'error_details' => $status === 'failed' ? ['error' => 'Test error'] : null,
            'summary' => $status === 'completed' ? "导入完成：总计 {$totalRows} 行，成功 {$successRows} 行，失败 {$failedRows} 行" : null,
            'created_by' => User::factory(),
            'started_at' => in_array($status, ['processing', 'completed', 'failed']) ? time() - 3600 : null,
            'completed_at' => in_array($status, ['completed', 'failed']) ? time() : null,
            'created_at' => time() - 7200,
            'updated_at' => time() - 3600,
        ];
    }

    /**
     * 待处理状态
     */
    public function pending(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'pending',
            'total_rows' => 0,
            'success_rows' => 0,
            'failed_rows' => 0,
            'started_at' => null,
            'completed_at' => null,
        ]);
    }

    /**
     * 处理中状态
     */
    public function processing(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'processing',
            'started_at' => time() - 1800,
            'completed_at' => null,
        ]);
    }

    /**
     * 已完成状态
     */
    public function completed(): static
    {
        return $this->state(function (array $attributes) {
            $totalRows = $this->faker->numberBetween(10, 100);
            $successRows = $this->faker->numberBetween(5, $totalRows);
            $failedRows = $totalRows - $successRows;

            return [
                'status' => 'completed',
                'total_rows' => $totalRows,
                'success_rows' => $successRows,
                'failed_rows' => $failedRows,
                'summary' => "导入完成：总计 {$totalRows} 行，成功 {$successRows} 行，失败 {$failedRows} 行",
                'started_at' => time() - 3600,
                'completed_at' => time() - 1800,
            ];
        });
    }

    /**
     * 失败状态
     */
    public function failed(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'failed',
            'error_details' => ['error' => 'Excel文件格式不正确'],
            'started_at' => time() - 3600,
            'completed_at' => time() - 1800,
        ]);
    }
}
