<template>
  <ElDrawer
    v-model="visible"
    :title="isEdit ? '编辑资产' : '新增资产'"
    :size="drawerSize"
    direction="rtl"
    :close-on-click-modal="false"
    @closed="handleClosed"
    class="asset-form-drawer"
  >
    <div class="drawer-body" v-loading="loading">
      <ElScrollbar class="drawer-scrollbar">
        <ElForm
          ref="formRef"
          :model="formData"
          :rules="rules"
          label-width="120px"
          class="drawer-form"
        >
          <!-- 基础信息 -->
          <div class="form-section">
            <h3 class="section-title">基础信息</h3>
            <ElRow :gutter="20">
              <ElCol :xs="24" :sm="24" :md="12">
                <ElFormItem label="资产名称" prop="name">
                  <ElInput v-model="formData.name" placeholder="请输入资产名称" maxlength="200" />
                </ElFormItem>
              </ElCol>
              <ElCol :xs="24" :sm="24" :md="12">
                <ElFormItem label="品牌" prop="brand">
                  <ElInput v-model="formData.brand" placeholder="请输入品牌" maxlength="100" />
                </ElFormItem>
              </ElCol>
              <ElCol :xs="24" :sm="24" :md="12">
                <ElFormItem label="规格型号" prop="model">
                  <ElInput v-model="formData.model" placeholder="请输入规格型号" maxlength="100" />
                </ElFormItem>
              </ElCol>
              <ElCol :xs="24" :sm="24" :md="12">
                <ElFormItem label="序列号" prop="serial_number">
                  <ElInput
                    v-model="formData.serial_number"
                    placeholder="请输入序列号"
                    maxlength="100"
                  />
                </ElFormItem>
              </ElCol>
              <ElCol :xs="24" :sm="24" :md="12">
                <ElFormItem label="资产来源" prop="asset_source">
                  <NullableSelect v-model="formData.asset_source" placeholder="请选择资产来源">
                    <ElOption
                      v-for="item in assetSourceOptions"
                      :key="item.code"
                      :label="item.value"
                      :value="item.code"
                    />
                  </NullableSelect>
                </ElFormItem>
              </ElCol>
              <ElCol :xs="24" :sm="24" :md="12">
                <ElFormItem label="资产状态" prop="asset_status">
                  <NullableSelect v-model="formData.asset_status" placeholder="请选择资产状态">
                    <ElOption
                      v-for="item in assetStatusOptions"
                      :key="item.code"
                      :label="item.value"
                      :value="item.code"
                    />
                  </NullableSelect>
                </ElFormItem>
              </ElCol>
              <ElCol :xs="24" :sm="24" :md="12">
                <ElFormItem label="成色" prop="asset_condition">
                  <NullableSelect v-model="formData.asset_condition" placeholder="请选择成色">
                    <ElOption
                      v-for="item in assetConditionOptions"
                      :key="item.code"
                      :label="item.value"
                      :value="item.code"
                    />
                  </NullableSelect>
                </ElFormItem>
              </ElCol>
              <ElCol :xs="24" :sm="24" :md="12">
                <ElFormItem label="是否附属设备">
                  <ElRadioGroup v-model="isAccessory">
                    <ElRadio :value="false">否</ElRadio>
                    <ElRadio :value="true">是</ElRadio>
                  </ElRadioGroup>
                </ElFormItem>
              </ElCol>
              <ElCol :xs="24" :sm="24" :md="12" v-if="isAccessory">
                <ElFormItem label="主设备" prop="parent_id">
                  <NullableSelect
                    v-model="formData.parent_id"
                    placeholder="请选择主设备"
                    filterable
                    remote
                    :remote-method="searchParentAsset"
                    :loading="parentAssetLoading"
                    clearable
                  >
                    <ElOption
                      v-for="item in parentAssetOptions"
                      :key="item.id"
                      :label="`${item.name} ${item.brand ? '(' + item.brand + ')' : ''}`"
                      :value="item.id"
                    />
                  </NullableSelect>
                </ElFormItem>
              </ElCol>
            </ElRow>
          </div>

          <!-- 分类信息 -->
          <div class="form-section">
            <h3 class="section-title">分类信息</h3>
            <ElRow :gutter="20">
              <ElCol v-for="group in categoryGroups" :key="group.id" :xs="24" :sm="12" :md="8">
                <ElFormItem :label="group.name">
                  <ElCascader
                    v-model="selectedCategories[group.id]"
                    :options="group.children"
                    :props="cascaderProps"
                    :placeholder="`请选择${group.name}`"
                    clearable
                    filterable
                    style="width: 100%"
                    @change="(value) => handleCategoryChange(value as number[], group.id)"
                  />
                </ElFormItem>
              </ElCol>
            </ElRow>
          </div>

          <!-- 地址信息 -->
          <div class="form-section">
            <h3 class="section-title">地址信息</h3>
            <ElRow :gutter="20">
              <ElCol :span="24">
                <ElFormItem label="所在地区" prop="region_code">
                  <RegionSelector
                    v-model="formData.region_code"
                    placeholder="请选择省市区"
                    :filterable="true"
                    :show-all-levels="true"
                    @change="handleRegionChange"
                  />
                </ElFormItem>
              </ElCol>
              <ElCol :span="24">
                <ElFormItem label="详细地址" prop="detailed_address">
                  <ElInput
                    v-model="formData.detailed_address"
                    type="textarea"
                    :rows="2"
                    placeholder="请输入详细地址"
                    maxlength="500"
                  />
                </ElFormItem>
              </ElCol>
            </ElRow>
          </div>

          <!-- 时间信息 -->
          <div class="form-section">
            <h3 class="section-title">时间信息</h3>
            <ElRow :gutter="20">
              <ElCol :xs="24" :sm="24" :md="12">
                <ElFormItem label="启用日期" prop="start_date">
                  <ElDatePicker
                    v-model="formData.start_date"
                    type="date"
                    placeholder="选择启用日期"
                    :value-format="'X'"
                    style="width: 100%"
                  />
                </ElFormItem>
              </ElCol>
              <ElCol :xs="24" :sm="24" :md="12">
                <ElFormItem label="合同质保期" prop="warranty_period">
                  <ElInputNumber
                    v-model="formData.warranty_period"
                    :min="0"
                    :max="999"
                    placeholder="月"
                    style="width: 100%"
                  />
                  <div class="form-tip">单位：月</div>
                </ElFormItem>
              </ElCol>
              <ElCol :xs="24" :sm="24" :md="12">
                <ElFormItem label="质保期预警" prop="warranty_alert">
                  <ElInputNumber
                    v-model="formData.warranty_alert"
                    :min="0"
                    :max="999"
                    placeholder="天"
                    style="width: 100%"
                  />
                  <div class="form-tip">质保到期前多少天开始预警</div>
                </ElFormItem>
              </ElCol>
              <ElCol :xs="24" :sm="24" :md="12">
                <ElFormItem label="维护周期" prop="maintenance_cycle">
                  <ElInputNumber
                    v-model="formData.maintenance_cycle"
                    :min="1"
                    :max="9999"
                    placeholder="天"
                    style="width: 100%"
                  />
                  <div class="form-tip">单位：天</div>
                </ElFormItem>
              </ElCol>
              <ElCol :xs="24" :sm="24" :md="12">
                <ElFormItem label="预计使用年限" prop="expected_years">
                  <ElInputNumber
                    v-model="formData.expected_years"
                    :min="1"
                    :max="99"
                    placeholder="年"
                    style="width: 100%"
                  />
                  <div class="form-tip">单位：年</div>
                </ElFormItem>
              </ElCol>
            </ElRow>
          </div>

          <!-- 附件信息 -->
          <div class="form-section">
            <h3 class="section-title">附件信息</h3>
            <ElRow :gutter="20">
              <ElCol :span="24">
                <ElFormItem label="资产图片" prop="attachments">
                  <AttachmentUpload
                    v-model="formData.attachments"
                    :attachments="currentAttachments"
                    :limit="9"
                    :max-size="10"
                    button-text="上传图片"
                  />
                </ElFormItem>
              </ElCol>
            </ElRow>
          </div>

          <!-- 相关主体 -->
          <div class="form-section">
            <h3 class="section-title">
              相关主体
              <ElButton
                type="primary"
                size="small"
                :icon="Plus"
                circle
                style="margin-left: 10px"
                @click="handleAddRelatedEntity"
                :disabled="!canAddRelatedEntity"
              />
            </h3>
            <div
              v-for="(entity, index) in formData.related_entities"
              :key="index"
              class="related-entity-item"
            >
              <ElRow :gutter="20">
                <ElCol :span="24">
                  <div class="entity-header">
                    <span class="entity-index">主体 {{ index + 1 }}</span>
                    <ElButton
                      type="danger"
                      size="small"
                      :icon="Delete"
                      circle
                      @click="handleRemoveRelatedEntity(index)"
                    />
                  </div>
                </ElCol>
                <ElCol :xs="24" :sm="24" :md="12">
                  <ElFormItem :label="'主体类型'" :prop="`related_entities.${index}.entity_type`">
                    <NullableSelect
                      v-model="entity.entity_type"
                      placeholder="请选择主体类型"
                      @change="() => handleEntityTypeChange(index)"
                    >
                      <ElOption
                        v-for="type in getAvailableEntityTypesForIndex()"
                        :key="type.code"
                        :label="type.value"
                        :value="type.code"
                      />
                    </NullableSelect>
                  </ElFormItem>
                </ElCol>
                <ElCol :xs="24" :sm="24" :md="12">
                  <ElFormItem :label="'主体名称'" :prop="`related_entities.${index}.entity_id`">
                    <NullableSelect
                      v-model="entity.entity_id"
                      placeholder="请选择主体"
                      filterable
                      :disabled="!entity.entity_type"
                      @change="(val: number | null) => handleEntityChange(index, val)"
                    >
                      <ElOption
                        v-for="item in entityOptionsMap[index] || []"
                        :key="item.id"
                        :label="item.name"
                        :value="item.id"
                      />
                    </NullableSelect>
                  </ElFormItem>
                </ElCol>
                <!-- 联系人选择按钮 -->
                <ElCol :xs="24" :sm="24" :md="24">
                  <ElFormItem label=" ">
                    <ElButton
                      :icon="User"
                      :disabled="!entity.entity_id"
                      @click="showContactDialog(index)"
                    >
                      选择联系人快速填充
                    </ElButton>
                    <span class="contact-tips" v-if="!entity.entity_id"> 请先选择主体 </span>
                  </ElFormItem>
                </ElCol>
                <ElCol :xs="24" :sm="24" :md="12">
                  <ElFormItem
                    :label="
                      entityKeywordsMap[index]
                        ? `${entityKeywordsMap[index]}联系人姓名`
                        : '联系人姓名'
                    "
                    :prop="`related_entities.${index}.contact_name`"
                  >
                    <ElInput
                      v-model="entity.contact_name"
                      :placeholder="!entity.entity_id ? '请先选择主体' : '请输入联系人姓名'"
                      :disabled="!entity.entity_id"
                    />
                  </ElFormItem>
                </ElCol>
                <ElCol :xs="24" :sm="24" :md="12">
                  <ElFormItem
                    :label="
                      entityKeywordsMap[index] ? `${entityKeywordsMap[index]}联系电话` : '联系电话'
                    "
                    :prop="`related_entities.${index}.contact_phone`"
                  >
                    <ElInput
                      v-model="entity.contact_phone"
                      :placeholder="!entity.entity_id ? '请先选择主体' : '请输入联系电话'"
                      :disabled="!entity.entity_id"
                    />
                  </ElFormItem>
                </ElCol>
                <ElCol :xs="24" :sm="24" :md="12">
                  <ElFormItem :label="'职位'" :prop="`related_entities.${index}.position`">
                    <ElInput
                      v-model="entity.position"
                      :placeholder="!entity.entity_id ? '请先选择主体' : '请输入职位'"
                      :disabled="!entity.entity_id"
                    />
                  </ElFormItem>
                </ElCol>
                <ElCol :xs="24" :sm="24" :md="12">
                  <ElFormItem :label="'所在部门'" :prop="`related_entities.${index}.department`">
                    <ElInput
                      v-model="entity.department"
                      :placeholder="!entity.entity_id ? '请先选择主体' : '请输入所在部门'"
                      :disabled="!entity.entity_id"
                    />
                  </ElFormItem>
                </ElCol>
              </ElRow>
            </div>
          </div>

          <!-- 备注 -->
          <div class="form-section">
            <h3 class="section-title">备注信息</h3>
            <ElRow :gutter="20">
              <ElCol :span="24">
                <ElFormItem label="备注" prop="remark">
                  <ElInput
                    v-model="formData.remark"
                    type="textarea"
                    :rows="3"
                    placeholder="请输入备注"
                    maxlength="1000"
                  />
                </ElFormItem>
              </ElCol>
            </ElRow>
          </div>
        </ElForm>
      </ElScrollbar>
    </div>

    <template #footer>
      <ElButton @click="handleCancel">取消</ElButton>
      <ElButton type="primary" @click="handleSubmit" :loading="submitLoading">
        {{ isEdit ? '保存' : '确定' }}
      </ElButton>
    </template>
  </ElDrawer>

  <!-- 联系人选择对话框 -->
  <ContactSelectDialog
    v-model="contactDialogVisible"
    :contacts="currentEntityContacts"
    @select="handleContactSelect"
  />
</template>

<script setup lang="ts">
  defineOptions({ name: 'AssetFormDrawer' })

  import { ref, reactive, computed, watch } from 'vue'
  import { ElMessage, ElCascader, type FormInstance, type FormRules } from 'element-plus'
  import { Plus, Delete, User } from '@element-plus/icons-vue'

  // Components
  import { AttachmentUpload } from '@/components/custom/upload'
  import NullableSelect from '@/components/custom/nullable-select/index.vue'
  import { RegionSelector } from '@/components/custom/region-selector'
  import ContactSelectDialog from './ContactSelectDialog.vue'

  // API
  import { getAssetDetail, createAsset, updateAsset, getMainAssetList } from '@/api/admin/assetApi'
  import { getEntityList, getEntityDetail } from '@/api/admin/entityApi'
  import { getCategoryList } from '@/api/admin/categoryApi'
  import { useDictionaryStore } from '@/store/modules/dictionary'

  // Types
  import type { Asset, AssetFormData, MainAssetQueryParams } from '@/types/api/asset'
  import type { DictionaryItem } from '@/types/api/dictionary'
  import type { Category } from '@/types/api/category'
  import type { Entity, Contact } from '@/types/api/entity'
  import type { AttachmentItem } from '@/types/api/attachment'
  import type { CascaderProps } from 'element-plus'

  interface Props {
    modelValue: boolean
    assetId?: number
  }

  const props = defineProps<Props>()
  const emit = defineEmits<{
    'update:modelValue': [value: boolean]
    success: []
  }>()

  // 计算属性
  const visible = computed({
    get: () => props.modelValue,
    set: (val) => emit('update:modelValue', val)
  })
  const isEdit = computed(() => !!props.assetId)

  // Store
  const dictionaryStore = useDictionaryStore()

  // 表单相关
  const formRef = ref<FormInstance>()
  const loading = ref(false)
  const submitLoading = ref(false)

  // 是否附属设备（仅用于UI控制，不提交到后端）
  const isAccessory = ref(false)

  // 响应式抽屉大小
  const drawerSize = computed(() => {
    // 根据屏幕宽度动态调整抽屉大小
    if (window.innerWidth < 768) {
      return '100%'
    } else if (window.innerWidth < 1200) {
      return '80%'
    } else {
      return '65%'
    }
  })

  // 表单数据
  const formData = reactive<AssetFormData>({
    name: '',
    brand: '',
    model: '',
    serial_number: '',
    asset_category_ids: [],
    asset_source: null,
    asset_status: null,
    asset_condition: null,
    parent_id: null,
    region_code: '',
    detailed_address: '',
    start_date: '',
    warranty_period: null,
    warranty_alert: null,
    maintenance_cycle: null,
    expected_years: null,
    related_entities: [],
    attachments: [],
    remark: ''
  })

  // 当前附件（用于编辑时显示）
  const currentAttachments = ref<AttachmentItem[]>([])

  // 字典选项
  const assetSourceOptions = ref<DictionaryItem[]>([])
  const assetStatusOptions = ref<DictionaryItem[]>([])
  const assetConditionOptions = ref<DictionaryItem[]>([])

  // 分类相关数据
  interface CategoryGroup {
    id: number
    name: string
    code: string
    children: Category[]
  }
  const categoryGroups = ref<CategoryGroup[]>([]) // 按顶级分类分组的数据
  const selectedCategories = ref<Record<number, number[]>>({}) // 存储每个顶级分类的选中值

  // 级联选择器配置
  const cascaderProps: CascaderProps = {
    value: 'id',
    label: 'name',
    children: 'children',
    checkStrictly: false, // 只能选择叶子节点
    emitPath: true // 返回完整路径
  }

  // 主设备选项
  const parentAssetOptions = ref<Asset[]>([])
  const parentAssetLoading = ref(false)

  // 主体类型选项
  const entityTypeOptions = ref<DictionaryItem[]>([])

  // 相关主体的实体选项（使用索引映射）
  const entityOptionsMap = ref<Record<number, Entity[]>>({})

  // 每个主体的联系人列表（使用索引映射）
  const entityContactsMap = ref<Record<number, Contact[]>>({})

  // 每个主体的特征词（使用索引映射）
  const entityKeywordsMap = ref<Record<number, string>>({})

  // 计算还可以选择的主体类型
  const canAddRelatedEntity = computed(() => {
    // 移除限制，始终允许添加新的相关主体
    return true
  })

  // 获取特定索引位置可选的主体类型
  const getAvailableEntityTypesForIndex = () => {
    // 移除过滤逻辑，返回所有主体类型选项
    return entityTypeOptions.value
  }

  // 表单验证规则
  const rules = reactive<FormRules>({
    name: [
      { required: true, message: '请输入资产名称', trigger: 'blur' },
      { max: 200, message: '资产名称不能超过200个字符', trigger: 'blur' }
    ],
    asset_source: [{ required: true, message: '请选择资产来源', trigger: 'change' }],
    asset_status: [{ required: true, message: '请选择资产状态', trigger: 'change' }],
    asset_condition: [{ required: true, message: '请选择成色', trigger: 'change' }],
    parent_id: [
      {
        validator: (_rule: any, value: any, callback: any) => {
          if (isAccessory.value && !value) {
            callback(new Error('附属设备必须选择主设备'))
          } else {
            callback()
          }
        },
        trigger: 'change'
      }
    ]
  })

  // 监听附属设备变化
  watch(isAccessory, (val) => {
    if (!val) {
      formData.parent_id = null
    }
  })

  // 监听对话框显示状态
  watch(visible, async (val) => {
    if (val) {
      await loadInitData()
      if (props.assetId) {
        await loadAssetDetail()
      }
    }
  })

  // 加载初始数据
  const loadInitData = async () => {
    // 加载字典数据
    const [sourceData, statusData, conditionData, entityTypeData] = await Promise.all([
      dictionaryStore.fetchItemsByCode('asset_source'),
      dictionaryStore.fetchItemsByCode('asset_status'),
      dictionaryStore.fetchItemsByCode('asset_condition'),
      dictionaryStore.fetchItemsByCode('entity_type')
    ])

    assetSourceOptions.value = sourceData || []
    assetStatusOptions.value = statusData || []
    assetConditionOptions.value = conditionData || []
    entityTypeOptions.value = entityTypeData || []

    // 加载分类数据（注意：getCategoryList已经返回树形结构）
    const categories = await getCategoryList()

    // 只保留顶级分类作为分组
    categoryGroups.value = categories
      .filter((cat) => cat.parent_id === 0)
      .map((topCat) => ({
        id: topCat.id,
        name: topCat.name,
        code: topCat.code,
        children: topCat.children || []
      }))

    // 初始化选中数据
    categoryGroups.value.forEach((group) => {
      selectedCategories.value[group.id] = []
    })
  }

  // 加载资产详情
  const loadAssetDetail = async () => {
    if (!props.assetId) return

    loading.value = true
    const asset = await getAssetDetail(props.assetId)

    // 填充表单数据
    Object.assign(formData, {
      name: asset.name,
      brand: asset.brand || '',
      model: asset.model || '',
      serial_number: asset.serial_number || '',
      asset_category_ids: [], // 稍后处理
      asset_source: asset.asset_source || null,
      asset_status: asset.asset_status || null,
      asset_condition: asset.asset_condition || null,
      parent_id: asset.parent_id,
      region_code: asset.region_code || '',
      detailed_address: asset.detailed_address || '',
      start_date: asset.start_date || '',
      warranty_period: asset.warranty_period,
      warranty_alert: asset.warranty_alert,
      maintenance_cycle: asset.maintenance_cycle,
      expected_years: asset.expected_years,
      related_entities: asset.related_entities || [],
      attachments: asset.attachments?.map((att) => att.id) || [],
      remark: asset.remark || ''
    })

    // 处理分类数据回显
    if (asset.asset_category_ids && Array.isArray(asset.asset_category_ids)) {
      // 将分类ID设置到对应的顶级分类组中
      asset.asset_category_ids.forEach((catId) => {
        categoryGroups.value.forEach((group) => {
          const path = findCategoryPath(catId, group.children)
          if (path) {
            selectedCategories.value[group.id] = path
          }
        })
      })

      // 更新 formData 中的 asset_category_ids
      formData.asset_category_ids = asset.asset_category_ids
    }

    // 设置当前附件用于显示
    currentAttachments.value = asset.attachments || []

    // 设置是否附属设备（根据parent_id判断）
    isAccessory.value = !!asset.parent_id

    // 如果有相关主体，加载每个主体的联系人信息
    if (asset.related_entities && asset.related_entities.length > 0) {
      for (let i = 0; i < asset.related_entities.length; i++) {
        const relatedEntity = asset.related_entities[i]
        if (relatedEntity.entity_id) {
          // 获取主体详情
          const entityDetail = await getEntityDetail(String(relatedEntity.entity_id))

          // 存储特征词
          if (entityDetail.keywords) {
            entityKeywordsMap.value[i] = entityDetail.keywords
          }

          // 存储联系人信息
          if (entityDetail.contacts && entityDetail.contacts.length > 0) {
            entityContactsMap.value[i] = entityDetail.contacts
          }

          // 同时获取该类型的主体列表
          if (relatedEntity.entity_type) {
            const res = await getEntityList({
              entity_type: relatedEntity.entity_type,
              per_page: 100
            })
            entityOptionsMap.value[i] = res.data || []
          }
        }
      }
    }
    loading.value = false
  }

  // 搜索主设备
  const searchParentAsset = async (query: string) => {
    if (!query) {
      parentAssetOptions.value = []
      return
    }

    parentAssetLoading.value = true
    const params: MainAssetQueryParams = {
      keyword: query,
      exclude_id: props.assetId,
      per_page: 20
    }
    const res = await getMainAssetList(params)
    parentAssetOptions.value = res.data || []
    parentAssetLoading.value = false
  }

  // 提交表单
  const handleSubmit = async () => {
    await formRef.value?.validate()

    submitLoading.value = true
    // 将选中的分类ID合并到 asset_category_ids
    formData.asset_category_ids = computedCategoryIds.value

    if (isEdit.value) {
      await updateAsset(props.assetId!, formData)
      ElMessage.success('编辑成功')
    } else {
      await createAsset(formData)
      ElMessage.success('新增成功')
    }

    visible.value = false
    emit('success')
    submitLoading.value = false
  }

  // 取消
  const handleCancel = () => {
    visible.value = false
  }

  // 对话框关闭后重置表单
  const handleClosed = () => {
    formRef.value?.resetFields()
    Object.assign(formData, {
      name: '',
      brand: '',
      model: '',
      serial_number: '',
      asset_category_ids: [],
      asset_source: null,
      asset_status: null,
      asset_condition: null,
      parent_id: null,
      region_code: '',
      detailed_address: '',
      start_date: '',
      warranty_period: null,
      warranty_alert: null,
      maintenance_cycle: null,
      expected_years: null,
      related_entities: [],
      attachments: [],
      remark: ''
    })
    currentAttachments.value = []
    parentAssetOptions.value = []
    entityOptionsMap.value = {}
    entityContactsMap.value = {}
    entityKeywordsMap.value = {}
    isAccessory.value = false

    // 重置选中的分类
    Object.keys(selectedCategories.value).forEach((key) => {
      selectedCategories.value[Number(key)] = []
    })
  }

  // 添加相关主体
  const handleAddRelatedEntity = () => {
    if (!formData.related_entities) {
      formData.related_entities = []
    }
    formData.related_entities.push({
      entity_type: null,
      entity_id: null,
      entity_name: '',
      contact_name: '',
      contact_phone: '',
      position: '',
      department: ''
    })
  }

  // 删除相关主体
  const handleRemoveRelatedEntity = (index: number) => {
    if (formData.related_entities) {
      formData.related_entities.splice(index, 1)
    }
    // 清理对应的选项数据
    delete entityOptionsMap.value[index]
    delete entityContactsMap.value[index]
    delete entityKeywordsMap.value[index]
  }

  // 地区选择处理
  const handleRegionChange = (value: any, regionInfo?: any) => {
    console.log('地区选择变化：', value, regionInfo)
    // 可以在这里添加额外的处理逻辑，比如根据地区加载相关数据
  }

  // 处理分类级联选择器变化
  const handleCategoryChange = (value: number[], groupId: number) => {
    // value 是完整路径数组，取最后一个作为选中的分类ID
    if (value && value.length > 0) {
      selectedCategories.value[groupId] = [value[value.length - 1]]
    } else {
      selectedCategories.value[groupId] = []
    }
  }

  // 计算所有选中的分类ID
  const computedCategoryIds = computed(() => {
    const ids: number[] = []
    Object.values(selectedCategories.value).forEach((groupIds) => {
      ids.push(...groupIds)
    })
    return ids
  })

  // 查找分类的完整路径
  const findCategoryPath = (
    categoryId: number,
    categories: Category[],
    path: number[] = []
  ): number[] | null => {
    for (const cat of categories) {
      if (cat.id === categoryId) {
        return [...path, cat.id]
      }
      if (cat.children && cat.children.length > 0) {
        const result = findCategoryPath(categoryId, cat.children, [...path, cat.id])
        if (result) {
          return result
        }
      }
    }
    return null
  }

  // 主体类型变化
  const handleEntityTypeChange = async (index: number) => {
    if (!formData.related_entities) return
    const entity = formData.related_entities[index]

    // 清空后续选择
    entity.entity_id = null
    entity.entity_name = ''
    entity.contact_name = ''
    entity.contact_phone = ''
    entity.position = ''
    entity.department = ''

    // 清理联系人列表和特征词
    delete entityContactsMap.value[index]
    delete entityKeywordsMap.value[index]

    if (entity.entity_type) {
      // 获取该类型的主体列表
      const res = await getEntityList({ entity_type: entity.entity_type, per_page: 100 })
      entityOptionsMap.value[index] = res.data || []
    } else {
      delete entityOptionsMap.value[index]
    }
  }

  // 主体选择变化
  const handleEntityChange = async (index: number, entityId: number | null) => {
    if (!formData.related_entities || !entityId) return
    const entity = formData.related_entities[index]

    // 如果清空了选择
    if (!entityId) {
      entity.entity_name = ''
      entity.contact_name = ''
      entity.contact_phone = ''
      entity.position = ''
      entity.department = ''
      delete entityContactsMap.value[index]
      delete entityKeywordsMap.value[index]
      return
    }

    // 获取主体详情，包含联系人信息
    const entityDetail = await getEntityDetail(String(entityId))

    // 存储主体名称（快照）
    entity.entity_name = entityDetail.name

    // 存储特征词
    if (entityDetail.keywords) {
      entityKeywordsMap.value[index] = entityDetail.keywords
    } else {
      delete entityKeywordsMap.value[index]
    }

    // 存储联系人列表
    if (entityDetail.contacts && entityDetail.contacts.length > 0) {
      entityContactsMap.value[index] = entityDetail.contacts
    } else {
      // 没有联系人信息
      delete entityContactsMap.value[index]
    }

    // 清空联系人信息
    entity.contact_name = ''
    entity.contact_phone = ''
    entity.position = ''
    entity.department = ''
  }

  // 联系人选择对话框
  const contactDialogVisible = ref(false)
  const currentEntityIndex = ref<number>(-1)
  const currentEntityContacts = computed(() => {
    if (currentEntityIndex.value >= 0) {
      return entityContactsMap.value[currentEntityIndex.value] || []
    }
    return []
  })

  // 显示联系人选择对话框
  const showContactDialog = (index: number) => {
    currentEntityIndex.value = index
    contactDialogVisible.value = true
  }

  // 处理联系人选择
  const handleContactSelect = (contact: Contact | null) => {
    if (currentEntityIndex.value < 0 || !formData.related_entities) return
    const entity = formData.related_entities[currentEntityIndex.value]

    if (contact) {
      // 填充联系人信息
      entity.contact_name = contact.name
      entity.contact_phone = contact.phone
      entity.position = contact.position || ''
      entity.department = contact.department || ''
    } else {
      // 手动填写，清空信息让用户自己填
      entity.contact_name = ''
      entity.contact_phone = ''
      entity.position = ''
      entity.department = ''
    }
  }
</script>

<style lang="scss" scoped>
  .asset-form-drawer {
    :deep(.el-drawer__body) {
      display: flex;
      flex-direction: column;
      padding: 0;
    }

    :deep(.el-drawer__footer) {
      padding: 16px 20px;
      border-top: 1px solid var(--el-border-color-light);
      box-shadow: 0 -2px 12px 0 rgb(0 0 0 / 10%);
    }

    :deep(.el-drawer__header) {
      padding: 16px 20px;
      margin-bottom: 0;
      border-bottom: 1px solid var(--el-border-color-light);
    }
  }

  .drawer-body {
    flex: 1;
    overflow: hidden;
    background-color: var(--el-bg-color-page);
  }

  .drawer-scrollbar {
    height: 100%;
  }

  .drawer-form {
    padding: 20px;

    @media (width <= 768px) {
      padding: 16px;
    }
  }

  .form-section {
    margin-bottom: 24px;

    .section-title {
      display: flex;
      align-items: center;
      padding-bottom: 8px;
      margin: 0 0 16px;
      font-size: 16px;
      font-weight: 600;
      border-bottom: 1px solid var(--el-border-color-light);
    }
  }

  .form-tip {
    margin-top: 4px;
    font-size: 12px;
    line-height: 1.2;
    color: var(--el-text-color-secondary);
  }

  .related-entity-item {
    padding: 16px;
    margin-bottom: 16px;
    background-color: var(--el-fill-color-lighter);
    border: 1px solid var(--el-border-color-lighter);
    border-radius: 6px;

    .entity-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 12px;

      .entity-index {
        font-weight: 600;
        color: var(--el-text-color-primary);
      }
    }
  }

  .contact-tips {
    margin-left: 12px;
    font-size: 12px;
    color: var(--el-text-color-secondary);
  }
</style>
