<template>
  <div class="attachment-page art-full-height art-page-view">
    <!-- 搜索栏 -->
    <ArtSearchBar
      v-model:filter="formInline"
      :items="formItems"
      :showExpand="true"
      @search="handleSearch"
      @reset="handleReset"
    />

    <ElCard shadow="never" class="art-table-card">
      <!-- 表格头部 -->
      <ArtTableHeader v-model:columns="columnChecks" @refresh="handleRefresh" />

      <!-- 表格 -->
      <ArtTable
        :data="tableData"
        :loading="loading"
        :pagination="{
          current: pagination.currentPage,
          size: pagination.pageSize,
          total: pagination.total
        }"
        :tableConfig="{
          height: undefined,
          maxHeight: '600px'
        }"
        :layout="{
          marginTop: 10
        }"
        @pagination:size-change="handleSizeChange"
        @pagination:current-change="handleCurrentChange"
      >
        <template #default>
          <ElTableColumn v-for="col in columns" :key="col.prop || col.type" v-bind="col" />
        </template>
      </ArtTable>
    </ElCard>

    <!-- 详情抽屉 -->
    <AttachmentDetailDrawer
      v-model="detailDrawerVisible"
      :attachment-id="currentAttachmentId"
      @refresh="fetchData"
    />
  </div>
</template>

<script setup lang="ts">
  defineOptions({ name: 'AttachmentManagement' })

  // Vue 核心
  import { reactive, ref, onMounted, h, computed } from 'vue'

  // UI 框架
  import { ElMessage, ElTableColumn, ElCard } from 'element-plus'

  // 内部 hooks
  import { useCheckedColumns } from '@/composables/useCheckedColumns'

  // 内部组件
  import ArtButtonTable from '@/components/custom/art-button-table/index.vue'
  import AttachmentDetailDrawer from './components/AttachmentDetailDrawer.vue'

  // API
  import { getAttachmentList } from '@/api/attachmentApi'
  import { useDictionaryStore } from '@/store/modules/dictionary'
  import { formatDate } from '@/utils/dataprocess/format'

  // 类型定义
  import type { AttachmentItem, AttachmentSearchParams } from '@/types/api'
  import type { SearchFormItem, ColumnOption } from '@/types/component'
  import type { DictionaryItem } from '@/types/api'
  const dictionaryStore = useDictionaryStore()

  const formInline = reactive<AttachmentSearchParams>({
    file_name: '',
    start_time: '',
    end_time: ''
  })

  const pagination = reactive({
    currentPage: 1,
    pageSize: 10,
    total: 0
  })

  const tableData = ref<AttachmentItem[]>([])
  const loading = ref(false)
  const detailDrawerVisible = ref(false)
  const currentAttachmentId = ref<string>()

  // 计算属性：表单项配置
  const formItems = computed((): SearchFormItem[] => [
    {
      prop: 'file_name',
      label: '文件名',
      type: 'input',
      placeholder: '请输入文件名'
    },
    {
      prop: 'start_time',
      label: '开始时间',
      type: 'date',
      placeholder: '请选择开始时间'
    },
    {
      prop: 'end_time',
      label: '结束时间',
      type: 'date',
      placeholder: '请选择结束时间'
    }
  ])

  // 表格列定义
  const columnsFactory = (): ColumnOption<AttachmentItem>[] => [
    { prop: 'file_name', label: '文件名', width: 300, showOverflowTooltip: true, fixed: 'left' },
    {
      prop: 'file_size',
      label: '文件大小',
      width: 120,
      formatter: (row: AttachmentItem) => {
        if (!row.file_size) return '-'
        const size = Number(row.file_size)
        if (size < 1024) return size + ' B'
        if (size < 1024 * 1024) return (size / 1024).toFixed(2) + ' KB'
        if (size < 1024 * 1024 * 1024) return (size / 1024 / 1024).toFixed(2) + ' MB'
        return (size / 1024 / 1024 / 1024).toFixed(2) + ' GB'
      }
    },
    { prop: 'mime_type', label: 'MIME类型', width: 200, showOverflowTooltip: true },
    {
      prop: 'storage_type',
      label: '存储类型',
      width: 120,
      formatter: (row: AttachmentItem) => {
        const storageMap: Record<string, string> = {
          local: '本地',
          alioss: '阿里云',
          qiniu: '七牛云',
          aws: 'AWS'
        }
        return storageMap[row.storage_type] || row.storage_type || 'local'
      }
    },
    {
      prop: 'created_at',
      label: '创建时间',
      width: 180,
      formatter: (row: AttachmentItem) =>
        row.created_at ? formatDate(row.created_at, 'YYYY-MM-DD HH:mm:ss') : ''
    },
    {
      prop: 'operation',
      label: '操作',
      width: 120,
      fixed: 'right',
      formatter: (row: AttachmentItem) => {
        return h('div', { style: 'display: flex; gap: 5px;' }, [
          h(ArtButtonTable, {
            type: 'primary',
            icon: '&#xe89f;',
            onClick: () => handleEdit(row)
          }),
          h(ArtButtonTable, {
            type: 'primary',
            icon: '&#xe626;',
            onClick: () => handleDownload(row)
          })
        ])
      }
    }
  ]

  // 使用列筛选钩子
  const { columns, columnChecks } = useCheckedColumns(columnsFactory)

  const fetchData = async () => {
    loading.value = true
    try {
      // 过滤掉 null 值
      const params: any = {
        page: pagination.currentPage,
        size: pagination.pageSize
      }

      // 只传递非空值
      if (formInline.file_name) params.file_name = formInline.file_name
      if (formInline.start_time) params.start_time = formInline.start_time
      if (formInline.end_time) params.end_time = formInline.end_time

      const res = await getAttachmentList(params)
      tableData.value = res.data
      pagination.total = res.meta?.total || 0
    } finally {
      loading.value = false
    }
  }

  const handleSearch = () => {
    pagination.currentPage = 1
    fetchData()
  }

  const handleReset = () => {
    formInline.file_name = ''
    formInline.start_time = ''
    formInline.end_time = ''
    pagination.currentPage = 1
    fetchData()
  }

  // 编辑处理
  const handleEdit = (row: AttachmentItem) => {
    currentAttachmentId.value = row.id
    detailDrawerVisible.value = true
  }

  const handleSizeChange = (size: number) => {
    pagination.pageSize = size
    fetchData()
  }

  const handleCurrentChange = (page: number) => {
    pagination.currentPage = page
    fetchData()
  }

  // 刷新处理
  const handleRefresh = () => {
    fetchData()
  }

  // 下载文件
  const handleDownload = (row: AttachmentItem) => {
    const link = document.createElement('a')
    link.href = row.file_path || ''
    link.download = row.file_name
    link.target = '_blank'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    ElMessage.success('开始下载文件')
  }

  onMounted(async () => {
    // 加载附件数据
    await fetchData()
  })
</script>

<style lang="scss" scoped></style>
