<?php

namespace App\Models;

use App\Traits\HasAttachments;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\Models\BaseModel;

/**
 * @property int $id
 * @property string $name 主体名称
 * @property string|null $tax_number 税号
 * @property string $entity_type 主体类型
 * @property string|null $address 地址
 * @property string|null $phone 联系电话
 * @property string|null $keywords 特征词
 * @property string|null $remark 备注
 * @property int|null $created_by 创建人
 * @property int|null $updated_by 更新人
 * @property int|null $created_at 创建时间
 * @property int|null $updated_at 更新时间
 * @property \Illuminate\Support\Carbon|null $deleted_at 删除时间
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Attachment> $attachments
 * @property-read int|null $attachments_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\EntityContact> $contacts
 * @property-read int|null $contacts_count
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Entity newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Entity newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Entity onlyTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Entity query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Entity whereAddress($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Entity whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Entity whereCreatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Entity whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Entity whereEntityType($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Entity whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Entity whereKeywords($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Entity whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Entity wherePhone($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Entity whereRemark($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Entity whereTaxNumber($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Entity whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Entity whereUpdatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Entity withTrashed(bool $withTrashed = true)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Entity withoutTrashed()
 * @mixin \Eloquent
 */
class Entity extends BaseModel
{
    use HasAttachments, HasFactory, SoftDeletes;

    protected $fillable = [
        'name',
        'tax_number',
        'entity_type',
        'address',
        'phone',
        'keywords',
        'remark',
        'created_by',
        'updated_by',
    ];

    protected $hidden = ['deleted_at'];

    protected $casts = [
        'created_at' => 'integer',
        'updated_at' => 'integer',
    ];

    public function contacts(): HasMany
    {
        return $this->hasMany(EntityContact::class);
    }
}
