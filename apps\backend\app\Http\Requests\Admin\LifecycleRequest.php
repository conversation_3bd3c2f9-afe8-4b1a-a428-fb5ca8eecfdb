<?php

namespace App\Http\Requests\Admin;

use Illuminate\Foundation\Http\FormRequest;

class LifecycleRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $isUpdate = $this->isMethod('put') || $this->isMethod('patch');

        $rules = [
            'asset_id' => 'nullable|exists:assets,id',
            'type' => ($isUpdate ? 'sometimes|' : '').'required|string|max:50',
            'date' => ($isUpdate ? 'sometimes|' : '').'required|integer',
            'initiator_id' => ($isUpdate ? 'sometimes|' : '').'required|exists:users,id',
            'content' => ($isUpdate ? 'sometimes|' : '').'required|string',
            'assistants' => ($isUpdate ? 'sometimes|' : '').'required|array',
            'assistants.*' => 'exists:users,id',
            'acceptance_entity_id' => ($isUpdate ? 'sometimes|' : '').'required|exists:entities,id',
            'acceptance_personnel_id' => ($isUpdate ? 'sometimes|' : '').'required|exists:entity_contacts,id',
            'acceptance_time' => ($isUpdate ? 'sometimes|' : '').'required|integer',
            'attachments' => 'nullable|array',
            'attachments.*' => 'exists:attachments,id',
        ];

        return $rules;
    }

    /**
     * Get the error messages for the defined validation rules.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'type.required' => '类型不能为空',
            'date.required' => '日期不能为空',
            'date.integer' => '日期格式错误',
            'initiator_id.required' => '发起人不能为空',
            'initiator_id.exists' => '发起人不存在',
            'content.required' => '内容不能为空',
            'assistants.required' => '协助人员不能为空',
            'assistants.*.exists' => '协助人员不存在',
            'acceptance_entity_id.required' => '验收主体不能为空',
            'acceptance_entity_id.exists' => '验收主体不存在',
            'acceptance_personnel_id.required' => '验收人员不能为空',
            'acceptance_personnel_id.exists' => '验收人员不存在',
            'acceptance_time.required' => '验收时间不能为空',
            'acceptance_time.integer' => '验收时间格式错误',
            'attachments.*.exists' => '附件不存在',
        ];
    }
}
