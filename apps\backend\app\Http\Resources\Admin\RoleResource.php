<?php

namespace App\Http\Resources\Admin;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use function App\Support\string_to_timestamp;

class RoleResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     *
     * @response {
     *   "id": 1,
     *   "name": "管理员",
     *   "guard_name": "admin",
     *   "description": "系统管理员角色",
     *   "users_count": 1,
     *   "permissions": [
     *     {
     *       "id": 1,
     *       "name": "新增",
     *       "path": "/user",
     *       "method": "POST"
     *     }
     *   ],
     *   "menus": [
     *     {
     *       "menu_id": 1,
     *       "permission_ids": [1, 2, 3]
     *     }
     *   ],
     *   "created_at": "2021-01-01 00:00:00",
     *   "updated_at": "2021-01-01 00:00:00"
     * }
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'guard_name' => $this->guard_name,
            'description' => $this->description,
            'users_count' => $this->when(isset($this->users_count), $this->users_count),
            'permissions' => $this->when($this->relationLoaded('permissions'), function () {
                return $this->permissions->map(function ($permission) {
                    return [
                        'id' => $permission->id,
                        'name' => $permission->name,
                        'path' => $permission->path,
                        'method' => $permission->method,
                    ];
                });
            }),
            'menus' => $this->when($this->relationLoaded('roleMenuPermissions'), function () {
                $menus = [];
                foreach ($this->roleMenuPermissions as $item) {
                    if (!isset($menus[$item->menu_id])) {
                        $menus[$item->menu_id] = [
                            'menu_id' => $item->menu_id,
                            'permission_ids' => []
                        ];
                    }

                    // 只有当 menu_permission_id 不为 null 时才添加到权限数组
                    if (!is_null($item->menu_permission_id)) {
                        $menus[$item->menu_id]['permission_ids'][] = $item->menu_permission_id;
                    }
                }

                return array_values($menus);
            }),
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ];
    }
}
