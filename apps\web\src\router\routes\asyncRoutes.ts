import { RoutesAlias } from '../routesAlias'
import { AppRouteRecord } from '@/types/router'

/**
 * 菜单列表、异步路由
 *
 * 支持两种模式:
 * 前端静态配置 - 直接使用本文件中定义的路由配置
 * 后端动态配置 - 后端返回菜单数据，前端解析生成路由
 *
 * 菜单标题（title）:
 * 可以是 i18n 的 key，也可以是字符串，比如：'用户列表'
 *
 * RoutesAlias.Layout 指向的是布局组件，后端返回的菜单数据中，component 字段需要指向 /index/index
 * 路由元数据（meta）：异步路由在 asyncRoutes 中配置，静态路由在 staticRoutes 中配置
 */
export const asyncRoutes: AppRouteRecord[] = [
  {
    name: 'Dashboard',
    path: '/dashboard',
    component: RoutesAlias.Layout,
    meta: {
      title: 'menus.dashboard.title',
      icon: '&#xe721;'
    },
    children: [
      {
        path: 'console',
        name: 'Console',
        component: RoutesAlias.Dashboard,
        meta: {
          title: 'menus.dashboard.console',
          keepAlive: false,
          fixedTab: true
        }
      }
      // 注释掉分析页和电商页，只保留工作页
      // {
      //   path: 'analysis',
      //   name: 'Analysis',
      //   component: RoutesAlias.Analysis,
      //   meta: {
      //     title: 'menus.dashboard.analysis',
      //     keepAlive: false
      //   }
      // },
      // {
      //   path: 'ecommerce',
      //   name: 'Ecommerce',
      //   component: RoutesAlias.Ecommerce,
      //   meta: {
      //     title: 'menus.dashboard.ecommerce',
      //     keepAlive: false
      //   }
      // }
    ]
  },
  // 注释掉模板中心菜单
  // {
  //   path: '/template',
  //   name: 'Template',
  //   component: RoutesAlias.Layout,
  //   meta: {
  //     title: 'menus.template.title',
  //     icon: '&#xe860;'
  //   },
  //   children: [
  //     {
  //       path: 'cards',
  //       name: 'Cards',
  //       component: RoutesAlias.Cards,
  //       meta: {
  //         title: 'menus.template.cards',
  //         keepAlive: false
  //       }
  //     },
  //     {
  //       path: 'banners',
  //       name: 'Banners',
  //       component: RoutesAlias.Banners,
  //       meta: {
  //         title: 'menus.template.banners',
  //         keepAlive: false
  //       }
  //     },
  //     {
  //       path: 'charts',
  //       name: 'Charts',
  //       component: RoutesAlias.Charts,
  //       meta: {
  //         title: 'menus.template.charts',
  //         keepAlive: false
  //       }
  //     },
  //     {
  //       path: 'map',
  //       name: 'Map',
  //       component: RoutesAlias.Map,
  //       meta: {
  //         title: 'menus.template.map',
  //         keepAlive: true
  //       }
  //     },
  //     {
  //       path: 'chat',
  //       name: 'Chat',
  //       component: RoutesAlias.Chat,
  //       meta: {
  //         title: 'menus.template.chat',
  //         keepAlive: true
  //       }
  //     },
  //     {
  //       path: 'calendar',
  //       name: 'Calendar',
  //       component: RoutesAlias.Calendar,
  //       meta: {
  //         title: 'menus.template.calendar',
  //         keepAlive: true
  //       }
  //     },
  //     {
  //       path: 'pricing',
  //       name: 'Pricing',
  //       component: RoutesAlias.Pricing,
  //       meta: {
  //         title: 'menus.template.pricing',
  //         keepAlive: true,
  //         isFullPage: true // 是否全屏显示
  //       }
  //     }
  //   ]
  // },
  // 注释掉组件中心菜单
  // {
  //   path: '/widgets',
  //   name: 'Widgets',
  //   component: RoutesAlias.Layout,
  //   meta: {
  //     title: 'menus.widgets.title',
  //     icon: '&#xe81a;'
  //   },
  //   children: [
  //     {
  //       path: 'icon-list',
  //       name: 'IconList',
  //       component: RoutesAlias.IconList,
  //       meta: {
  //         title: 'menus.widgets.iconList',
  //         keepAlive: true
  //       }
  //     },
  //     {
  //       path: 'icon-selector',
  //       name: 'IconSelector',
  //       component: RoutesAlias.IconSelector,
  //       meta: {
  //         title: 'menus.widgets.iconSelector',
  //         keepAlive: true
  //       }
  //     },
  //     {
  //       path: 'image-crop',
  //       name: 'ImageCrop',
  //       component: RoutesAlias.ImageCrop,
  //       meta: {
  //         title: 'menus.widgets.imageCrop',
  //         keepAlive: true
  //       }
  //     },
  //     {
  //       path: 'excel',
  //       name: 'Excel',
  //       component: RoutesAlias.Excel,
  //       meta: {
  //         title: 'menus.widgets.excel',
  //         keepAlive: true
  //       }
  //     },
  //     {
  //       path: 'video',
  //       name: 'Video',
  //       component: RoutesAlias.Video,
  //       meta: {
  //         title: 'menus.widgets.video',
  //         keepAlive: true
  //       }
  //     },
  //     {
  //       path: 'count-to',
  //       name: 'CountTo',
  //       component: RoutesAlias.CountTo,
  //       meta: {
  //         title: 'menus.widgets.countTo',
  //         keepAlive: false,
  //         showTextBadge: 'New'
  //       }
  //     },
  //     {
  //       path: 'wang-editor',
  //       name: 'WangEditor',
  //       component: RoutesAlias.WangEditor,
  //       meta: {
  //         title: 'menus.widgets.wangEditor',
  //         keepAlive: true
  //       }
  //     },
  //     {
  //       path: 'watermark',
  //       name: 'Watermark',
  //       component: RoutesAlias.Watermark,
  //       meta: {
  //         title: 'menus.widgets.watermark',
  //         keepAlive: true
  //       }
  //     },
  //     {
  //       path: 'context-menu',
  //       name: 'ContextMenu',
  //       component: RoutesAlias.ContextMenu,
  //       meta: {
  //         title: 'menus.widgets.contextMenu',
  //         keepAlive: true
  //       }
  //     },
  //     {
  //       path: 'qrcode',
  //       name: 'Qrcode',
  //       component: RoutesAlias.Qrcode,
  //       meta: {
  //         title: 'menus.widgets.qrcode',
  //         keepAlive: true
  //       }
  //     },
  //     {
  //       path: 'drag',
  //       name: 'Drag',
  //       component: RoutesAlias.Drag,
  //       meta: {
  //         title: 'menus.widgets.drag',
  //         keepAlive: true
  //       }
  //     },
  //     {
  //       path: 'text-scroll',
  //       name: 'TextScroll',
  //       component: RoutesAlias.TextScroll,
  //       meta: {
  //         title: 'menus.widgets.textScroll',
  //         keepAlive: true
  //       }
  //     },
  //     {
  //       path: 'fireworks',
  //       name: 'Fireworks',
  //       component: RoutesAlias.Fireworks,
  //       meta: {
  //         title: 'menus.widgets.fireworks',
  //         keepAlive: true,
  //         showTextBadge: 'Hot'
  //       }
  //     },
  //     {
  //       path: '/outside/iframe/elementui',
  //       name: 'ElementUI',
  //       component: '',
  //       meta: {
  //         title: 'menus.widgets.elementUI',
  //         keepAlive: false,
  //         link: 'https://element-plus.org/zh-CN/component/overview.html',
  //         isIframe: true,
  //         showBadge: true
  //       }
  //     }
  //   ]
  // },
  // 注释掉功能示例菜单
  // {
  //   path: '/examples',
  //   name: 'Examples',
  //   component: RoutesAlias.Layout,
  //   meta: {
  //     title: 'menus.examples.title',
  //     icon: '&#xe8d4;',
  //     showBadge: true
  //   },
  //   children: [
  //     {
  //       path: 'tabs',
  //       name: 'Tabs',
  //       component: RoutesAlias.ExamplesTabs,
  //       meta: {
  //         title: 'menus.examples.tabs'
  //       }
  //     },
  //     {
  //       path: 'tables/basic',
  //       name: 'TablesBasic',
  //       component: RoutesAlias.ExamplesTablesBasic,
  //       meta: {
  //         title: 'menus.examples.tablesBasic',
  //         keepAlive: true,
  //         showTextBadge: 'New'
  //       }
  //     },
  //     {
  //       path: 'tables',
  //       name: 'Tables',
  //       component: RoutesAlias.ExamplesTables,
  //       meta: {
  //         title: 'menus.examples.tables',
  //         keepAlive: true,
  //         showTextBadge: 'New'
  //       }
  //     }
  //   ]
  // },
  {
    path: '/system',
    name: 'System',
    component: RoutesAlias.Layout,
    meta: {
      title: 'menus.system.title',
      icon: '&#xe7b9;'
    },
    children: [
      {
        path: 'user',
        name: 'User',
        component: RoutesAlias.User,
        meta: {
          title: 'menus.system.user',
          keepAlive: true
        }
      },
      {
        path: 'role',
        name: 'Role',
        component: RoutesAlias.Role,
        meta: {
          title: 'menus.system.role',
          keepAlive: true
        }
      },
      {
        path: 'user-center',
        name: 'UserCenter',
        component: RoutesAlias.UserCenter,
        meta: {
          title: 'menus.system.userCenter',
          isHide: true,
          keepAlive: true,
          isHideTab: true
        }
      },
      {
        path: 'menu',
        name: 'Menus',
        component: RoutesAlias.Menu,
        meta: {
          title: 'menus.system.menu',
          keepAlive: true,
          authList: [
            {
              title: '新增',
              authMark: 'add'
            },
            {
              title: '编辑',
              authMark: 'edit'
            },
            {
              title: '删除',
              authMark: 'delete'
            }
          ]
        }
      }
      // 注释掉嵌套菜单
      // {
      //   path: 'nested',
      //   name: 'Nested',
      //   component: '',
      //   meta: {
      //     title: 'menus.system.nested',
      //     keepAlive: true
      //   },
      //   children: [
      //     {
      //       path: 'menu1',
      //       name: 'NestedMenu1',
      //       component: RoutesAlias.NestedMenu1,
      //       meta: {
      //         title: 'menus.system.menu1',
      //         icon: '&#xe676;',
      //         keepAlive: true
      //       }
      //     },
      //     {
      //       path: 'menu2',
      //       name: 'NestedMenu2',
      //       component: '',
      //       meta: {
      //         title: 'menus.system.menu2',
      //         icon: '&#xe676;',
      //         keepAlive: true
      //       },
      //       children: [
      //         {
      //           path: 'menu2-1',
      //           name: 'NestedMenu2-1',
      //           component: RoutesAlias.NestedMenu21,
      //           meta: {
      //             title: 'menus.system.menu21',
      //             icon: '&#xe676;',
      //             keepAlive: true
      //           }
      //         }
      //       ]
      //     },
      //     {
      //       path: 'menu3',
      //       name: 'NestedMenu3',
      //       component: '',
      //       meta: {
      //         title: 'menus.system.menu3',
      //         icon: '&#xe676;',
      //         keepAlive: true
      //       },
      //       children: [
      //         {
      //           path: 'menu3-1',
      //           name: 'NestedMenu3-1',
      //           component: RoutesAlias.NestedMenu31,
      //           meta: {
      //             title: 'menus.system.menu31',
      //             icon: '&#xe676;',
      //             keepAlive: true
      //           }
      //         },
      //         {
      //           path: 'menu3-2',
      //           name: 'NestedMenu3-2',
      //           component: '',
      //           meta: {
      //             title: 'menus.system.menu32',
      //             icon: '&#xe676;',
      //             keepAlive: true
      //           },
      //           children: [
      //             {
      //               path: 'menu3-2-1',
      //               name: 'NestedMenu3-2-1',
      //               component: RoutesAlias.NestedMenu321,
      //               meta: {
      //                 title: 'menus.system.menu321',
      //                 icon: '&#xe676;',
      //                 keepAlive: true
      //               }
      //             }
      //           ]
      //         }
      //       ]
      //     }
      //   ]
      // }
    ]
  },
  // 注释掉文章管理菜单
  // {
  //   path: '/article',
  //   name: 'Article',
  //   component: RoutesAlias.Layout,
  //   meta: {
  //     title: 'menus.article.title',
  //     icon: '&#xe7ae;'
  //   },
  //   children: [
  //     {
  //       path: 'article-list',
  //       name: 'ArticleList',
  //       component: RoutesAlias.ArticleList,
  //       meta: {
  //         title: 'menus.article.articleList',
  //         keepAlive: true,
  //         authList: [
  //           {
  //             title: '新增',
  //             authMark: 'add'
  //           },
  //           {
  //             title: '编辑',
  //             authMark: 'edit'
  //           }
  //         ]
  //       }
  //     },
  //     {
  //       path: 'detail',
  //       name: 'ArticleDetail',
  //       component: RoutesAlias.ArticleDetail,
  //       meta: {
  //         title: 'menus.article.articleDetail',
  //         isHide: true,
  //         keepAlive: true,
  //         activePath: '/article/article-list' // 激活菜单路径
  //       }
  //     },
  //     {
  //       path: 'comment',
  //       name: 'ArticleComment',
  //       component: RoutesAlias.Comment,
  //       meta: {
  //         title: 'menus.article.comment',
  //         keepAlive: true
  //       }
  //     },
  //     {
  //       path: 'publish',
  //       name: 'ArticlePublish',
  //       component: RoutesAlias.ArticlePublish,
  //       meta: {
  //         title: 'menus.article.articlePublish',
  //         keepAlive: true,
  //         authList: [
  //           {
  //             title: '发布',
  //             authMark: 'article/article-publish/add'
  //           }
  //         ]
  //       }
  //     }
  //   ]
  // },
  // 注释掉结果页面菜单
  // {
  //   path: '/result',
  //   name: 'Result',
  //   component: RoutesAlias.Layout,
  //   meta: {
  //     title: 'menus.result.title',
  //     icon: '&#xe715;'
  //   },
  //   children: [
  //     {
  //       path: 'success',
  //       name: 'ResultSuccess',
  //       component: RoutesAlias.Success,
  //       meta: {
  //         title: 'menus.result.success',
  //         keepAlive: true
  //       }
  //     },
  //     {
  //       path: 'fail',
  //       name: 'ResultFail',
  //       component: RoutesAlias.Fail,
  //       meta: {
  //         title: 'menus.result.fail',
  //         keepAlive: true
  //       }
  //     }
  //   ]
  // },
  // 注释掉异常页面菜单
  // {
  //   path: '/exception',
  //   name: 'Exception',
  //   component: RoutesAlias.Layout,
  //   meta: {
  //     title: 'menus.exception.title',
  //     icon: '&#xe820;'
  //   },
  //   children: [
  //     {
  //       path: '403',
  //       name: '403',
  //       component: RoutesAlias.Exception403,
  //       meta: {
  //         title: 'menus.exception.forbidden',
  //         keepAlive: true
  //       }
  //     },
  //     {
  //       path: '404',
  //       name: '404',
  //       component: RoutesAlias.Exception404,
  //       meta: {
  //         title: 'menus.exception.notFound',
  //         keepAlive: true
  //       }
  //     },
  //     {
  //       path: '500',
  //       name: '500',
  //       component: RoutesAlias.Exception500,
  //       meta: {
  //         title: 'menus.exception.serverError',
  //         keepAlive: true
  //       }
  //     }
  //   ]
  // },
  // 注释掉运维管理菜单
  // {
  //   path: '/safeguard',
  //   name: 'Safeguard',
  //   component: RoutesAlias.Layout,
  //   meta: {
  //     title: 'menus.safeguard.title',
  //     icon: '&#xe816;',
  //     keepAlive: false
  //   },
  //   children: [
  //     {
  //       path: 'server',
  //       name: 'SafeguardServer',
  //       component: RoutesAlias.Server,
  //       meta: {
  //         title: 'menus.safeguard.server',
  //         keepAlive: true
  //       }
  //     }
  //   ]
  // },
  {
    path: '/development',
    name: 'Development',
    component: RoutesAlias.Layout,
    meta: {
      title: 'menus.development.title',
      icon: '&#xe7c5;',
      keepAlive: false
    },
    children: [
      {
        path: 'asset',
        name: 'Asset',
        component: RoutesAlias.Asset,
        meta: {
          title: '资产管理',
          keepAlive: true
        }
      },
      {
        path: 'dictionary',
        name: 'Dictionary',
        component: RoutesAlias.Dictionary,
        meta: {
          title: 'menus.development.dictionary',
          keepAlive: true
        }
      },
      {
        path: 'category',
        name: 'Category',
        component: RoutesAlias.Category,
        meta: {
          title: 'menus.development.category',
          keepAlive: true
        }
      },
      {
        path: 'entity',
        name: 'Entity',
        component: RoutesAlias.Entity,
        meta: {
          title: 'menus.development.entity',
          keepAlive: true
        }
      },
      {
        path: 'lifecycle',
        name: 'Lifecycle',
        component: RoutesAlias.Lifecycle,
        meta: {
          title: 'menus.development.lifecycle',
          keepAlive: true
        }
      },
      {
        path: 'attachment',
        name: 'AttachmentManagement',
        component: RoutesAlias.AttachmentManagement,
        meta: {
          title: '附件管理',
          keepAlive: true
        }
      }
    ]
  }
  // 注释掉帮助中心菜单
  // {
  //   name: 'Help',
  //   path: '/help',
  //   component: RoutesAlias.Layout,
  //   meta: {
  //     title: 'menus.help.title',
  //     icon: '&#xe719;',
  //     keepAlive: false
  //   },
  //   children: [
  //     {
  //       path: '',
  //       name: 'Document',
  //       meta: {
  //         title: 'menus.help.document',
  //         link: WEB_LINKS.DOCS,
  //         isIframe: false,
  //         keepAlive: false
  //       }
  //     }
  //   ]
  // },
  // 注释掉更新日志菜单
  // {
  //   name: 'ChangeLog',
  //   path: '/change/log',
  //   component: RoutesAlias.ChangeLog,
  //   meta: {
  //     title: 'menus.plan.log',
  //     showTextBadge: `v${__APP_VERSION__}`,
  //     icon: '&#xe712;',
  //     keepAlive: false
  //   }
  // }
]
