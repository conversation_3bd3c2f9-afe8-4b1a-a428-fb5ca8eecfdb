<?php

/**
 * 验证导入的数据
 */

require_once __DIR__ . '/vendor/autoload.php';

use Illuminate\Foundation\Application;
use App\Models\Asset;
use App\Models\Entity;
use App\Models\EntityContact;
use App\Models\AssetImportTask;

// 启动Laravel应用
$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "=== 验证导入数据 ===\n\n";

try {
    // 查看最新的导入任务
    echo "1. 最新导入任务状态...\n";

    $latestTask = AssetImportTask::orderBy('created_at', 'desc')->first();
    if ($latestTask) {
        echo "任务ID: {$latestTask->id}\n";
        echo "状态: {$latestTask->status}\n";
        echo "文件名: {$latestTask->original_filename}\n";
        echo "总行数: {$latestTask->total_rows}\n";
        echo "成功行数: {$latestTask->success_rows}\n";
        echo "失败行数: {$latestTask->failed_rows}\n";
        echo "摘要: {$latestTask->summary}\n";
    } else {
        echo "没有找到导入任务\n";
    }

    // 查看最新导入的资产
    echo "\n2. 最新导入的资产...\n";

    $recentAssets = Asset::orderBy('created_at', 'desc')
        ->limit(5)
        ->get(['id', 'name', 'brand', 'model', 'serial_number', 'asset_category_ids', 'related_entities', 'created_at']);

    if ($recentAssets->count() > 0) {
        echo "找到 " . $recentAssets->count() . " 个最新资产:\n";

        foreach ($recentAssets as $asset) {
            echo "\n资产ID: {$asset->id}\n";
            echo "  名称: {$asset->name}\n";
            echo "  品牌: {$asset->brand}\n";
            echo "  型号: {$asset->model}\n";
            echo "  序列号: {$asset->serial_number}\n";
            echo "  创建时间: " . date('Y-m-d H:i:s', $asset->created_at) . "\n";

            // 解析资产分类（已经是数组类型）
            if ($asset->asset_category_ids) {
                $categories = is_array($asset->asset_category_ids) ? $asset->asset_category_ids : [];
                echo "  分类IDs: " . implode(', ', $categories) . "\n";
            }

            // 解析相关主体（已经是数组类型）
            if ($asset->related_entities) {
                $entities = is_array($asset->related_entities) ? $asset->related_entities : [];
                if ($entities) {
                    echo "  相关主体:\n";
                    foreach ($entities as $entity) {
                        $entityId = $entity['entity_id'] ?? '未知';
                        $entityName = $entity['entity_name'] ?? '未知';
                        $relationType = $entity['relation_type'] ?? '未知';
                        echo "    - ID: {$entityId}, 名称: {$entityName}, 关系: {$relationType}\n";
                    }
                }
            }
        }
    } else {
        echo "没有找到资产记录\n";
    }

    // 查看最新导入的主体
    echo "\n3. 最新导入的主体...\n";

    $recentEntities = Entity::orderBy('created_at', 'desc')
        ->limit(5)
        ->get(['id', 'name', 'tax_number', 'entity_type', 'address', 'phone', 'created_at']);

    if ($recentEntities->count() > 0) {
        echo "找到 " . $recentEntities->count() . " 个最新主体:\n";

        foreach ($recentEntities as $entity) {
            echo "\n主体ID: {$entity->id}\n";
            echo "  名称: {$entity->name}\n";
            echo "  税号: {$entity->tax_number}\n";
            echo "  类型: {$entity->entity_type}\n";
            echo "  地址: {$entity->address}\n";
            echo "  电话: {$entity->phone}\n";
            echo "  创建时间: " . date('Y-m-d H:i:s', $entity->created_at) . "\n";
        }
    } else {
        echo "没有找到主体记录\n";
    }

    // 查看最新导入的联系人
    echo "\n4. 最新导入的联系人...\n";

    $recentContacts = EntityContact::with('entity')
        ->orderBy('created_at', 'desc')
        ->limit(5)
        ->get(['id', 'entity_id', 'name', 'phone', 'position', 'department', 'created_at']);

    if ($recentContacts->count() > 0) {
        echo "找到 " . $recentContacts->count() . " 个最新联系人:\n";

        foreach ($recentContacts as $contact) {
            echo "\n联系人ID: {$contact->id}\n";
            echo "  姓名: {$contact->name}\n";
            echo "  电话: {$contact->phone}\n";
            echo "  职位: {$contact->position}\n";
            echo "  部门: {$contact->department}\n";
            echo "  所属主体: " . ($contact->entity ? $contact->entity->name : '未知') . "\n";
            echo "  创建时间: " . date('Y-m-d H:i:s', $contact->created_at) . "\n";
        }
    } else {
        echo "没有找到联系人记录\n";
    }

    // 统计信息
    echo "\n5. 数据统计...\n";

    $totalAssets = Asset::count();
    $totalEntities = Entity::count();
    $totalContacts = EntityContact::count();
    $totalTasks = AssetImportTask::count();

    echo "总资产数量: {$totalAssets}\n";
    echo "总主体数量: {$totalEntities}\n";
    echo "总联系人数量: {$totalContacts}\n";
    echo "总导入任务数量: {$totalTasks}\n";

    // 成功率统计
    $completedTasks = AssetImportTask::where('status', 'completed')->count();
    $failedTasks = AssetImportTask::where('status', 'failed')->count();
    $pendingTasks = AssetImportTask::where('status', 'pending')->count();
    $processingTasks = AssetImportTask::where('status', 'processing')->count();

    echo "\n导入任务状态统计:\n";
    echo "  已完成: {$completedTasks}\n";
    echo "  失败: {$failedTasks}\n";
    echo "  待处理: {$pendingTasks}\n";
    echo "  处理中: {$processingTasks}\n";

    if ($totalTasks > 0) {
        $successRate = round(($completedTasks / $totalTasks) * 100, 2);
        echo "  成功率: {$successRate}%\n";
    }

    echo "\n=== 验证完成 ===\n";

    if ($latestTask && $latestTask->status === 'completed' && $latestTask->success_rows > 0) {
        echo "✓ 数据导入验证成功！\n";
        echo "✓ 资产、主体、联系人数据已正确导入\n";
        echo "✓ 批量处理和SQL防注入功能正常工作\n";
    } else {
        echo "⚠ 请检查导入任务状态和数据完整性\n";
    }

} catch (Exception $e) {
    echo "✗ 验证失败：" . $e->getMessage() . "\n";
    echo "错误位置：" . $e->getFile() . ":" . $e->getLine() . "\n";
}

echo "\n=== 验证结束 ===\n";
