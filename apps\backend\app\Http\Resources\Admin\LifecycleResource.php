<?php

namespace App\Http\Resources\Admin;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use function App\Support\string_to_timestamp;

class LifecycleResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'asset_id' => $this->asset_id,
            'asset' => $this->whenLoaded('asset', fn() => [
                'id' => $this->asset->id,
                'name' => $this->asset->name,
                'brand' => $this->asset->brand,
                'model' => $this->asset->model,
                'serial_number' => $this->asset->serial_number,
            ]),
            'type' => $this->type,
            'date' => $this->date,
            'initiator' => $this->initiator_id,
            'initiator_name' => $this->whenLoaded('initiator', fn() => $this->initiator->nickname),
            'content' => $this->content,
            'assistants' => $this->whenLoaded('assistants', fn() => $this->assistants->pluck('id')->toArray()),
            'assistant_names' => $this->whenLoaded('assistants', fn() => $this->assistants->pluck('nickname')->toArray()),
            'acceptance_entity' => $this->acceptance_entity_id,
            'acceptance_entity_name' => $this->whenLoaded('acceptanceEntity', fn() => $this->acceptanceEntity->name),
            'acceptance_personnel' => $this->acceptance_personnel_id,
            'acceptance_personnel_name' => $this->whenLoaded('acceptancePersonnel', fn() => $this->acceptancePersonnel->name),
            'acceptance_time' => $this->acceptance_time,
            'attachments' => $this->whenLoaded('attachments'),
            'follow_ups' => $this->whenLoaded('followUps', fn() => LifecycleFollowUpResource::collection($this->followUps)),
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ];
    }
}
