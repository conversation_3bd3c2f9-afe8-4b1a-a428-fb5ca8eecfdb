<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('menus', function (Blueprint $table) {
            // 设置表备注
            $table->comment('菜单表');
            $table->id();
            $table->unsignedBigInteger('parent_id')->nullable()->comment('父级ID');
            $table->string('name', 50)->comment('路由名称（唯一）');
            $table->string('path', 255)->comment('路由路径');
            $table->string('component', 255)->nullable()->comment('组件路径');
            $table->string('title', 100)->comment('菜单标题');
            $table->string('icon', 50)->nullable()->comment('菜单图标');
            $table->string('label', 100)->nullable()->comment('权限标识');
            $table->integer('sort')->default(0)->comment('排序');
            $table->boolean('is_hide')->default(false)->comment('是否隐藏');
            $table->boolean('is_hide_tab')->default(false)->comment('是否在标签页隐藏');
            $table->string('link', 255)->nullable()->comment('外部链接');
            $table->boolean('is_iframe')->default(false)->comment('是否为iframe');
            $table->boolean('keep_alive')->default(true)->comment('是否缓存');
            $table->boolean('is_first_level')->default(false)->comment('是否为一级菜单');
            $table->boolean('fixed_tab')->default(false)->comment('是否固定标签页');
            $table->string('active_path', 255)->nullable()->comment('激活菜单路径');
            $table->boolean('is_full_page')->default(false)->comment('是否全屏页面');
            $table->boolean('show_badge')->default(false)->comment('是否显示徽章');
            $table->string('show_text_badge', 50)->nullable()->comment('文本徽章内容');
            $table->boolean('status')->default(true)->comment('状态：1启用 0禁用');
            $table->bigInteger('created_at')->nullable()->comment('创建时间');
            $table->bigInteger('updated_at')->nullable()->comment('更新时间');

            // 添加索引
            $table->unique(['name']);
            $table->index('parent_id');
            $table->index('sort');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('menus');
    }
};
