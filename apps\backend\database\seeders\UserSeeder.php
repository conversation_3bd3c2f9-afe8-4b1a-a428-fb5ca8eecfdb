<?php

namespace Database\Seeders;

use App\Models\Role;
use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\DB;

class UserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // 清空表数据 - 先禁用外键检查
        DB::statement('SET FOREIGN_KEY_CHECKS=0;');
        User::truncate();
        DB::statement('SET FOREIGN_KEY_CHECKS=1;');

        // 只创建一个默认管理员
        $user = User::create([
            'nickname' => 'admin',
            'password' => Hash::make('admin@123'),
            'email' => null,
            'account' => '***********',
            'status' => 'enable',
            'is_super_admin' => 1,
        ]);

        // 分配管理员角色
        $adminRole = Role::where('name', 'admin')->first();
        if ($adminRole) {
            $user->roles()->attach($adminRole->id);
        }
    }
}
