<template>
  <ElDialog
    v-model="dialogVisible"
    :title="`为用户「${userName}」分配角色`"
    width="900px"
    :close-on-click-modal="false"
    @closed="handleClosed"
  >
    <div v-loading="loading">
      <ElTransfer
        v-model="selectedRoleIds"
        :data="transferData"
        :titles="['可分配角色', '已分配角色']"
        :button-texts="['移除', '分配']"
        :format="{
          noChecked: '${total}',
          hasChecked: '${checked}/${total}'
        }"
        filterable
        filter-placeholder="搜索角色"
        @change="handleRoleChange"
        class="role-transfer"
      >
        <template #default="{ option }">
          <div class="role-item">
            <div class="role-name">{{ option.label }}</div>
            <div v-if="option.description" class="role-description">{{ option.description }}</div>
          </div>
        </template>
      </ElTransfer>
    </div>
    <template #footer>
      <ElButton @click="handleCancel">取消</ElButton>
      <ElButton type="primary" :loading="submitLoading" @click="handleSubmit">确定</ElButton>
    </template>
  </ElDialog>
</template>

<script setup lang="ts">
  defineOptions({ name: 'UserRoleDialog' })

  import { ref, computed, watch } from 'vue'
  import { ElMessage } from 'element-plus'
  import type { TransferKey, TransferDirection } from 'element-plus'
  import { getRoleList, syncUserRoles } from '@/api/admin/roleApi'
  import type { RoleListItem } from '@/types/api/role'

  // Transfer 组件需要的数据结构
  interface TransferOption {
    key: number
    label: string
    description?: string
    disabled?: boolean
  }

  interface Props {
    modelValue: boolean
    userId: number | null
    userName: string
    currentRoleIds: number[]
  }

  const props = defineProps<Props>()
  const emit = defineEmits<{
    'update:modelValue': [value: boolean]
    success: []
  }>()

  // 对话框显示状态
  const dialogVisible = computed({
    get: () => props.modelValue,
    set: (val) => emit('update:modelValue', val)
  })

  // 数据状态
  const loading = ref(false)
  const submitLoading = ref(false)
  const roleList = ref<RoleListItem[]>([])
  const selectedRoleIds = ref<number[]>([])

  // Transfer 组件数据
  const transferData = computed<TransferOption[]>(() => {
    return roleList.value.map((role) => ({
      key: role.id,
      label: role.name,
      description: role.description || '暂无描述',
      disabled: false
    }))
  })

  // 监听对话框打开
  watch(
    () => props.modelValue,
    async (newVal) => {
      if (newVal && props.userId) {
        await loadRoles()
        // 设置当前用户已有的角色
        selectedRoleIds.value = [...props.currentRoleIds]
      }
    }
  )

  // 加载角色列表
  const loadRoles = async () => {
    loading.value = true
    const res = await getRoleList({ per_page: 100 })
    roleList.value = res.data
    loading.value = false
  }

  // 处理角色变更
  const handleRoleChange = (
    value: TransferKey[],
    direction: TransferDirection,
    movedKeys: TransferKey[]
  ) => {
    // 将 TransferKey 转换为 number 数组
    selectedRoleIds.value = value.map((key) => Number(key))
    console.log('角色变更:', { value, direction, movedKeys })

    if (direction === 'right') {
      ElMessage.success(`成功分配 ${movedKeys.length} 个角色`)
    } else {
      ElMessage.info(`移除了 ${movedKeys.length} 个角色`)
    }
  }

  // 提交
  const handleSubmit = async () => {
    if (!props.userId) return

    submitLoading.value = true
    await syncUserRoles(props.userId, {
      role_ids: selectedRoleIds.value
    })
    ElMessage.success('角色分配成功')
    emit('success')
    dialogVisible.value = false
    submitLoading.value = false
  }

  // 取消
  const handleCancel = () => {
    dialogVisible.value = false
  }

  // 对话框关闭后重置
  const handleClosed = () => {
    selectedRoleIds.value = []
    roleList.value = []
  }
</script>

<style scoped>
  .role-item {
    display: flex;
    flex-direction: column;
    width: 100%;
    line-height: 1.5;
  }

  .role-name {
    margin-bottom: 2px;
    font-weight: 500;
    color: #303133;
  }

  .role-description {
    font-size: 12px;
    line-height: 1.4;
    color: #909399;
  }

  .role-transfer {
    min-height: 450px;
  }

  :deep(.el-transfer) {
    display: flex !important;
    flex-direction: row !important;
    gap: 20px !important;
    align-items: flex-start !important;
    justify-content: center !important;
  }

  :deep(.el-transfer-panel) {
    flex-shrink: 0;
    width: 320px;
    height: 450px;
  }

  :deep(.el-transfer-panel__header) {
    background: #f5f7fa;
    border-bottom: 1px solid #e4e7ed;
  }

  :deep(.el-transfer-panel__item) {
    padding: 8px 12px;
    margin-bottom: 0;
    border-bottom: 1px solid #f5f7fa;
  }

  :deep(.el-transfer-panel__item:hover) {
    background-color: #f5f7fa;
  }

  :deep(.el-transfer-panel__filter) {
    padding: 12px;
    border-bottom: 1px solid #e4e7ed;
  }

  :deep(.el-transfer__buttons) {
    display: flex !important;
    flex-direction: column !important;
    gap: 10px !important;
    align-items: center !important;
    align-self: center !important;
    justify-content: center !important;
    padding: 0 15px !important;
  }

  :deep(.el-transfer__button) {
    display: block !important;
    min-width: 60px !important;
    margin: 0 0 10px !important;
  }
</style>
