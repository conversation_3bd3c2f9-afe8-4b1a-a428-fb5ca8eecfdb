<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class AssetImportRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'file' => [
                'required',
                'file',
                'mimes:xlsx,xls',
                'max:10240', // 10MB
            ],
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'file.required' => '请选择要导入的Excel文件',
            'file.file' => '上传的文件格式不正确',
            'file.mimes' => '只支持Excel文件格式（.xlsx, .xls）',
            'file.max' => '文件大小不能超过10MB',
        ];
    }
}
