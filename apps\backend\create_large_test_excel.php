<?php

/**
 * 创建大数据量测试Excel文件（用于测试批量导入性能）
 */

require_once __DIR__ . '/vendor/autoload.php';

use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;

echo "创建大数据量测试Excel文件...\n";

// 创建新的电子表格
$spreadsheet = new Spreadsheet();
$sheet = $spreadsheet->getActiveSheet();

// 设置标题行
$headers = [
    '资产名称', '资产品牌', '规格型号', '序列号', '资产分类',
    '资产来源', '资产状态', '成色', '区县代码', '详细地址',
    '启用日期', '合同质保期(月)', '质保期预警(天)', '维护周期(天)', '预计使用年限(年)',
    '主体名称', '税号', '主体类型', '主体地址', '主体电话',
    '联系人姓名', '联系人电话', '职位', '部门', '备注'
];

// 写入标题行
$column = 'A';
foreach ($headers as $header) {
    $sheet->setCellValue($column . '1', $header);
    $column++;
}

// 生成测试数据模板
$assetTypes = ['办公电脑', '打印机', '服务器', '网络设备', '投影仪', '扫描仪', '复印机', '路由器'];
$brands = ['联想', '戴尔', '惠普', '华为', '思科', '佳能', '爱普生', '小米'];
$models = ['M720', 'R740', 'LaserJet', 'Pro', 'Elite', 'ThinkCentre', 'OptiPlex', 'Pavilion'];
$sources = ['采购', '租赁', '捐赠', '调拨'];
$statuses = ['在用', '闲置', '维修', '报废'];
$conditions = ['全新', '九成新', '八成新', '七成新'];
$entityTypes = ['企业', '事业单位', '政府机关', '个人'];
$positions = ['技术经理', '系统管理员', '网络工程师', '运维专员', '项目经理'];
$departments = ['技术部', '运维部', '网络部', '信息部', '研发部'];

// 公司名称模板
$companyPrefixes = ['北京', '上海', '深圳', '广州', '杭州', '成都', '武汉', '西安'];
$companySuffixes = ['科技有限公司', '信息技术有限公司', '网络科技有限公司', '软件有限公司'];

// 生成指定数量的测试数据
$totalRows = 2500; // 生成2500行数据，测试批量处理
echo "正在生成 {$totalRows} 行测试数据...\n";

for ($i = 1; $i <= $totalRows; $i++) {
    $row = $i + 1; // Excel行号从2开始
    
    // 生成随机数据
    $assetName = $assetTypes[array_rand($assetTypes)] . sprintf('%04d', $i);
    $brand = $brands[array_rand($brands)];
    $model = $models[array_rand($models)] . '-' . sprintf('%03d', rand(100, 999));
    $serialNumber = strtoupper(substr($brand, 0, 2)) . sprintf('%08d', $i);
    $category = $assetTypes[array_rand($assetTypes)] . ',IT设备';
    
    $companyName = $companyPrefixes[array_rand($companyPrefixes)] . 
                   $companySuffixes[array_rand($companySuffixes)];
    $taxNumber = '9111' . sprintf('%014d', 1000000000000 + $i) . 'X';
    
    $contactName = '联系人' . sprintf('%04d', $i);
    $contactPhone = '138' . sprintf('%08d', rand(10000000, 99999999));
    
    $testData = [
        $assetName,
        $brand,
        $model,
        $serialNumber,
        $category,
        $sources[array_rand($sources)],
        $statuses[array_rand($statuses)],
        $conditions[array_rand($conditions)],
        '110101',
        '北京市朝阳区XX路' . $i . '号',
        '2024-' . sprintf('%02d', rand(1, 12)) . '-' . sprintf('%02d', rand(1, 28)),
        rand(12, 60),
        rand(15, 90),
        rand(30, 180),
        rand(3, 10),
        $companyName,
        $taxNumber,
        $entityTypes[array_rand($entityTypes)],
        '北京市朝阳区XX路' . $i . '号办公楼',
        '010-' . sprintf('%08d', rand(10000000, 99999999)),
        $contactName,
        $contactPhone,
        $positions[array_rand($positions)],
        $departments[array_rand($departments)],
        '测试数据第' . $i . '行'
    ];
    
    // 写入数据行
    $column = 'A';
    foreach ($testData as $value) {
        $sheet->setCellValue($column . $row, $value);
        $column++;
    }
    
    // 每1000行显示进度
    if ($i % 1000 === 0) {
        echo "已生成 {$i} 行数据...\n";
    }
}

// 设置列宽自适应（仅前几列，避免处理时间过长）
foreach (range('A', 'J') as $col) {
    $sheet->getColumnDimension($col)->setAutoSize(true);
}

// 保存文件
$writer = new Xlsx($spreadsheet);
$filename = 'storage/app/public/large_test_assets_import.xlsx';

// 确保目录存在
$dir = dirname($filename);
if (!is_dir($dir)) {
    mkdir($dir, 0755, true);
}

echo "正在保存Excel文件...\n";
$writer->save($filename);

$fileSize = round(filesize($filename) / 1024 / 1024, 2);

echo "\n=== 大数据量测试文件创建完成 ===\n";
echo "文件路径: {$filename}\n";
echo "数据行数: {$totalRows} 行\n";
echo "文件大小: {$fileSize} MB\n";

echo "\n性能测试建议：\n";
echo "1. 批量大小: 1000 行/批次\n";
echo "2. 预计批次: " . ceil($totalRows / 1000) . " 个批次\n";
echo "3. 内存使用: 建议设置 memory_limit >= 512M\n";
echo "4. 执行时间: 建议设置 max_execution_time >= 300s\n";

echo "\n测试命令：\n";
echo "curl -X POST http://your-domain/api/admin/assets/import \\\n";
echo "  -H \"Authorization: Bearer your-token\" \\\n";
echo "  -F \"file=@{$filename}\"\n";

echo "\n监控要点：\n";
echo "- 数据库连接数\n";
echo "- 内存使用情况\n";
echo "- 批量插入性能\n";
echo "- 错误处理效果\n";
echo "- 事务回滚测试\n";

echo "\n文件创建完成！\n";
