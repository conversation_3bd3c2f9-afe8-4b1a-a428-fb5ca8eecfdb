<template>
  <div class="attachment-upload">
    <ElUpload
      v-model:file-list="fileList"
      :class="{ 'hide-upload': hideUpload }"
      :list-type="listType"
      :http-request="customUpload"
      :multiple="multiple"
      :limit="limit"
      :accept="accept"
      :disabled="disabled"
      :on-exceed="handleExceed"
      :on-remove="handleRemove"
      :before-upload="beforeUpload"
      :on-preview="handlePreview"
      :on-success="handleSuccess"
      :on-error="handleError"
      :auto-upload="autoUpload"
      :drag="drag"
    >
      <!-- 上传触发器插槽 -->
      <slot>
        <ElButton type="primary" plain size="small" :disabled="disabled">
          <ElIcon class="el-icon--left"><Upload /></ElIcon>
          {{ buttonText }}
        </ElButton>
      </slot>

      <!-- 提示信息插槽 -->
      <template #tip v-if="showTip">
        <div class="el-upload__tip">
          <slot name="tip">
            {{ tipText }}
          </slot>
        </div>
      </template>
    </ElUpload>
  </div>
</template>

<script setup lang="ts">
  import { computed, toRef } from 'vue'
  import { ElMessage, ElUpload, ElButton, ElIcon } from 'element-plus'
  import { Upload } from '@element-plus/icons-vue'
  import type { UploadProps } from 'element-plus'
  import { useUpload } from '@/composables/useUpload'
  import type { AttachmentItem } from '@/types/api'

  /**
   * 附件上传组件
   *
   * @description
   * 通用的附件上传组件，支持多文件上传、文件类型限制、大小限制等功能。
   * 使用 Vue 3.5 的 defineModel 实现 v-model 双向绑定。
   *
   * @example
   * ```vue
   * <AttachmentUpload
   *   v-model="attachmentIds"
   *   :attachments="attachmentList"
   *   :limit="5"
   *   :max-size="10"
   *   accept=".pdf,.doc,.docx"
   *   button-text="上传文档"
   * />
   * ```
   */
  defineOptions({
    name: 'AttachmentUpload'
  })

  interface Props {
    /**
     * 附件详情数组（用于编辑时显示）
     */
    attachments?: AttachmentItem[]
    /**
     * 列表展示类型
     * @default 'text'
     */
    listType?: 'text' | 'picture' | 'picture-card'
    /**
     * 是否支持多选
     * @default true
     */
    multiple?: boolean
    /**
     * 最大上传数量
     * @default 10
     */
    limit?: number
    /**
     * 接受的文件类型，如 '.jpg,.png' 或 'image/*'
     * @default ''
     */
    accept?: string
    /**
     * 单个文件最大大小（MB）
     * @default 10
     */
    maxSize?: number
    /**
     * 是否禁用
     * @default false
     */
    disabled?: boolean
    /**
     * 是否显示提示信息
     * @default true
     */
    showTip?: boolean
    /**
     * 上传按钮文字
     * @default '上传附件'
     */
    buttonText?: string
    /**
     * 提示文字
     * @default '最多上传10个文件，单个文件不超过10MB'
     */
    tipText?: string
    /**
     * 是否自动上传
     * @default true
     */
    autoUpload?: boolean
    /**
     * 是否启用拖拽上传
     * @default false
     */
    drag?: boolean
  }

  const props = withDefaults(defineProps<Props>(), {
    attachments: () => [],
    listType: 'text',
    multiple: true,
    limit: 10,
    accept: '',
    maxSize: 10,
    disabled: false,
    showTip: true,
    buttonText: '上传附件',
    tipText: '最多上传10个文件，单个文件不超过10MB',
    autoUpload: true,
    drag: false
  })

  /**
   * 定义 v-model，使用 Vue 3.5 的 defineModel
   */
  const modelValue = defineModel<number[]>({
    default: () => []
  })

  /**
   * 定义事件
   */
  const emit = defineEmits<{
    /**
     * 附件列表变化事件
     */
    change: [value: number[]]
    /**
     * 上传成功事件
     */
    'upload-success': [file: any]
    /**
     * 上传失败事件
     */
    'upload-error': [error: any]
  }>()

  /**
   * 使用上传 composable
   */
  const {
    fileList,
    hasUploading,
    isLimitReached,
    beforeUpload,
    customUpload,
    handleSuccess: baseHandleSuccess,
    handleError: baseHandleError,
    handleRemove,
    handleExceed,
    clearFiles,
    getFiles
  } = useUpload(modelValue, toRef(props, 'attachments'), {
    limit: props.limit,
    maxSize: props.maxSize,
    accept: props.accept,
    multiple: props.multiple,
    showSuccessMessage: false, // 自定义成功消息
    onSuccess: (file) => {
      // 触发成功事件
      emit('upload-success', file.response)
      // 触发 change 事件
      emit('change', modelValue.value)
      // 显示成功消息
      ElMessage.success('文件上传成功')
    },
    onError: (error) => {
      // 触发错误事件
      emit('upload-error', error)
    },
    onRemove: () => {
      // 触发 change 事件
      emit('change', modelValue.value)
    }
  })

  /**
   * 是否隐藏上传按钮
   * 单文件模式下，达到限制时隐藏
   */
  const hideUpload = computed(() => {
    return !props.multiple && isLimitReached.value
  })

  /**
   * 处理文件预览
   */
  const handlePreview: UploadProps['onPreview'] = (uploadFile) => {
    if (uploadFile.url) {
      window.open(uploadFile.url, '_blank')
    } else {
      ElMessage.warning('文件URL不存在')
    }
  }

  /**
   * 包装处理成功方法，确保参数正确传递
   */
  const handleSuccess: UploadProps['onSuccess'] = (response, file, fileList) => {
    baseHandleSuccess(response, file, fileList)
  }

  /**
   * 包装错误处理方法
   */
  const handleError: UploadProps['onError'] = (error, file) => {
    baseHandleError(error, file)
  }

  /**
   * 暴露方法给父组件
   */
  defineExpose({
    /**
     * 获取已上传的文件列表
     */
    getFiles,
    /**
     * 清空文件列表
     */
    clearFiles,
    /**
     * 是否有文件正在上传
     */
    hasUploading
  })
</script>

<style lang="scss" scoped>
  .attachment-upload {
    width: 100%;

    // 隐藏上传按钮（单文件模式下达到限制时）
    .hide-upload {
      :deep(.el-upload) {
        display: none;
      }
    }

    // 拖拽上传区域样式
    :deep(.el-upload-dragger) {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      width: 100%;
      height: 180px;

      .el-icon {
        margin-bottom: 16px;
        font-size: 67px;
        color: var(--el-text-color-placeholder);
      }

      .el-upload__text {
        font-size: 14px;
        color: var(--el-text-color-regular);

        em {
          font-style: normal;
          color: var(--el-color-primary);
        }
      }
    }

    // 图片卡片模式样式调整
    :deep(.el-upload-list--picture-card) {
      .el-upload-list__item {
        width: 100px;
        height: 100px;
      }
    }
  }
</style>
