<?php

namespace App\Models;

use App\Services\Attachment\StorageManager;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphToMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\Models\BaseModel;

/**
 * @property int $id
 * @property string $file_name 原始文件名
 * @property string $file_path 存储路径
 * @property int $file_size 文件大小(字节)
 * @property string $mime_type MIME类型
 * @property string $storage_type 存储类型:local/alioss/qiniu/aws
 * @property string|null $md5_hash MD5哈希值
 * @property int|null $created_at 创建时间
 * @property int|null $updated_at 更新时间
 * @property \Illuminate\Support\Carbon|null $deleted_at 删除时间
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\AttachmentRelation> $attachables
 * @property-read int|null $attachables_count
 * @property-read \App\Models\User|null $creator
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Entity> $entities
 * @property-read int|null $entities_count
 * @property-read string $file_url
 * @property-read string $formatted_file_size
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Attachment newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Attachment newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Attachment onlyTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Attachment query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Attachment whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Attachment whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Attachment whereFileName($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Attachment whereFilePath($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Attachment whereFileSize($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Attachment whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Attachment whereMd5Hash($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Attachment whereMimeType($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Attachment whereStorageType($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Attachment whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Attachment withTrashed(bool $withTrashed = true)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Attachment withoutTrashed()
 * @mixin \Eloquent
 */
class Attachment extends BaseModel
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'file_name',
        'file_path',
        'file_size',
        'mime_type',
        'storage_type',
        'md5_hash',
    ];

    protected $casts = [
        'file_size' => 'integer',
        'created_at' => 'integer',
        'updated_at' => 'integer',
    ];

    protected $appends = ['file_url', 'formatted_file_size'];

    /**
     * 获取文件访问URL
     */
    public function getFileUrlAttribute(): string
    {
        if (! $this->file_path) {
            return '';
        }

        try {
            $storageManager = app(StorageManager::class);
            $driver = $storageManager->driver($this->storage_type);

            return $driver->url($this->file_path);
        } catch (\Exception $e) {
            \Log::info("Attachment::getFileUrlAttribute", [
                'storage_type' => $this->storage_type,
                'file_path' => $this->file_path,
                'e' => $e->getMessage()
            ]);

            // 如果驱动不存在，返回原始路径
            return $this->file_path;
        }
    }

    /**
     * 获取格式化的文件大小
     */
    public function getFormattedFileSizeAttribute(): string
    {
        $bytes = $this->file_size;

        if ($bytes >= 1073741824) {
            return number_format($bytes / 1073741824, 2) . ' GB';
        } elseif ($bytes >= 1048576) {
            return number_format($bytes / 1048576, 2) . ' MB';
        } elseif ($bytes >= 1024) {
            return number_format($bytes / 1024, 2) . ' KB';
        } else {
            return $bytes . ' bytes';
        }
    }

    /**
     * 获取所有拥有此附件的主体
     */
    public function entities(): MorphToMany
    {
        return $this->morphedByMany(Entity::class, 'attachable', 'attachment_relations')
            ->withPivot(['category', 'sort', 'description'])
            ->withTimestamps();
    }

    /**
     * 获取附件的所有关联
     */
    public function attachables()
    {
        return $this->hasMany(AttachmentRelation::class);
    }

    /**
     * 获取创建人
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }
}
