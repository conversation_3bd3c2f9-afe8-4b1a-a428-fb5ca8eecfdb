<?php

/**
 * 测试导入过程并查看日志
 */

require_once __DIR__ . '/vendor/autoload.php';

use Illuminate\Foundation\Application;
use App\Models\Attachment;
use App\Models\AssetImportTask;
use App\Services\AssetImportService;

// 启动Laravel应用
$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "=== 测试导入过程并查看日志 ===\n\n";

try {
    // 查找现有的大数据文件
    echo "1. 查找测试文件...\n";
    
    $filePath = 'storage/app/public/大数据资产导入模板_2025-08-14_06-26-47.xlsx';
    $fullPath = __DIR__ . '/' . $filePath;
    
    if (!file_exists($fullPath)) {
        echo "测试文件不存在: {$fullPath}\n";
        exit(1);
    }
    
    echo "找到测试文件: {$filePath}\n";
    
    // 创建附件记录
    echo "\n2. 创建附件记录...\n";
    
    $fileName = basename($filePath);
    $attachment = Attachment::where('file_name', $fileName)->first();
    
    if (!$attachment) {
        $attachment = Attachment::create([
            'file_name' => $fileName,
            'file_path' => str_replace('storage/app/public/', '', $filePath),
            'file_size' => filesize($fullPath),
            'mime_type' => 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            'storage_type' => 'local',
            'md5_hash' => md5_file($fullPath),
        ]);
        echo "创建新附件记录，ID: {$attachment->id}\n";
    } else {
        echo "使用现有附件记录，ID: {$attachment->id}\n";
    }
    
    // 创建导入任务
    echo "\n3. 创建导入任务...\n";
    
    $importTask = AssetImportTask::create([
        'file_path' => $attachment->file_path,
        'original_filename' => $attachment->file_name,
        'status' => 'pending',
        'created_by' => 1,
    ]);
    
    echo "导入任务创建成功，ID: {$importTask->id}\n";
    
    // 执行导入（只处理少量数据）
    echo "\n4. 执行导入测试...\n";
    
    $service = new AssetImportService();
    $reflection = new ReflectionClass($service);
    
    // 设置小批次大小
    $batchSizeProperty = $reflection->getProperty('batchSize');
    $batchSizeProperty->setAccessible(true);
    $batchSizeProperty->setValue($service, 5); // 只处理5行数据
    
    $importTask->markAsProcessing();
    
    $startTime = microtime(true);
    $result = $service->processImport($importTask);
    $endTime = microtime(true);
    
    echo "导入完成！\n";
    echo "耗时: " . number_format($endTime - $startTime, 2) . " 秒\n";
    echo "总行数: " . $result['total_rows'] . "\n";
    echo "成功行数: " . $result['success_rows'] . "\n";
    echo "失败行数: " . $result['failed_rows'] . "\n";
    
    if (!empty($result['errors'])) {
        echo "错误信息:\n";
        foreach ($result['errors'] as $error) {
            echo "  行 {$error['row']}: {$error['error']}\n";
        }
    }
    
    $importTask->markAsCompleted($result);
    
    // 检查导入的资产
    echo "\n5. 检查导入的资产...\n";
    
    $newAssets = \App\Models\Asset::where('created_at', '>=', $startTime)->get();
    
    echo "新导入的资产数量: " . $newAssets->count() . "\n";
    
    foreach ($newAssets->take(3) as $asset) {
        echo "资产ID: {$asset->id}, 名称: {$asset->name}\n";
        echo "  related_entities: ";
        if (is_null($asset->related_entities)) {
            echo "NULL\n";
        } elseif (empty($asset->related_entities)) {
            echo "空\n";
        } else {
            $relatedEntities = is_array($asset->related_entities) 
                ? $asset->related_entities 
                : json_decode($asset->related_entities, true);
            echo "包含 " . count($relatedEntities) . " 个主体\n";
            
            foreach ($relatedEntities as $index => $entity) {
                echo "    主体 " . ($index + 1) . ": ID {$entity['entity_id']}, 类型 {$entity['entity_type']}, 联系人 {$entity['contact_name']}\n";
            }
        }
    }
    
    // 检查日志文件
    echo "\n6. 检查最新的日志...\n";
    
    $logPath = storage_path('logs/laravel.log');
    if (file_exists($logPath)) {
        $logContent = file_get_contents($logPath);
        $logLines = explode("\n", $logContent);
        
        // 查找最近的相关主体构建日志
        $relevantLogs = [];
        foreach (array_reverse($logLines) as $line) {
            if (strpos($line, '构建相关主体数据') !== false) {
                $relevantLogs[] = $line;
                if (count($relevantLogs) >= 5) break;
            }
        }
        
        if (!empty($relevantLogs)) {
            echo "最近的相关主体构建日志:\n";
            foreach (array_reverse($relevantLogs) as $log) {
                echo "  " . $log . "\n";
            }
        } else {
            echo "未找到相关主体构建日志\n";
        }
    } else {
        echo "日志文件不存在\n";
    }
    
    echo "\n=== 测试完成 ===\n";
    
} catch (Exception $e) {
    echo "✗ 测试失败：" . $e->getMessage() . "\n";
    echo "错误位置：" . $e->getFile() . ":" . $e->getLine() . "\n";
}

echo "\n=== 测试结束 ===\n";
