<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // 创建生命周期管理表
        Schema::create('lifecycles', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('asset_id')->nullable()->comment('资产ID');
            $table->string('type', 50)->comment('类型');
            $table->unsignedInteger('date')->nullable()->comment('日期');
            $table->unsignedBigInteger('initiator_id')->nullable()->comment('发起人ID');
            $table->text('content')->nullable()->comment('内容');
            $table->unsignedBigInteger('acceptance_entity_id')->nullable()->comment('验收主体ID');
            $table->unsignedBigInteger('acceptance_personnel_id')->nullable()->comment('验收人员ID');
            $table->unsignedInteger('acceptance_time')->nullable()->comment('验收时间');
            $table->unsignedBigInteger('created_by')->nullable()->comment('创建人');
            $table->unsignedBigInteger('updated_by')->nullable()->comment('更新人');
            $table->bigInteger('created_at')->nullable()->comment('创建时间');
            $table->bigInteger('updated_at')->nullable()->comment('更新时间');
            $table->bigInteger('deleted_at')->nullable()->comment('删除时间');
            // $table->softDeletes();

            // 索引
            $table->index('asset_id');
            $table->index('type');
            $table->index('date');
            $table->index('initiator_id');
            $table->index('acceptance_entity_id');
            $table->index('acceptance_personnel_id');
            $table->index('created_at');

            // 外键约束
            $table->foreign('initiator_id')->references('id')->on('users')->onDelete('restrict');
            $table->foreign('acceptance_entity_id')->references('id')->on('entities')->onDelete('restrict');
            $table->foreign('acceptance_personnel_id')->references('id')->on('entity_contacts')->onDelete('restrict');

            $table->comment('生命周期管理表');
        });

        // 创建生命周期协助人员关联表
        Schema::create('lifecycle_assistants', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('lifecycle_id')->comment('生命周期ID');
            $table->unsignedBigInteger('user_id')->comment('用户ID');
            $table->bigInteger('created_at')->nullable()->comment('创建时间');
            $table->bigInteger('updated_at')->nullable()->comment('更新时间');

            // 索引
            $table->unique(['lifecycle_id', 'user_id']);
            $table->index('lifecycle_id');
            $table->index('user_id');

            // 外键约束
            $table->foreign('lifecycle_id')->references('id')->on('lifecycles')->onDelete('cascade');
            $table->foreign('user_id')->references('id')->on('users')->onDelete('restrict');

            $table->comment('生命周期协助人员关联表');
        });

        // 创建生命周期跟进记录表
        Schema::create('lifecycle_follow_ups', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('lifecycle_id')->comment('生命周期ID');
            $table->unsignedInteger('date')->comment('日期');
            $table->unsignedBigInteger('person_id')->comment('跟进人ID');
            $table->text('content')->comment('内容');
            $table->unsignedBigInteger('created_by')->nullable()->comment('创建人');
            $table->unsignedBigInteger('updated_by')->nullable()->comment('更新人');
            $table->bigInteger('created_at')->nullable()->comment('创建时间');
            $table->bigInteger('updated_at')->nullable()->comment('更新时间');

            // 索引
            $table->index('lifecycle_id');
            $table->index('date');
            $table->index('person_id');

            // 外键约束
            $table->foreign('lifecycle_id')->references('id')->on('lifecycles')->onDelete('cascade');
            $table->foreign('person_id')->references('id')->on('users')->onDelete('restrict');

            $table->comment('生命周期跟进记录表');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('lifecycle_follow_ups');
        Schema::dropIfExists('lifecycle_assistants');
        Schema::dropIfExists('lifecycles');
    }
};
