<template>
  <ElDialog
    v-model="dialogVisible"
    :title="title"
    width="500px"
    :close-on-click-modal="false"
    @closed="handleClosed"
  >
    <ElForm ref="formRef" :model="formData" :rules="rules" label-width="100px" :disabled="loading">
      <ElFormItem label="角色名称" prop="name">
        <ElInput
          v-model="formData.name"
          placeholder="请输入角色名称"
          maxlength="50"
          show-word-limit
        />
      </ElFormItem>
      <ElFormItem label="角色描述" prop="description">
        <ElInput
          v-model="formData.description"
          type="textarea"
          placeholder="请输入角色描述"
          :rows="3"
          maxlength="200"
          show-word-limit
        />
      </ElFormItem>
    </ElForm>
    <template #footer>
      <ElButton @click="handleCancel">取消</ElButton>
      <ElButton type="primary" :loading="loading" @click="handleSubmit">确定</ElButton>
    </template>
  </ElDialog>
</template>

<script setup lang="ts">
  defineOptions({ name: 'RoleDialog' })

  import { ElMessage } from 'element-plus'
  import type { FormInstance, FormRules } from 'element-plus'
  import { createRole, updateRole } from '@/api/admin/roleApi'
  import type { RoleListItem, RoleForm } from '@/types/api/role'

  interface Props {
    visible: boolean
    type: 'add' | 'edit'
    data: RoleListItem | null
  }

  const props = defineProps<Props>()
  const emit = defineEmits<{
    'update:visible': [value: boolean]
    submit: []
  }>()

  // 对话框显示状态
  const dialogVisible = computed({
    get: () => props.visible,
    set: (val) => emit('update:visible', val)
  })

  // 对话框标题
  const title = computed(() => (props.type === 'add' ? '新增角色' : '编辑角色'))

  // 表单相关
  const formRef = ref<FormInstance>()
  const loading = ref(false)
  const formData = ref<RoleForm>({
    name: '',
    description: ''
  })

  // 表单验证规则
  const rules: FormRules = {
    name: [
      { required: true, message: '请输入角色名称', trigger: 'blur' },
      { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
    ],
    description: [{ max: 200, message: '角色描述不能超过 200 个字符', trigger: 'blur' }]
  }

  // 监听 props.data 变化，初始化表单数据
  watch(
    () => props.data,
    (newData) => {
      if (newData && props.type === 'edit') {
        formData.value = {
          name: newData.name,
          description: newData.description || ''
        }
      } else {
        formData.value = {
          name: '',
          description: ''
        }
      }
    },
    { immediate: true }
  )

  // 提交表单
  const handleSubmit = async () => {
    await formRef.value?.validate()

    loading.value = true
    if (props.type === 'add') {
      await createRole(formData.value)
      ElMessage.success('新增成功')
    } else {
      await updateRole(props.data!.id, formData.value)
      ElMessage.success('编辑成功')
    }

    emit('submit')
    dialogVisible.value = false
    loading.value = false
  }

  // 取消
  const handleCancel = () => {
    dialogVisible.value = false
  }

  // 对话框关闭后重置表单
  const handleClosed = () => {
    formRef.value?.resetFields()
    formData.value = {
      name: '',
      description: ''
    }
  }
</script>
