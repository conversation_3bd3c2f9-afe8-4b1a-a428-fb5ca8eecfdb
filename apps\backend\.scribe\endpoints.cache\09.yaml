## Autogenerated by <PERSON>ribe. DO NOT MODIFY.

name: 地区管理
description: |-

  省市区地区数据管理接口
endpoints:
  -
    httpMethods:
      - GET
    uri: api/admin/regions/tree
    metadata:
      groupName: 地区管理
      groupDescription: |-

        省市区地区数据管理接口
      subgroup: ''
      subgroupDescription: ''
      title: 获取地区树形结构
      description: 获取完整的省市区三级树形结构数据，用于级联选择器
      authenticated: true
      deprecated: false
      custom: []
    headers:
      Authorization: 'Bearer {YOUR_AUTH_KEY}'
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters:
      deep:
        name: deep
        description: 获取的层级深度（可选，默认3级）
        required: false
        example: 3
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanQueryParameters:
      deep: 3
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"message":"Unauthenticated."}'
        headers:
          cache-control: 'no-cache, private'
          content-type: application/json
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth:
      - headers
      - Authorization
      - 'Bearer 6g43cv8PD1aE5beadkZfhV6'
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: 'api/admin/regions/children/{parentId}'
    metadata:
      groupName: 地区管理
      groupDescription: |-

        省市区地区数据管理接口
      subgroup: ''
      subgroupDescription: ''
      title: 获取指定父级的子地区
      description: 用于懒加载获取子地区数据
      authenticated: true
      deprecated: false
      custom: []
    headers:
      Authorization: 'Bearer {YOUR_AUTH_KEY}'
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      parentId:
        name: parentId
        description: 父级地区ID
        required: true
        example: 11
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanUrlParameters:
      parentId: 11
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"message":"Unauthenticated."}'
        headers:
          cache-control: 'no-cache, private'
          content-type: application/json
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth:
      - headers
      - Authorization
      - 'Bearer 6g43cv8PD1aE5beadkZfhV6'
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: 'api/admin/regions/path/{code}'
    metadata:
      groupName: 地区管理
      groupDescription: |-

        省市区地区数据管理接口
      subgroup: ''
      subgroupDescription: ''
      title: 根据地区代码获取完整路径
      description: 根据区县代码获取省市区完整路径信息
      authenticated: true
      deprecated: false
      custom: []
    headers:
      Authorization: 'Bearer {YOUR_AUTH_KEY}'
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      code:
        name: code
        description: 地区代码
        required: true
        example: '110101000000'
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanUrlParameters:
      code: '110101000000'
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"message":"Unauthenticated."}'
        headers:
          cache-control: 'no-cache, private'
          content-type: application/json
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth:
      - headers
      - Authorization
      - 'Bearer 6g43cv8PD1aE5beadkZfhV6'
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: api/admin/regions/search
    metadata:
      groupName: 地区管理
      groupDescription: |-

        省市区地区数据管理接口
      subgroup: ''
      subgroupDescription: ''
      title: 搜索地区
      description: 根据关键词搜索地区，支持名称和拼音搜索
      authenticated: true
      deprecated: false
      custom: []
    headers:
      Authorization: 'Bearer {YOUR_AUTH_KEY}'
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters:
      keyword:
        name: keyword
        description: 搜索关键词
        required: true
        example: 北京
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      limit:
        name: limit
        description: 返回结果数量限制（默认20，最大50）
        required: false
        example: 20
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      deep:
        name: deep
        description: 搜索的层级（0省1市2区县，默认搜索所有层级）
        required: false
        example: 2
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanQueryParameters:
      keyword: 北京
      limit: 20
      deep: 2
    bodyParameters:
      keyword:
        name: keyword
        description: 'Must be at least 1 character.'
        required: true
        example: bngzmiyvdljnikhw
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      limit:
        name: limit
        description: 'Must be at least 1. Must not be greater than 50.'
        required: false
        example: 22
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      deep:
        name: deep
        description: ''
        required: false
        example: '1'
        type: integer
        enumValues:
          - '0'
          - '1'
          - '2'
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanBodyParameters:
      keyword: bngzmiyvdljnikhw
      limit: 22
      deep: '1'
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"message":"Unauthenticated."}'
        headers:
          cache-control: 'no-cache, private'
          content-type: application/json
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth:
      - headers
      - Authorization
      - 'Bearer 6g43cv8PD1aE5beadkZfhV6'
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: api/admin/regions/provinces
    metadata:
      groupName: 地区管理
      groupDescription: |-

        省市区地区数据管理接口
      subgroup: ''
      subgroupDescription: ''
      title: 获取省份列表
      description: 获取所有省级行政区列表
      authenticated: true
      deprecated: false
      custom: []
    headers:
      Authorization: 'Bearer {YOUR_AUTH_KEY}'
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"message":"Unauthenticated."}'
        headers:
          cache-control: 'no-cache, private'
          content-type: application/json
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth:
      - headers
      - Authorization
      - 'Bearer 6g43cv8PD1aE5beadkZfhV6'
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: 'api/admin/regions/cities/{provinceId}'
    metadata:
      groupName: 地区管理
      groupDescription: |-

        省市区地区数据管理接口
      subgroup: ''
      subgroupDescription: ''
      title: 获取指定省份的城市列表
      description: ''
      authenticated: true
      deprecated: false
      custom: []
    headers:
      Authorization: 'Bearer {YOUR_AUTH_KEY}'
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      provinceId:
        name: provinceId
        description: 省份ID
        required: true
        example: 11
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanUrlParameters:
      provinceId: 11
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"message":"Unauthenticated."}'
        headers:
          cache-control: 'no-cache, private'
          content-type: application/json
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth:
      - headers
      - Authorization
      - 'Bearer 6g43cv8PD1aE5beadkZfhV6'
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: 'api/admin/regions/districts/{cityId}'
    metadata:
      groupName: 地区管理
      groupDescription: |-

        省市区地区数据管理接口
      subgroup: ''
      subgroupDescription: ''
      title: 获取指定城市的区县列表
      description: ''
      authenticated: true
      deprecated: false
      custom: []
    headers:
      Authorization: 'Bearer {YOUR_AUTH_KEY}'
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      cityId:
        name: cityId
        description: 城市ID
        required: true
        example: 1101
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanUrlParameters:
      cityId: 1101
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"message":"Unauthenticated."}'
        headers:
          cache-control: 'no-cache, private'
          content-type: application/json
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth:
      - headers
      - Authorization
      - 'Bearer 6g43cv8PD1aE5beadkZfhV6'
    controller: null
    method: null
    route: null
    custom: []
