<?php

namespace App\Http\Requests\Admin;

use Illuminate\Foundation\Http\FormRequest;

class STSCredentialsRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'filename' => 'required|string|max:255',
            'filesize' => 'required|integer|min:1|max:5368709120', // 最大5GB
            'mime_type' => 'required|string|max:100',
            'md5_hash' => 'nullable|string|size:32',
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array
     */
    public function messages(): array
    {
        return [
            'filename.required' => '文件名不能为空',
            'filename.max' => '文件名过长',
            'filesize.required' => '文件大小不能为空',
            'filesize.integer' => '文件大小必须是整数',
            'filesize.min' => '文件大小无效',
            'filesize.max' => '文件大小不能超过5GB',
            'mime_type.required' => 'MIME类型不能为空',
            'mime_type.max' => 'MIME类型过长',
            'md5_hash.size' => 'MD5值格式错误',
        ];
    }
}