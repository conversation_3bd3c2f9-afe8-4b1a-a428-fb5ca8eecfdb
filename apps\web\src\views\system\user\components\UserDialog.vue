<template>
  <ElDialog
    v-model="dialogVisible"
    :title="dialogType === 'add' ? '添加用户' : '编辑用户'"
    width="30%"
    align-center
  >
    <ElForm ref="formRef" :model="formData" :rules="rules" label-width="80px">
      <ElFormItem label="头像" prop="avatar_id">
        <AvatarUpload
          v-model="formData.avatar_id"
          :attachments="currentAvatar"
          :max-size="10"
          accept="image/*"
          :tip="'只能上传图片文件，且不超过10MB'"
        />
      </ElFormItem>
      <ElFormItem label="账号" prop="account">
        <ElInput v-model="formData.account" placeholder="请输入11位手机号" maxlength="11" />
      </ElFormItem>
      <ElFormItem label="用户昵称" prop="nickname">
        <ElInput v-model="formData.nickname" placeholder="请输入用户昵称" />
      </ElFormItem>
      <ElFormItem label="邮箱" prop="email">
        <ElInput v-model="formData.email" placeholder="请输入邮箱" />
      </ElFormItem>
      <ElFormItem v-if="dialogType === 'add'" label="密码" prop="password">
        <ElInput
          v-model="formData.password"
          type="password"
          placeholder="请输入密码"
          show-password
        />
      </ElFormItem>
      <ElFormItem v-if="dialogType === 'add'" label="确认密码" prop="passwordConfirm">
        <ElInput
          v-model="formData.passwordConfirm"
          type="password"
          placeholder="请再次输入密码"
          show-password
        />
      </ElFormItem>
      <ElFormItem v-if="dialogType === 'edit'" label="密码" prop="password">
        <ElInput
          v-model="formData.password"
          type="password"
          placeholder="不修改请留空"
          show-password
        />
      </ElFormItem>
      <ElFormItem label="状态" prop="status">
        <ElRadioGroup v-model="formData.status">
          <ElRadio value="enable">启用</ElRadio>
          <ElRadio value="disable">禁用</ElRadio>
        </ElRadioGroup>
      </ElFormItem>
    </ElForm>
    <template #footer>
      <div class="dialog-footer">
        <ElButton @click="dialogVisible = false">取消</ElButton>
        <ElButton type="primary" @click="handleSubmit">提交</ElButton>
      </div>
    </template>
  </ElDialog>
</template>

<script setup lang="ts">
  import { ref, reactive, computed, watch, nextTick } from 'vue'
  import type { FormInstance, FormRules } from 'element-plus'
  import { handleSuccess } from '@/utils/errorHandler'
  import { createUser, updateUser } from '@/api/admin/userApi'
  import { AvatarUpload } from '@/components/custom/upload'
  import type { UserListItem, UserForm } from '@/types/api'

  interface Props {
    visible: boolean
    type: 'add' | 'edit'
    userData?: UserListItem | null
  }

  interface Emits {
    (e: 'update:visible', value: boolean): void
    (e: 'submit'): void
  }

  const props = defineProps<Props>()
  const emit = defineEmits<Emits>()

  // 对话框显示控制
  const dialogVisible = computed({
    get: () => props.visible,
    set: (value) => emit('update:visible', value)
  })

  const dialogType = computed(() => props.type)

  // 表单实例
  const formRef = ref<FormInstance>()

  // 表单数据
  const formData = reactive<UserForm & { passwordConfirm?: string }>({
    nickname: '',
    password: '',
    passwordConfirm: '',
    email: '',
    account: '',
    status: 'enable',
    avatar_id: null
  })

  // 当前头像附件（编辑时使用）
  const currentAvatar = ref<any[]>([])

  // 密码验证器
  const validatePassword = (rule: any, value: any, callback: any) => {
    if (dialogType.value === 'add' && !value) {
      callback(new Error('请输入密码'))
    } else if (value && value.length < 6) {
      callback(new Error('密码长度不能少于6位'))
    } else {
      callback()
    }
  }

  // 确认密码验证器
  const validatePasswordConfirm = (rule: any, value: any, callback: any) => {
    if (dialogType.value === 'add' && !value) {
      callback(new Error('请再次输入密码'))
    } else if (value !== formData.password) {
      callback(new Error('两次输入的密码不一致'))
    } else {
      callback()
    }
  }

  // 表单验证规则
  const rules: FormRules = {
    nickname: [
      { required: true, message: '请输入用户昵称', trigger: 'blur' },
      { min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'blur' }
    ],
    password: [{ validator: validatePassword, trigger: 'blur' }],
    passwordConfirm: [{ validator: validatePasswordConfirm, trigger: 'blur' }],
    email: [{ type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }],
    account: [
      { required: true, message: '请输入账号', trigger: 'blur' },
      { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的11位手机号', trigger: 'blur' }
    ],
    status: [{ required: true, message: '请选择状态', trigger: 'change' }]
  }

  // 初始化表单数据
  const initFormData = () => {
    if (props.type === 'edit' && props.userData) {
      const row = props.userData
      Object.assign(formData, {
        nickname: row.nickname || '',
        password: '', // 编辑时密码留空
        passwordConfirm: '',
        email: row.email || '',
        account: row.account || '',
        status: row.status || 'enable',
        avatar_id: row.avatar_id || null
      })

      // 设置当前头像
      currentAvatar.value =
        row.avatar && row.avatar_id
          ? [
              {
                id: row.avatar_id,
                file_name: '用户头像',
                file_path: row.avatar,
                file_url: row.avatar // 修复：使用 file_url 而不是 url
              }
            ]
          : []
    } else {
      // 新增时重置表单
      Object.assign(formData, {
        nickname: '',
        password: '',
        passwordConfirm: '',
        email: '',
        account: '',
        status: 'enable',
        avatar_id: null
      })
      currentAvatar.value = []
    }
  }

  // 统一监听对话框状态变化
  watch(
    () => [props.visible, props.type, props.userData],
    ([visible]) => {
      if (visible) {
        initFormData()
        nextTick(() => {
          formRef.value?.clearValidate()
        })
      }
    },
    { immediate: true, deep: true }
  )

  // 提交表单
  const handleSubmit = async () => {
    if (!formRef.value) return

    await formRef.value.validate(async (valid) => {
      if (valid) {
        // 准备提交数据
        const submitData: any = {
          nickname: formData.nickname,
          account: formData.account,
          status: formData.status
        }

        // 只有非空值才添加到提交数据中
        if (formData.email) submitData.email = formData.email
        if (formData.avatar_id !== null && formData.avatar_id !== undefined) {
          submitData.avatar_id = formData.avatar_id
        }

        // 处理密码
        if (dialogType.value === 'add') {
          submitData.password = formData.password
        } else if (formData.password) {
          submitData.password = formData.password
        }

        // 调用API
        if (dialogType.value === 'add') {
          await createUser(submitData)
          handleSuccess('创建成功')
        } else {
          await updateUser(props.userData!.id, submitData)
          handleSuccess('更新成功')
        }

        dialogVisible.value = false
        emit('submit')
      }
    })
  }
</script>
