<?php

/**
 * 调试主体数据创建过程
 */

require_once __DIR__ . '/vendor/autoload.php';

use Illuminate\Foundation\Application;
use App\Models\Entity;
use App\Models\EntityContact;
use App\Models\Asset;
use App\Models\AssetImportTask;

// 启动Laravel应用
$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "=== 调试主体数据创建过程 ===\n\n";

try {
    // 1. 检查数据库中的主体数据
    echo "1. 检查数据库中的主体数据...\n";
    
    $totalEntities = Entity::count();
    $recentEntities = Entity::orderBy('created_at', 'desc')->limit(10)->get();
    
    echo "总主体数量: {$totalEntities}\n";
    echo "最近10个主体:\n";
    
    if ($recentEntities->isEmpty()) {
        echo "  无主体记录\n";
    } else {
        foreach ($recentEntities as $entity) {
            echo "  ID: {$entity->id}, 名称: '{$entity->name}', 类型: '{$entity->entity_type}', 创建时间: {$entity->created_at}\n";
        }
    }
    
    // 2. 检查最近的导入任务
    echo "\n2. 检查最近的导入任务...\n";
    
    $recentTasks = AssetImportTask::orderBy('created_at', 'desc')->limit(5)->get();
    
    foreach ($recentTasks as $task) {
        echo "任务ID: {$task->id}, 状态: {$task->status}, 文件: {$task->original_filename}\n";
        echo "  总行数: {$task->total_rows}, 成功: {$task->success_rows}, 失败: {$task->failed_rows}\n";
    }
    
    // 3. 测试手动创建主体
    echo "\n3. 测试手动创建主体...\n";
    
    $testEntityData = [
        'name' => '测试主体_' . date('YmdHis'),
        'entity_type' => 'manufacturer',
        'address' => '测试地址',
        'phone' => '13800138000',
        'created_by' => 1,
        'updated_by' => 1,
        'created_at' => time(),
        'updated_at' => time(),
    ];
    
    echo "尝试创建测试主体...\n";
    echo "数据: " . json_encode($testEntityData, JSON_UNESCAPED_UNICODE) . "\n";
    
    try {
        $testEntity = Entity::create($testEntityData);
        echo "✓ 主体创建成功，ID: {$testEntity->id}\n";
        
        // 验证创建的主体
        $verifyEntity = Entity::find($testEntity->id);
        if ($verifyEntity) {
            echo "✓ 主体验证成功: {$verifyEntity->name}\n";
        } else {
            echo "✗ 主体验证失败\n";
        }
        
    } catch (Exception $e) {
        echo "✗ 主体创建失败: " . $e->getMessage() . "\n";
        echo "错误位置: " . $e->getFile() . ":" . $e->getLine() . "\n";
    }
    
    // 4. 检查导入服务中的主体数据准备
    echo "\n4. 检查导入服务中的主体数据准备...\n";
    
    // 模拟新模板数据
    $mockRowData = [
        '资产名称' => '测试资产',
        '生产厂商名称' => '测试生产厂商',
        '生产厂商联系人' => '张三',
        '生产厂商联系电话' => '13800138001',
        '生产厂商职位' => '技术经理',
        '供应商名称' => '测试供应商',
        '供应商联系人' => '李四',
        '供应商联系电话' => '13800138002',
        '供应商职位' => '销售经理',
    ];
    
    echo "模拟数据: " . json_encode($mockRowData, JSON_UNESCAPED_UNICODE) . "\n";
    
    // 使用反射调用私有方法
    $service = new \App\Services\AssetImportService();
    $reflection = new ReflectionClass($service);
    
    // 设置模板类型
    $templateTypeProperty = $reflection->getProperty('templateType');
    $templateTypeProperty->setAccessible(true);
    $templateTypeProperty->setValue($service, 'new_template');
    
    // 调用准备多个主体数据的方法
    $prepareMethod = $reflection->getMethod('prepareMultipleEntities');
    $prepareMethod->setAccessible(true);
    
    $entities = [];
    $contacts = [];
    
    try {
        $prepareMethod->invoke($service, $mockRowData, $entities, $contacts, 1);
        
        echo "准备的主体数据:\n";
        foreach ($entities as $key => $entityData) {
            echo "  键: {$key}\n";
            echo "  数据: " . json_encode($entityData, JSON_UNESCAPED_UNICODE) . "\n";
        }
        
        echo "准备的联系人数据:\n";
        foreach ($contacts as $contactData) {
            echo "  数据: " . json_encode($contactData, JSON_UNESCAPED_UNICODE) . "\n";
        }
        
    } catch (Exception $e) {
        echo "✗ 数据准备失败: " . $e->getMessage() . "\n";
        echo "错误位置: " . $e->getFile() . ":" . $e->getLine() . "\n";
    }
    
    // 5. 测试批量插入主体
    echo "\n5. 测试批量插入主体...\n";
    
    if (!empty($entities)) {
        try {
            $batchInsertMethod = $reflection->getMethod('batchInsertEntities');
            $batchInsertMethod->setAccessible(true);
            
            echo "执行批量插入...\n";
            $batchInsertMethod->invoke($service, $entities);
            
            // 检查processedEntities
            $processedEntitiesProperty = $reflection->getProperty('processedEntities');
            $processedEntitiesProperty->setAccessible(true);
            $processedEntities = $processedEntitiesProperty->getValue($service);
            
            echo "已处理的主体:\n";
            foreach ($processedEntities as $key => $entityId) {
                echo "  键: {$key}, ID: {$entityId}\n";
                
                // 验证主体是否真的存在
                $entity = Entity::find($entityId);
                if ($entity) {
                    echo "    ✓ 主体存在: {$entity->name}\n";
                } else {
                    echo "    ✗ 主体不存在\n";
                }
            }
            
        } catch (Exception $e) {
            echo "✗ 批量插入失败: " . $e->getMessage() . "\n";
            echo "错误位置: " . $e->getFile() . ":" . $e->getLine() . "\n";
        }
    }
    
    // 6. 检查数据库表结构
    echo "\n6. 检查数据库表结构...\n";
    
    try {
        $columns = \DB::select("DESCRIBE entities");
        echo "entities表结构:\n";
        foreach ($columns as $column) {
            echo "  {$column->Field}: {$column->Type} " . 
                 ($column->Null === 'YES' ? 'NULL' : 'NOT NULL') . 
                 ($column->Default ? " DEFAULT {$column->Default}" : '') . "\n";
        }
    } catch (Exception $e) {
        echo "✗ 获取表结构失败: " . $e->getMessage() . "\n";
    }
    
    // 7. 检查最新的资产记录
    echo "\n7. 检查最新的资产记录...\n";
    
    $recentAssets = Asset::orderBy('created_at', 'desc')->limit(3)->get();
    
    foreach ($recentAssets as $asset) {
        echo "资产ID: {$asset->id}, 名称: {$asset->name}\n";
        
        $relatedEntities = is_string($asset->related_entities) 
            ? json_decode($asset->related_entities, true) 
            : $asset->related_entities;
            
        if (!empty($relatedEntities)) {
            echo "  相关主体:\n";
            foreach ($relatedEntities as $entity) {
                if (isset($entity['entity_id'])) {
                    $dbEntity = Entity::find($entity['entity_id']);
                    if ($dbEntity) {
                        echo "    ✓ 主体ID {$entity['entity_id']}: {$dbEntity->name}\n";
                    } else {
                        echo "    ✗ 主体ID {$entity['entity_id']}: 不存在于数据库\n";
                    }
                }
            }
        } else {
            echo "  无相关主体数据\n";
        }
    }
    
    echo "\n=== 调试完成 ===\n";
    
} catch (Exception $e) {
    echo "✗ 调试失败：" . $e->getMessage() . "\n";
    echo "错误位置：" . $e->getFile() . ":" . $e->getLine() . "\n";
}

echo "\n=== 调试结束 ===\n";
