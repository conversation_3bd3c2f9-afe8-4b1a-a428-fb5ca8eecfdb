<?php

/**
 * 调试模板字段名称
 */

require_once __DIR__ . '/vendor/autoload.php';

$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "=== 调试模板字段名称 ===\n\n";

try {
    $filePath = 'storage/app/public/大数据资产导入模板_2025-08-14_06-26-47.xlsx';
    $fullPath = __DIR__ . '/' . $filePath;
    
    class DebugReader
    {
        // 空的读取器类
    }
    
    $data = \Maatwebsite\Excel\Facades\Excel::toArray(new DebugReader(), $fullPath);
    
    if (!empty($data) && !empty($data[0])) {
        $sheet = $data[0];
        
        // 找到标题行
        $headerRowIndex = -1;
        for ($i = 0; $i < count($sheet); $i++) {
            if (in_array('资产名称', $sheet[$i])) {
                $headerRowIndex = $i;
                break;
            }
        }
        
        if ($headerRowIndex !== -1) {
            $headers = $sheet[$headerRowIndex];
            echo "找到标题行（第" . ($headerRowIndex + 1) . "行）：\n";
            
            foreach ($headers as $index => $header) {
                if (!empty($header)) {
                    echo sprintf("%2d. %s\n", $index + 1, $header);
                }
            }
            
            // 检查主体相关字段
            echo "\n=== 主体相关字段检查 ===\n";
            
            $entityFields = [
                '生产厂商名称', '生产厂商联系人', '生产厂商联系电话', '生产厂商职位',
                '供应商名称', '供应商联系人', '供应商联系电话', '供应商职位',
                '服务商名称', '服务商联系人', '服务商联系电话', '服务商职位',
                '售后部名称', '售后部联系人', '售后部联系电话', '售后部职位',
            ];
            
            foreach ($entityFields as $field) {
                if (in_array($field, $headers)) {
                    echo "✓ 找到字段: {$field}\n";
                } else {
                    echo "✗ 缺少字段: {$field}\n";
                }
            }
            
            // 显示第一行数据作为示例
            if (count($sheet) > $headerRowIndex + 1) {
                echo "\n=== 第一行数据示例 ===\n";
                $firstDataRow = $sheet[$headerRowIndex + 1];
                
                foreach ($headers as $index => $header) {
                    if (!empty($header) && isset($firstDataRow[$index]) && !empty($firstDataRow[$index])) {
                        echo "{$header}: {$firstDataRow[$index]}\n";
                    }
                }
            }
            
        } else {
            echo "未找到标题行\n";
        }
    }
    
} catch (Exception $e) {
    echo "调试失败: " . $e->getMessage() . "\n";
}

echo "\n=== 调试结束 ===\n";
