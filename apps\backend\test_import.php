<?php

/**
 * 资产导入功能测试脚本
 * 
 * 使用方法：
 * php test_import.php
 */

require_once __DIR__ . '/vendor/autoload.php';

use Illuminate\Foundation\Application;

// 启动Laravel应用
$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

// 测试导入任务创建
echo "=== 资产导入功能测试 ===\n\n";

try {
    // 1. 测试模型创建
    echo "1. 测试AssetImportTask模型...\n";
    
    $task = new \App\Models\AssetImportTask([
        'file_path' => 'test/sample.xlsx',
        'original_filename' => 'sample.xlsx',
        'status' => 'pending',
        'created_by' => 1,
    ]);
    
    echo "   ✓ AssetImportTask模型创建成功\n";
    
    // 2. 测试服务类
    echo "2. 测试AssetImportService服务...\n";
    
    $service = new \App\Services\AssetImportService();
    echo "   ✓ AssetImportService服务创建成功\n";
    
    // 3. 测试Job类
    echo "3. 测试ProcessAssetImport Job...\n";
    
    // 创建一个临时任务用于测试
    $testTask = new \App\Models\AssetImportTask();
    $testTask->id = 999;
    $testTask->file_path = 'test/sample.xlsx';
    $testTask->original_filename = 'sample.xlsx';
    $testTask->status = 'pending';
    
    $job = new \App\Jobs\ProcessAssetImport($testTask);
    echo "   ✓ ProcessAssetImport Job创建成功\n";
    
    // 4. 测试请求验证类
    echo "4. 测试AssetImportRequest验证...\n";
    
    $request = new \App\Http\Requests\AssetImportRequest();
    $rules = $request->rules();
    
    if (isset($rules['file']) && is_array($rules['file'])) {
        echo "   ✓ AssetImportRequest验证规则正确\n";
    } else {
        echo "   ✗ AssetImportRequest验证规则有误\n";
    }
    
    // 5. 测试命令类
    echo "5. 测试ProcessAssetImportQueue命令...\n";
    
    $command = new \App\Console\Commands\ProcessAssetImportQueue();
    echo "   ✓ ProcessAssetImportQueue命令创建成功\n";
    
    echo "\n=== 所有组件测试通过 ===\n";
    echo "\n接下来的步骤：\n";
    echo "1. 运行数据库迁移：php artisan migrate\n";
    echo "2. 启动队列处理器：php artisan asset:process-import-queue\n";
    echo "3. 测试API接口：POST /api/admin/assets/import\n";
    echo "4. 查看导入状态：GET /api/admin/assets/import-tasks\n";
    
} catch (Exception $e) {
    echo "✗ 测试失败：" . $e->getMessage() . "\n";
    echo "错误位置：" . $e->getFile() . ":" . $e->getLine() . "\n";
}

echo "\n=== 测试完成 ===\n";
