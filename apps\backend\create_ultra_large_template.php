<?php

/**
 * 生成超大数据量Excel模板（10000行数据）
 * 使用分批写入避免内存问题
 */

require_once __DIR__ . '/vendor/autoload.php';

use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Font;
use PhpOffice\PhpSpreadsheet\Style\Fill;

echo "=== 生成超大数据量Excel模板（10000行） ===\n\n";

// 设置内存和时间限制
ini_set('memory_limit', '1024M');
ini_set('max_execution_time', 600);

try {
    // 创建新的电子表格
    $spreadsheet = new Spreadsheet();
    $sheet = $spreadsheet->getActiveSheet();
    $sheet->setTitle('超大数据资产导入');

    // 设置标题行
    $sheet->setCellValue('A1', '资产导入模板（超大数据测试版 - 10000行）');
    $sheet->mergeCells('A1:AI1');
    
    // 设置标题样式
    $titleStyle = [
        'font' => ['bold' => true, 'size' => 16, 'color' => ['rgb' => 'FFFFFF']],
        'fill' => ['fillType' => Fill::FILL_SOLID, 'startColor' => ['rgb' => '4472C4']],
        'alignment' => ['horizontal' => Alignment::HORIZONTAL_CENTER]
    ];
    $sheet->getStyle('A1:AI1')->applyFromArray($titleStyle);

    // 定义字段
    $headers = [
        '资产名称', '品牌', '规格型号', '序列号', '资产来源', '资产状态', '成色', '主设备', '所在地区', '详细地址',
        '启用日期', '合同质保期(月)', '质保期预警(天)', '维护周期(天)', '预计使用年限(年)',
        '生产厂商名称', '生产厂商联系人', '生产厂商联系电话', '生产厂商职位',
        '供应商名称', '供应商联系人', '供应商联系电话', '供应商职位',
        '服务商名称', '服务商联系人', '服务商联系电话', '服务商职位',
        '售后部名称', '售后部联系人', '售后部联系电话', '售后部职位',
        '备注', '医疗分类', '科室', '行业分类'
    ];

    // 写入字段行
    foreach ($headers as $index => $header) {
        $sheet->setCellValueByColumnAndRow($index + 1, 2, $header);
    }

    // 设置字段行样式
    $headerStyle = [
        'font' => ['bold' => true, 'color' => ['rgb' => 'FFFFFF']],
        'fill' => ['fillType' => Fill::FILL_SOLID, 'startColor' => ['rgb' => '70AD47']],
    ];
    $sheet->getStyle('A2:AI2')->applyFromArray($headerStyle);

    // 数据模板
    $templates = [
        'assetTypes' => ['办公电脑', '激光打印机', '服务器', '交换机', '路由器', '投影仪', '复印机', '扫描仪', '监控设备', 'UPS电源'],
        'brands' => ['联想', '戴尔', '惠普', '华为', '思科', '海康威视', '大华', '锐捷', '华三', '新华三'],
        'models' => ['M720', 'R740', 'Pro404', 'C2960', 'AR3200', 'EB-X41', 'WC3335', 'DS-7608', 'C1000', 'RT-3200'],
        'sources' => ['采购', '租赁', '捐赠', '调拨'],
        'statuses' => ['在用', '闲置', '维修', '报废'],
        'conditions' => ['全新', '九成新', '八成新', '七成新'],
        'regions' => ['北京', '上海', '深圳', '广州', '杭州', '成都', '武汉', '西安'],
        'companies' => ['科技有限公司', '信息技术有限公司', '网络科技有限公司', '电子设备有限公司'],
        'contacts' => ['张经理', '李工程师', '王主管', '赵专员', '钱总监', '孙技师'],
        'positions' => ['技术经理', '销售经理', '客服专员', '维修工程师', '项目经理', '技术支持'],
        'medicalCategories' => ['诊断设备', '治疗设备', '监护设备', '检验设备', '办公设备', 'IT设备'],
        'departments' => ['内科', '外科', '儿科', '急诊科', '放射科', '检验科', '信息科', '行政部'],
        'industryCategories' => ['医疗器械', 'IT设备', '办公设备', '网络设备', '安防设备', '实验设备']
    ];

    $totalRows = 10000;
    $batchSize = 500; // 每批处理500行
    $batches = ceil($totalRows / $batchSize);

    echo "总行数: {$totalRows}\n";
    echo "批次大小: {$batchSize}\n";
    echo "总批次数: {$batches}\n\n";

    for ($batch = 0; $batch < $batches; $batch++) {
        $startRow = $batch * $batchSize + 1;
        $endRow = min(($batch + 1) * $batchSize, $totalRows);
        $currentBatchSize = $endRow - $startRow + 1;
        
        echo "处理批次 " . ($batch + 1) . "/{$batches}：第 {$startRow}-{$endRow} 行 ({$currentBatchSize} 行)...\n";

        for ($i = $startRow; $i <= $endRow; $i++) {
            $excelRow = $i + 2; // Excel行号（第1行标题，第2行字段）
            
            // 随机生成数据
            $assetType = $templates['assetTypes'][array_rand($templates['assetTypes'])];
            $brand = $templates['brands'][array_rand($templates['brands'])];
            $model = $templates['models'][array_rand($templates['models'])];
            $region = $templates['regions'][array_rand($templates['regions'])];
            $company = $templates['companies'][array_rand($templates['companies'])];
            $contact = $templates['contacts'][array_rand($templates['contacts'])];
            $position = $templates['positions'][array_rand($templates['positions'])];
            
            $data = [
                $assetType . sprintf('%05d', $i),                    // 资产名称
                $brand,                                               // 品牌
                $model . '-' . sprintf('%03d', rand(100, 999)),     // 规格型号
                strtoupper(substr($brand, 0, 2)) . sprintf('%010d', $i), // 序列号
                $templates['sources'][array_rand($templates['sources'])], // 资产来源
                $templates['statuses'][array_rand($templates['statuses'])], // 资产状态
                $templates['conditions'][array_rand($templates['conditions'])], // 成色
                $i > 1 ? $assetType . sprintf('%05d', rand(1, $i-1)) : '', // 主设备
                $region . '市',                                      // 所在地区
                $region . '市XX区XX街道' . $i . '号',                // 详细地址
                '2024-' . sprintf('%02d', rand(1, 12)) . '-' . sprintf('%02d', rand(1, 28)), // 启用日期
                rand(12, 60),                                        // 合同质保期(月)
                rand(15, 90),                                        // 质保期预警(天)
                rand(30, 180),                                       // 维护周期(天)
                rand(3, 15),                                         // 预计使用年限(年)
                $region . $company . '制造部',                       // 生产厂商名称
                $contact . '(制造)',                                 // 生产厂商联系人
                '138' . sprintf('%08d', rand(10000000, 99999999)),   // 生产厂商联系电话
                $position,                                           // 生产厂商职位
                $region . $company . '销售部',                       // 供应商名称
                $contact . '(销售)',                                 // 供应商联系人
                '139' . sprintf('%08d', rand(10000000, 99999999)),   // 供应商联系电话
                $position,                                           // 供应商职位
                $region . $company . '服务部',                       // 服务商名称
                $contact . '(服务)',                                 // 服务商联系人
                '137' . sprintf('%08d', rand(10000000, 99999999)),   // 服务商联系电话
                $position,                                           // 服务商职位
                $region . $company . '售后部',                       // 售后部名称
                $contact . '(售后)',                                 // 售后部联系人
                '136' . sprintf('%08d', rand(10000000, 99999999)),   // 售后部联系电话
                $position,                                           // 售后部职位
                '超大数据测试第' . $i . '行，用于性能压力测试',        // 备注
                $templates['medicalCategories'][array_rand($templates['medicalCategories'])], // 医疗分类
                $templates['departments'][array_rand($templates['departments'])], // 科室
                $templates['industryCategories'][array_rand($templates['industryCategories'])], // 行业分类
            ];
            
            // 写入数据
            foreach ($data as $colIndex => $value) {
                $sheet->setCellValueByColumnAndRow($colIndex + 1, $excelRow, $value);
            }
        }
        
        // 每批次后清理内存
        if ($batch % 5 === 0) {
            gc_collect_cycles();
        }
    }

    // 设置基本列宽
    for ($col = 1; $col <= 10; $col++) {
        $sheet->getColumnDimensionByColumn($col)->setWidth(15);
    }

    // 保存文件
    $filename = 'storage/app/public/超大数据资产导入模板_' . date('Y-m-d_H-i-s') . '.xlsx';
    
    echo "\n正在保存Excel文件...\n";
    $writer = new Xlsx($spreadsheet);
    $writer->save($filename);

    $fileSize = round(filesize($filename) / 1024 / 1024, 2);

    echo "\n=== 超大数据Excel模板生成完成 ===\n";
    echo "文件路径: {$filename}\n";
    echo "数据行数: {$totalRows} 行\n";
    echo "总字段数: " . count($headers) . " 个\n";
    echo "文件大小: {$fileSize} MB\n";

    echo "\n极限性能测试数据：\n";
    echo "- 预计生成 " . ($totalRows * 4) . " 个主体记录\n";
    echo "- 预计生成 " . ($totalRows * 4) . " 个联系人记录\n";
    echo "- 预计批次数: " . ceil($totalRows / 1000) . " 个批次\n";
    echo "- 预计处理时间: 约 " . ceil($totalRows / 50) . " 分钟\n";

    echo "\n压力测试建议：\n";
    echo "1. 服务器内存: >= 2GB\n";
    echo "2. PHP内存限制: memory_limit >= 2048M\n";
    echo "3. 执行时间限制: max_execution_time >= 1800s\n";
    echo "4. 数据库连接池: 适当增加连接数\n";
    echo "5. 监控磁盘空间和I/O性能\n";

    echo "\n文件生成完成！可用于极限性能测试。\n";

} catch (Exception $e) {
    echo "✗ 生成失败：" . $e->getMessage() . "\n";
    echo "错误位置：" . $e->getFile() . ":" . $e->getLine() . "\n";
}

echo "\n=== 生成结束 ===\n";
