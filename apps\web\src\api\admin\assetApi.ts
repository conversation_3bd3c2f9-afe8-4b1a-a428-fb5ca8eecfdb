/**
 * 资产管理API
 */

import request from '@/utils/http'
import type {
  Asset,
  AssetQueryParams,
  AssetFormData,
  MainAssetQueryParams
} from '@/types/api/asset'
import type { PaginatedResponse } from '@/types/api/pagination'

/**
 * 获取资产列表
 */
export const getAssetList = (params?: AssetQueryParams): Promise<PaginatedResponse<Asset>> => {
  return request.get<PaginatedResponse<Asset>>({
    url: '/admin/assets',
    params
  })
}

/**
 * 获取可作为主设备的资产列表
 */
export const getMainAssetList = (
  params?: MainAssetQueryParams
): Promise<PaginatedResponse<Asset>> => {
  return request.get<PaginatedResponse<Asset>>({
    url: '/admin/assets/main-assets',
    params
  })
}

/**
 * 获取资产详情
 */
export const getAssetDetail = (id: number): Promise<Asset> => {
  return request.get<Asset>({
    url: `/admin/assets/${id}`
  })
}

/**
 * 创建资产
 */
export const createAsset = (data: AssetFormData): Promise<Asset> => {
  return request.post<Asset>({
    url: '/admin/assets',
    data
  })
}

/**
 * 更新资产
 */
export const updateAsset = (id: number, data: AssetFormData): Promise<Asset> => {
  return request.put<Asset>({
    url: `/admin/assets/${id}`,
    data
  })
}

/**
 * 删除资产
 */
export const deleteAsset = (id: number): Promise<void> => {
  return request.del<void>({
    url: `/admin/assets/${id}`
  })
}
