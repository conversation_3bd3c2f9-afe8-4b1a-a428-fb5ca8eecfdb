<?php

/**
 * 调试相关主体数据为空的问题
 */

require_once __DIR__ . '/vendor/autoload.php';

use Illuminate\Foundation\Application;
use App\Models\Asset;
use App\Models\Entity;
use App\Models\EntityContact;
use App\Services\AssetImportService;

// 启动Laravel应用
$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "=== 调试相关主体数据为空的问题 ===\n\n";

try {
    // 1. 检查最新的资产记录
    echo "1. 检查最新的资产记录...\n";

    $recentAssets = Asset::orderBy('created_at', 'desc')->limit(5)->get();

    if ($recentAssets->isEmpty()) {
        echo "未找到资产记录\n";
        exit(1);
    }

    echo "找到 " . $recentAssets->count() . " 个最新资产\n\n";

    foreach ($recentAssets as $index => $asset) {
        echo "资产 " . ($index + 1) . ":\n";
        echo "  ID: {$asset->id}\n";
        echo "  名称: {$asset->name}\n";
        echo "  品牌: {$asset->brand}\n";
        echo "  创建时间: {$asset->created_at}\n";

        // 检查相关主体数据
        echo "  related_entities 原始值: ";
        if (is_null($asset->related_entities)) {
            echo "NULL\n";
        } elseif (empty($asset->related_entities)) {
            echo "空字符串\n";
        } else {
            echo "'{$asset->related_entities}'\n";
        }

        echo "\n";
    }

    // 2. 检查数据库中的主体数据
    echo "2. 检查数据库中的主体数据...\n";

    $totalEntities = Entity::count();
    echo "总主体数量: {$totalEntities}\n";

    if ($totalEntities > 0) {
        $recentEntities = Entity::orderBy('created_at', 'desc')->limit(10)->get();
        echo "最近10个主体:\n";

        foreach ($recentEntities as $entity) {
            echo "  ID: {$entity->id}, 名称: '{$entity->name}', 类型: '{$entity->entity_type}'\n";
        }
    } else {
        echo "数据库中没有主体记录！\n";
    }

    // 3. 模拟导入过程
    echo "\n3. 模拟导入过程...\n";

    // 创建AssetImportService实例
    $service = new AssetImportService();
    $reflection = new ReflectionClass($service);

    // 设置模板类型为新模板
    $templateTypeProperty = $reflection->getProperty('templateType');
    $templateTypeProperty->setAccessible(true);
    $templateTypeProperty->setValue($service, 'new_template');

    // 模拟行数据
    $mockRowData = [
        '资产名称' => '测试资产_' . date('His'),
        '品牌' => '测试品牌',
        '规格型号' => 'TEST-001',
        '序列号' => 'SN' . date('His'),
        '生产厂商名称' => '测试生产厂商_' . date('His'),
        '生产厂商联系人' => '张三',
        '生产厂商联系电话' => '13800138001',
        '生产厂商职位' => '技术经理',
        '供应商名称' => '测试供应商_' . date('His'),
        '供应商联系人' => '李四',
        '供应商联系电话' => '13800138002',
        '供应商职位' => '销售经理',
    ];

    echo "模拟行数据:\n";
    foreach ($mockRowData as $key => $value) {
        echo "  {$key}: {$value}\n";
    }

    // 4. 测试主体数据准备
    echo "\n4. 测试主体数据准备...\n";

    $entities = [];
    $contacts = [];

    $prepareMethod = $reflection->getMethod('prepareMultipleEntities');
    $prepareMethod->setAccessible(true);

    try {
        $prepareMethod->invokeArgs($service, [$mockRowData, &$entities, &$contacts, 1]);

        echo "准备的主体数据:\n";
        foreach ($entities as $key => $entityData) {
            echo "  键: {$key}\n";
            echo "  数据: " . json_encode($entityData, JSON_UNESCAPED_UNICODE) . "\n";
        }

        echo "准备的联系人数据:\n";
        foreach ($contacts as $contactData) {
            echo "  数据: " . json_encode($contactData, JSON_UNESCAPED_UNICODE) . "\n";
        }

    } catch (Exception $e) {
        echo "✗ 主体数据准备失败: " . $e->getMessage() . "\n";
        return;
    }

    // 5. 测试主体插入
    echo "\n5. 测试主体插入...\n";

    if (!empty($entities)) {
        $batchInsertMethod = $reflection->getMethod('batchInsertEntities');
        $batchInsertMethod->setAccessible(true);

        try {
            $batchInsertMethod->invoke($service, $entities);

            // 获取已处理的主体
            $processedEntitiesProperty = $reflection->getProperty('processedEntities');
            $processedEntitiesProperty->setAccessible(true);
            $processedEntities = $processedEntitiesProperty->getValue($service);

            echo "已处理的主体:\n";
            foreach ($processedEntities as $key => $entityId) {
                echo "  键: {$key}, ID: {$entityId}\n";

                // 验证主体是否存在
                $entity = Entity::find($entityId);
                if ($entity) {
                    echo "    ✓ 主体存在: {$entity->name} (类型: {$entity->entity_type})\n";
                } else {
                    echo "    ✗ 主体不存在\n";
                }
            }

        } catch (Exception $e) {
            echo "✗ 主体插入失败: " . $e->getMessage() . "\n";
            return;
        }
    }

    // 6. 测试资产数据准备
    echo "\n6. 测试资产数据准备...\n";

    $prepareAssetMethod = $reflection->getMethod('prepareAssetData');
    $prepareAssetMethod->setAccessible(true);

    try {
        $assetData = $prepareAssetMethod->invoke($service, $mockRowData, 1);

        echo "准备的资产数据:\n";
        foreach ($assetData as $key => $value) {
            if ($key === 'asset_category_ids' || $key === 'related_entities') {
                echo "  {$key}: " . (is_string($value) ? $value : json_encode($value, JSON_UNESCAPED_UNICODE)) . "\n";
            } else {
                echo "  {$key}: {$value}\n";
            }
        }

    } catch (Exception $e) {
        echo "✗ 资产数据准备失败: " . $e->getMessage() . "\n";
        return;
    }

    // 7. 测试相关主体数据构建
    echo "\n7. 测试相关主体数据构建...\n";

    $buildRelatedMethod = $reflection->getMethod('buildRelatedEntitiesData');
    $buildRelatedMethod->setAccessible(true);

    try {
        $relatedEntities = $buildRelatedMethod->invoke($service, $mockRowData, 1);

        echo "构建的相关主体数据:\n";
        if (empty($relatedEntities)) {
            echo "  无相关主体数据\n";
        } else {
            foreach ($relatedEntities as $index => $entityData) {
                echo "  主体 " . ($index + 1) . ": " . json_encode($entityData, JSON_UNESCAPED_UNICODE) . "\n";
            }
        }

        echo "JSON格式: " . json_encode($relatedEntities, JSON_UNESCAPED_UNICODE) . "\n";

    } catch (Exception $e) {
        echo "✗ 相关主体数据构建失败: " . $e->getMessage() . "\n";
        echo "错误位置: " . $e->getFile() . ":" . $e->getLine() . "\n";
    }

    // 8. 测试完整的资产插入过程
    echo "\n8. 测试完整的资产插入过程...\n";

    if (isset($assetData)) {
        $assets = [$assetData];

        $batchInsertAssetsMethod = $reflection->getMethod('batchInsertAssets');
        $batchInsertAssetsMethod->setAccessible(true);

        try {
            $batchInsertAssetsMethod->invoke($service, $assets);

            echo "✓ 资产插入成功\n";

            // 查找刚插入的资产
            $newAsset = Asset::where('name', $mockRowData['资产名称'])->first();
            if ($newAsset) {
                echo "✓ 找到新插入的资产: ID {$newAsset->id}\n";
                echo "  related_entities: ";
                if (is_null($newAsset->related_entities)) {
                    echo "NULL\n";
                } elseif (empty($newAsset->related_entities)) {
                    echo "空字符串\n";
                } else {
                    echo "'{$newAsset->related_entities}'\n";
                }
            } else {
                echo "✗ 未找到新插入的资产\n";
            }

        } catch (Exception $e) {
            echo "✗ 资产插入失败: " . $e->getMessage() . "\n";
            echo "错误位置: " . $e->getFile() . ":" . $e->getLine() . "\n";
        }
    }

    echo "\n=== 调试完成 ===\n";

} catch (Exception $e) {
    echo "✗ 调试失败：" . $e->getMessage() . "\n";
    echo "错误位置：" . $e->getFile() . ":" . $e->getLine() . "\n";
}

echo "\n=== 调试结束 ===\n";
