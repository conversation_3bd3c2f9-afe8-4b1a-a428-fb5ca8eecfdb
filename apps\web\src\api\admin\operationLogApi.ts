import type { OperationLog, OperationLogListData, OperationLogSearchParams } from '@/types/api'
import request from '@/utils/http'

/**
 * 获取操作日志列表
 */
export const getOperationLogs = (
  params: OperationLogSearchParams
): Promise<OperationLogListData> => {
  return request.get<OperationLogListData>({
    url: '/admin/operation-logs',
    params
  })
}

/**
 * 获取操作日志详情
 */
export const getOperationLogDetail = (id: number | string): Promise<OperationLog> => {
  return request.get<OperationLog>({
    url: `/admin/operation-logs/${id}`
  })
}
