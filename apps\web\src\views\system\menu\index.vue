<template>
  <div class="menu-page art-full-height">
    <!-- 搜索栏 -->
    <ArtSearchBar
      v-model:filter="formFilters"
      :items="formItems"
      :showExpand="false"
      @reset="handleReset"
      @search="handleSearch"
    ></ArtSearchBar>

    <ElCard class="art-table-card" shadow="never">
      <!-- 表格头部 -->
      <ArtTableHeader :showZebra="false" v-model:columns="columnChecks" @refresh="handleRefresh">
        <template #left>
          <!-- 添加菜单按钮 -->
          <ElButton v-auth="'add'" @click="showModel('menu', null, true)" v-ripple>
            添加菜单
          </ElButton>
          <ElButton @click="toggleExpand" v-ripple>
            {{ isExpanded ? '收起' : '展开' }}
          </ElButton>
        </template>
      </ArtTableHeader>
      <!-- 表格 -->
      <ArtTable
        ref="tableRef"
        :loading="loading"
        :data="filteredTableData"
        :tableConfig="{
          rowKey: 'path',
          stripe: false
        }"
        :layout="{
          marginTop: 10
        }"
      >
        <template #default>
          <ElTableColumn v-for="col in columns" :key="col.prop || col.type" v-bind="col" />
        </template>
      </ArtTable>

      <ElDialog :title="dialogTitle" v-model="dialogVisible" width="700px" align-center>
        <ElForm ref="formRef" :model="form" :rules="rules" label-width="85px">
          <!-- 仅在编辑时显示菜单类型（禁用状态） -->
          <ElFormItem label="菜单类型" v-if="isEdit">
            <ElRadioGroup v-model="labelPosition" :disabled="true">
              <ElRadioButton value="menu" label="menu">菜单</ElRadioButton>
              <ElRadioButton value="button" label="button">权限</ElRadioButton>
            </ElRadioGroup>
          </ElFormItem>

          <template v-if="labelPosition === 'menu'">
            <ElRow :gutter="20">
              <ElCol :span="24">
                <ElFormItem label="父级菜单" prop="parent_id">
                  <ElTreeSelect
                    v-model="form.parent_id"
                    :data="menuTreeOptions"
                    :props="{ label: 'title', value: 'id', children: 'children' }"
                    placeholder="请选择父级菜单（留空为顶级菜单）"
                    filterable
                    clearable
                    check-strictly
                    :render-after-expand="false"
                    style="width: 100%"
                  />
                </ElFormItem>
              </ElCol>
            </ElRow>
            <ElRow :gutter="20">
              <ElCol :span="12">
                <ElFormItem label="菜单名称" prop="name">
                  <ElInput v-model="form.name" placeholder="菜单名称"></ElInput>
                </ElFormItem>
              </ElCol>
              <ElCol :span="12">
                <ElFormItem label="路由地址" prop="path">
                  <ElInput v-model="form.path" placeholder="路由地址（由开发人员设置）" :disabled="true"></ElInput>
                </ElFormItem>
              </ElCol>
            </ElRow>
            <ElRow :gutter="20">
              <ElCol :span="12">
                <ElFormItem label="权限标识" prop="label">
                  <ElInput v-model="form.label" placeholder="权限标识（由开发人员设置）" :disabled="true"></ElInput>
                </ElFormItem>
              </ElCol>
              <ElCol :span="12">
                <ElFormItem label="图标" prop="icon">
                  <ArtIconSelector v-model="form.icon" :iconType="iconType" width="100%" />
                </ElFormItem>
              </ElCol>
            </ElRow>

            <ElRow :gutter="20">
              <ElCol :span="12">
                <ElFormItem label="菜单排序" prop="sort" style="width: 100%">
                  <ElInputNumber
                    v-model="form.sort"
                    style="width: 100%"
                    @change="handleChange"
                    :min="1"
                    controls-position="right"
                  />
                </ElFormItem>
              </ElCol>
              <ElCol :span="12">
                <ElFormItem label="外部链接" prop="link">
                  <ElInput
                    v-model="form.link"
                    placeholder="外部链接/内嵌地址(https://www.baidu.com)"
                  ></ElInput>
                </ElFormItem>
              </ElCol>
            </ElRow>

            <ElRow :gutter="20">
              <ElCol :span="6">
                <ElFormItem label="页面缓存" prop="keep_alive">
                  <ElSwitch v-model="form.keep_alive"></ElSwitch>
                </ElFormItem>
              </ElCol>
              <ElCol :span="6">
                <ElFormItem label="是否隐藏" prop="is_hidden">
                  <ElSwitch v-model="form.is_hidden"></ElSwitch>
                </ElFormItem>
              </ElCol>
              <ElCol :span="6">
                <ElFormItem label="是否内嵌" prop="is_iframe">
                  <ElSwitch v-model="form.is_iframe"></ElSwitch>
                </ElFormItem>
              </ElCol>
            </ElRow>
          </template>

          <template v-if="labelPosition === 'button'">
            <ElRow :gutter="20">
              <ElCol :span="12">
                <ElFormItem label="权限名称" prop="auth_name">
                  <ElInput v-model="form.auth_name" placeholder="权限名称"></ElInput>
                </ElFormItem>
              </ElCol>
              <ElCol :span="12">
                <ElFormItem label="权限标识" prop="auth_label">
                  <ElInput v-model="form.auth_label" placeholder="权限标识（由开发人员设置）" :disabled="true"></ElInput>
                </ElFormItem>
              </ElCol>
            </ElRow>
            <ElRow :gutter="20">
              <ElCol :span="12">
                <ElFormItem label="权限排序" prop="auth_sort" style="width: 100%">
                  <ElInputNumber
                    v-model="form.auth_sort"
                    style="width: 100%"
                    @change="handleChange"
                    :min="1"
                    controls-position="right"
                  />
                </ElFormItem>
              </ElCol>
            </ElRow>
          </template>
        </ElForm>

        <template #footer>
          <span class="dialog-footer">
            <ElButton @click="dialogVisible = false">取 消</ElButton>
            <ElButton type="primary" @click="submitForm()">确 定</ElButton>
          </span>
        </template>
      </ElDialog>
    </ElCard>
  </div>
</template>

<script setup lang="ts">
  import type { FormInstance, FormRules } from 'element-plus'
  import { ElMessageBox, ElTag, ElMessage, ElButton } from 'element-plus'
  import { IconTypeEnum } from '@/enums/appEnum'
  import { formatMenuTitle } from '@/router/utils/utils'
  import ArtButtonTable from '@/components/core/forms/art-button-table/index.vue'
  import { useTableColumns } from '@/composables/useTableColumns'
  import { useAuth } from '@/composables/useAuth'
  import { useMenu } from '@/composables/useMenu'
  import { SearchFormItem } from '@/types'
  import {
    getMenuList,
    createMenu,
    updateMenu,
    deleteMenu,
    type BackendMenuItem,
    type MenuForm,
    type MenuPermissionForm
  } from '@/api/admin/menuApi'
  const handleSuccess = (message: string) => {
    ElMessage.success(message)
  }

  defineOptions({ name: 'Menus' })

  const { hasAuth } = useAuth()
  const { getMenuType, getMenuTypeTagType } = useMenu()

  const loading = ref(false)

  // 定义表单搜索初始值
  const initialSearchState = {
    name: ''
  }

  // 响应式表单数据
  const formFilters = reactive({ ...initialSearchState })

  // 重置表单
  const handleReset = () => {
    Object.assign(formFilters, { ...initialSearchState })
  }

  // 搜索处理
  const handleSearch = () => {
    // 前端搜索，不需要调用接口
  }

  // 表单配置项
  const formItems: SearchFormItem[] = [
    {
      label: '菜单名称',
      prop: 'name',
      type: 'input',
      config: {
        clearable: true,
        placeholder: '请输入菜单名称'
      }
    }
  ]

  // 动态列配置
  const { columnChecks, columns } = useTableColumns(() => [
    {
      prop: 'meta.title',
      label: '菜单名称',
      minWidth: 120,
      formatter: (row: BackendMenuItem) => {
        return formatMenuTitle(row.meta?.title)
      }
    },
    {
      prop: 'type',
      label: '菜单类型',
      formatter: (row: BackendMenuItem) => {
        return h(ElTag, { type: getMenuTypeTagType(row) as any }, () => getMenuType(row))
      }
    },
    {
      prop: 'path',
      label: '路由',
      formatter: (row: BackendMenuItem) => {
        return row.meta?.link || row.path || ''
      }
    },
    {
      prop: 'meta.authList',
      label: '可操作权限',
      width: 200,
      formatter: (row: BackendMenuItem) => {
        const authList = row.meta?.authList || []

        // 调试信息：打印权限数据
        console.log('菜单权限数据:', {
          title: row.meta?.title,
          authList: authList,
          permissions: row.permissions,
          fullRow: row
        })

        const buttons = []

        // 显示所有权限按钮
        authList.forEach((item: { title: string; authMark: string }, index: number) => {
          // 权限按钮组
          const permissionGroup = h(
            'div',
            {
              style:
                'display: inline-flex; align-items: center; margin: 2px; border: 1px solid #ddd; border-radius: 4px; overflow: hidden;'
            },
            [
              // 权限名称按钮
              h(
                ElButton,
                {
                  size: 'small',
                  type: 'primary',
                  style: 'border-radius: 0; border: none; margin: 0;',
                  onClick: () => showModel('button', item, false, row),
                  title: `编辑权限: ${item.title}`,
                  key: `auth-${index}`
                },
                { default: () => item.title }
              ),
              // 删除按钮
              h(
                ElButton,
                {
                  size: 'small',
                  type: 'danger',
                  style: 'border-radius: 0; border: none; margin: 0; padding: 0 8px;',
                  onClick: () => handleDeleteAuth(row, item),
                  title: `删除权限: ${item.title}`,
                  key: `delete-${index}`
                },
                { default: () => '×' }
              )
            ]
          )

          buttons.push(permissionGroup)
        })

        // 添加“添加权限”按钮
        buttons.push(
          h(
            ElButton,
            {
              size: 'small',
              type: 'success',
              style: 'margin: 2px',
              onClick: () => showModel('button', null, false, row),
              key: 'add-auth'
            },
            { default: () => '+ 添加权限' }
          )
        )

        return h('div', { style: 'display: flex; flex-wrap: wrap; gap: 2px;' }, buttons)
      }
    },
    {
      prop: 'status',
      label: '菜单状态',
      formatter: (row) => {
        return h(ElTag, { type: row.meta.isHide ? 'danger' : 'success' }, () =>
          row.meta.isHide ? '隐藏' : '显示'
        )
      }
    },
    {
      prop: 'operation',
      label: '操作',
      width: 180,
      formatter: (row: BackendMenuItem) => {
        return h('div', [
          hasAuth('edit') &&
            h(ArtButtonTable, {
              type: 'edit',
              onClick: () => showDialog('edit', row)
            }),
          hasAuth('delete') &&
            h(ArtButtonTable, {
              type: 'delete',
              onClick: () => handleDeleteMenu(row)
            })
        ])
      }
    }
  ])

  const handleRefresh = () => {
    getTableData()
  }

  const dialogVisible = ref(false)
  const form = reactive({
    // 菜单
    parent_id: null,
    name: '',
    path: '',
    label: '',
    icon: '',
    sort: 1,
    is_menu: true,
    keep_alive: true,
    is_hidden: true,
    link: '',
    is_iframe: false,
    // 权限 (修改这部分)
    auth_name: '',
    auth_label: '',
    auth_icon: '',
    auth_sort: 1
  })
  const iconType = ref(IconTypeEnum.UNICODE)

  const labelPosition = ref('menu')
  const rules = reactive<FormRules>({
    name: [
      { required: true, message: '请输入菜单名称', trigger: 'blur' },
      { min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'blur' }
    ],
    // path和label不再必填，因为已禁用且后端会自动生成
    // 权限相关
    auth_name: [{ required: true, message: '请输入权限名称', trigger: 'blur' }]
    // auth_label不再必填，因为已禁用且后端会自动生成
  })

  const tableData = ref<BackendMenuItem[]>([])
  const menuTreeOptions = ref<BackendMenuItem[]>([])
  const currentEditingMenu = ref<BackendMenuItem | null>(null)

  onMounted(() => {
    getTableData()
    getMenuTreeData()
  })

  const getTableData = async () => {
    loading.value = true
    try {
      tableData.value = await getMenuList()
    } finally {
      loading.value = false
    }
  }

  const getMenuTreeData = async () => {
    // 复用 getMenuList API，数据更完整
    const treeData = await getMenuList()
    // 处理菜单树数据，为 TreeSelect 添加顶层 title 字段
    const processMenuTree = (menus: BackendMenuItem[], excludeId?: number): any[] => {
      return menus
        .filter((menu) => menu.id !== excludeId) // 过滤掉当前编辑的菜单
        .map((menu) => ({
          ...menu,
          title: menu.meta?.title || menu.name, // 添加顶层 title 字段
          children: menu.children
            ? processMenuTree(menu.children as BackendMenuItem[], excludeId)
            : []
        }))
    }

    // 如果正在编辑菜单，排除当前菜单避免循环引用
    const excludeId =
      isEdit.value && currentEditingMenu.value ? currentEditingMenu.value.id : undefined
    menuTreeOptions.value = processMenuTree(treeData, excludeId)
  }

  // 过滤后的表格数据
  const filteredTableData = computed(() => {
    // 深拷贝函数，避免修改原数据
    const deepClone = (obj: any): any => {
      if (obj === null || typeof obj !== 'object') return obj
      if (obj instanceof Date) return new Date(obj)
      if (Array.isArray(obj)) return obj.map((item) => deepClone(item))

      const cloned: any = {}
      for (const key in obj) {
        if (Object.prototype.hasOwnProperty.call(obj, key)) {
          cloned[key] = deepClone(obj[key])
        }
      }
      return cloned
    }

    // 递归搜索函数
    const searchMenu = (items: BackendMenuItem[]): BackendMenuItem[] => {
      const results: BackendMenuItem[] = []
      const searchKeyword = formFilters.name?.toLowerCase().trim() || ''

      for (const item of items) {
        // 获取菜单标题
        const menuTitle = formatMenuTitle(item.meta?.title || '').toLowerCase()

        // 如果有子菜单，递归搜索
        if (item.children && item.children.length > 0) {
          const matchedChildren = searchMenu(item.children as BackendMenuItem[])
          // 如果子菜单有匹配项，保留当前菜单并更新子菜单
          if (matchedChildren.length > 0) {
            const clonedItem = deepClone(item)
            clonedItem.children = matchedChildren
            results.push(clonedItem)
            continue
          }
        }

        // 当前菜单匹配条件则返回
        if (!searchKeyword || menuTitle.includes(searchKeyword)) {
          results.push(deepClone(item))
        }
      }

      return results
    }

    const filteredData = searchMenu(tableData.value)

    // 如果有搜索关键词，自动展开所有菜单
    if (formFilters.name?.trim()) {
      nextTick(() => {
        if (tableRef.value) {
          tableRef.value.expandAll()
        }
      })
    }

    return filteredData
  })

  const isEdit = ref(false)
  const formRef = ref<FormInstance>()
  const dialogTitle = computed(() => {
    const type = labelPosition.value === 'menu' ? '菜单' : '权限'
    return isEdit.value ? `编辑${type}` : `新建${type}`
  })

  const showDialog = (_type: string, row: BackendMenuItem) => {
    showModel('menu', row, true)
  }

  const handleChange = () => {}

  const submitForm = async () => {
    if (!formRef.value) return

    await formRef.value.validate(async (valid) => {
      if (valid) {
        try {
          loading.value = true

          if (labelPosition.value === 'menu') {
            // 处理菜单
            const menuData: MenuForm = {
              parent_id: form.parent_id,
              name: form.label,
              path: form.path,
              component: form.path ? `/${form.path}/index` : '/index/index',
              title: form.name,
              icon: form.icon,
              sort: form.sort,
              is_hide: form.is_hidden,
              link: form.link,
              is_iframe: form.is_iframe,
              keep_alive: form.keep_alive,
              status: true
            }

            if (isEdit.value && currentEditingMenu.value) {
              await updateMenu(currentEditingMenu.value.id!, menuData)
              handleSuccess('编辑成功')
            } else {
              await createMenu(menuData)
              handleSuccess('新增成功')
            }
          } else {
            // 处理权限按钮
            if (currentEditingMenu.value) {
              const permissions: MenuPermissionForm[] = [
                {
                  title: form.auth_name,
                  auth_mark: form.auth_label,
                  sort: form.auth_sort
                }
              ]

              const menuData: MenuForm = {
                parent_id: currentEditingMenu.value.parent_id,
                name: currentEditingMenu.value.name as string,
                path: currentEditingMenu.value.path,
                component: currentEditingMenu.value.component as string,
                title: currentEditingMenu.value.meta.title,
                icon: currentEditingMenu.value.meta.icon,
                sort: currentEditingMenu.value.sort,
                is_hide: currentEditingMenu.value.is_hide,
                link: currentEditingMenu.value.meta.link,
                is_iframe: currentEditingMenu.value.is_iframe,
                keep_alive: currentEditingMenu.value.keep_alive,
                status: currentEditingMenu.value.status,
                permissions
              }

              await updateMenu(currentEditingMenu.value.id!, menuData)
              handleSuccess('权限更新成功')
            }
          }

          dialogVisible.value = false
          await getTableData()
          await getMenuTreeData()
        } finally {
          loading.value = false
        }
      }
    })
  }

  const showModel = (type: string, row?: any, lock: boolean = false, menuRow?: BackendMenuItem) => {
    dialogVisible.value = true
    labelPosition.value = type
    isEdit.value = false
    lockMenuType.value = lock
    resetForm()

    if (row) {
      isEdit.value = true
      if (type === 'menu') {
        // 菜单数据
        currentEditingMenu.value = row
        nextTick(() => {
          // 菜单数据回显
          form.parent_id = row.parent_id
          form.name = formatMenuTitle(row.meta.title)
          form.path = row.path
          form.label = (row.name as string) || ''
          form.icon = row.meta.icon
          form.sort = row.sort || 1
          form.keep_alive = row.keep_alive !== false
          form.is_hidden = row.is_hide === true
          form.link = row.link || ''
          form.is_iframe = row.is_iframe === true
        })
      } else {
        // 权限按钮数据回显
        // row 是权限对象，需要找到它所属的菜单
        const findMenuByPermission = (
          menus: BackendMenuItem[],
          permission: any
        ): BackendMenuItem | null => {
          for (const menu of menus) {
            if (menu.permissions?.some((p) => p.auth_mark === permission.authMark)) {
              return menu
            }
            if (menu.children) {
              const found = findMenuByPermission(menu.children as BackendMenuItem[], permission)
              if (found) return found
            }
          }
          return null
        }

        currentEditingMenu.value = findMenuByPermission(tableData.value, row)

        nextTick(() => {
          form.auth_name = row.title
          form.auth_label = row.authMark
          form.auth_icon = row.icon || ''
          form.auth_sort = row.sort || 1
        })
      }
    } else {
      // 如果是添加权限按钮，设置当前编辑的菜单
      if (type === 'button' && menuRow) {
        currentEditingMenu.value = menuRow
      } else {
        currentEditingMenu.value = null
      }
    }

    // 重新加载菜单树数据，确保编辑时正确过滤
    nextTick(() => {
      getMenuTreeData()
    })
  }

  const resetForm = () => {
    formRef.value?.resetFields()
    Object.assign(form, {
      // 菜单
      parent_id: null,
      name: '',
      path: '',
      label: '',
      icon: '',
      sort: 1,
      is_menu: true,
      keep_alive: true,
      is_hidden: true,
      link: '',
      is_iframe: false,
      // 权限
      auth_name: '',
      auth_label: '',
      auth_icon: '',
      auth_sort: 1
    })
  }

  const handleDeleteMenu = async (row: BackendMenuItem) => {
    await ElMessageBox.confirm('确定要删除该菜单吗？删除后无法恢复', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    if (row.id) {
      await deleteMenu(row.id)
      handleSuccess('删除成功')
      await getTableData()
      await getMenuTreeData()
    }
  }

  const handleDeleteAuth = async (
    menuRow: BackendMenuItem,
    permission: { title: string; authMark: string }
  ) => {
    await ElMessageBox.confirm('确定要删除该权限吗？删除后无法恢复', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    if (menuRow.id && menuRow.permissions) {
      // 移除指定的权限
      const updatedPermissions = menuRow.permissions.filter(
        (p) => p.auth_mark !== permission.authMark
      )

      const menuData: MenuForm = {
        parent_id: menuRow.parent_id,
        name: (menuRow.name as string) || '',
        path: menuRow.path,
        component: menuRow.component as string,
        title: menuRow.meta.title,
        icon: menuRow.meta.icon,
        sort: menuRow.sort,
        is_hide: menuRow.is_hide,
        link: menuRow.meta.link,
        is_iframe: menuRow.is_iframe,
        keep_alive: menuRow.keep_alive,
        status: menuRow.status,
        permissions: updatedPermissions.map((p) => ({
          title: p.title,
          auth_mark: p.auth_mark,
          sort: p.sort
        }))
      }

      await updateMenu(menuRow.id, menuData)
      handleSuccess('删除权限成功')
      await getTableData()
    }
  }

  // 修改计算属性，增加锁定控制参数
  const disableMenuType = computed(() => {
    // 编辑权限时锁定为权限类型
    if (isEdit.value && labelPosition.value === 'button') return true
    // 编辑菜单时锁定为菜单类型
    if (isEdit.value && labelPosition.value === 'menu') return true
    // 顶部添加菜单按钮时锁定为菜单类型
    if (!isEdit.value && labelPosition.value === 'menu' && lockMenuType.value) return true
    return false
  })

  // 添加一个控制变量
  const lockMenuType = ref(false)

  const isExpanded = ref(false)
  const tableRef = ref()

  const toggleExpand = () => {
    isExpanded.value = !isExpanded.value
    nextTick(() => {
      if (tableRef.value) {
        tableRef.value[isExpanded.value ? 'expandAll' : 'collapseAll']()
      }
    })
  }
</script>

<style lang="scss" scoped>
  .menu-page {
    .svg-icon {
      width: 1.8em;
      height: 1.8em;
      overflow: hidden;
      vertical-align: -8px;
      fill: currentcolor;
    }

    :deep(.small-btn) {
      height: 30px !important;
      padding: 0 10px !important;
      font-size: 12px !important;
    }
  }
</style>
