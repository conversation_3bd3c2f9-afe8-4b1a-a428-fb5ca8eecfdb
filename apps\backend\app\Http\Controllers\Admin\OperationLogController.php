<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Resources\Admin\OperationLogResource;
use App\Models\OperationLog;
use Illuminate\Http\Request;

/**
 * @group 操作日志管理
 *
 * 系统操作日志查询接口
 */
class OperationLogController extends Controller
{
    /**
     * 获取操作日志列表
     *
     * @queryParam user_name string 用户名搜索 Example: admin
     * @queryParam ip string IP地址搜索 Example: ***********
     * @queryParam method string 请求方法筛选 Example: POST
     * @queryParam path string 请求路径搜索 Example: /api/users
     * @queryParam operation_type string 操作类型筛选 Example: create
     * @queryParam target_type string 目标类型筛选 Example: Asset
     * @queryParam menu_name string 菜单名称搜索 Example: 资产管理
     * @queryParam start_time string 开始时间 Example: 2024-01-01 00:00:00
     * @queryParam end_time string 结束时间 Example: 2024-12-31 23:59:59
     * @queryParam page int 页码 Example: 1
     * @queryParam per_page int 每页条数 Example: 20
     */
    public function index(Request $request)
    {
        $query = OperationLog::with(['user', 'menu']);

        // 用户名搜索
        if ($request->has('user_name')) {
            $query->where('user_name', 'like', '%' . $request->input('user_name') . '%');
        }

        // IP地址搜索
        if ($request->has('ip')) {
            $query->where('ip', 'like', '%' . $request->input('ip') . '%');
        }

        // 请求方法筛选
        if ($request->has('method')) {
            $query->where('method', $request->input('method'));
        }

        // 请求路径搜索
        if ($request->has('path')) {
            $query->where('path', 'like', '%' . $request->input('path') . '%');
        }

        // 操作类型筛选
        if ($request->has('operation_type')) {
            $query->where('operation_type', $request->input('operation_type'));
        }

        // 目标类型筛选
        if ($request->has('target_type')) {
            $query->where('target_type', $request->input('target_type'));
        }

        // 菜单名称搜索
        if ($request->has('menu_name')) {
            $query->where('menu_name', 'like', '%' . $request->input('menu_name') . '%');
        }

        // 时间范围筛选
        if ($request->has('start_time')) {
            $query->where('created_at', '>=', $request->input('start_time'));
        }
        if ($request->has('end_time')) {
            $query->where('created_at', '<=', $request->input('end_time'));
        }

        // 按时间倒序排序
        $query->orderByDesc('created_at');

        $logs = $query->paginate($request->input('per_page', 20));

        return OperationLogResource::collection($logs);
    }

    /**
     * 获取操作日志详情
     *
     * @urlParam id integer required 日志ID Example: 1
     */
    public function show(OperationLog $operationLog)
    {
        return new OperationLogResource($operationLog);
    }

    /**
     * 获取操作类型统计
     */
    public function operationTypeStats(Request $request)
    {
        $query = OperationLog::query();

        // 时间范围筛选
        if ($request->has('start_time')) {
            $query->where('created_at', '>=', $request->input('start_time'));
        }
        if ($request->has('end_time')) {
            $query->where('created_at', '<=', $request->input('end_time'));
        }

        $stats = $query->selectRaw('operation_type, COUNT(*) as count')
            ->whereNotNull('operation_type')
            ->groupBy('operation_type')
            ->orderByDesc('count')
            ->get();

        return response()->json([
            'data' => $stats
        ]);
    }

    /**
     * 获取菜单操作统计
     */
    public function menuStats(Request $request)
    {
        $query = OperationLog::query();

        // 时间范围筛选
        if ($request->has('start_time')) {
            $query->where('created_at', '>=', $request->input('start_time'));
        }
        if ($request->has('end_time')) {
            $query->where('created_at', '<=', $request->input('end_time'));
        }

        $stats = $query->selectRaw('menu_name, COUNT(*) as count')
            ->whereNotNull('menu_name')
            ->groupBy('menu_name')
            ->orderByDesc('count')
            ->limit(10)
            ->get();

        return response()->json([
            'data' => $stats
        ]);
    }
}
