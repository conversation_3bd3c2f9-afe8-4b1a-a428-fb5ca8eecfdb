<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * @property int $id
 * @property int $category_id 分类ID
 * @property string $code 字典编码
 * @property string $value 字典值
 * @property string|null $label 显示标签
 * @property int $sort 排序
 * @property string|null $color 颜色值
 * @property string|null $icon 图标
 * @property array<array-key, mixed>|null $config 扩展配置
 * @property string|null $remark 备注
 * @property bool $is_enabled 是否启用
 * @property int|null $created_by 创建人ID
 * @property int|null $updated_by 更新人ID
 * @property int|null $created_at 创建时间
 * @property int|null $updated_at 更新时间
 * @property \Illuminate\Support\Carbon|null $deleted_at 删除时间
 * @property-read \App\Models\DictionaryCategory $category
 * @property-read string $display_text
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DictionaryItem enabled()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DictionaryItem newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DictionaryItem newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DictionaryItem ofCategory($categoryId)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DictionaryItem ofCategoryCode($categoryCode)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DictionaryItem onlyTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DictionaryItem ordered()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DictionaryItem query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DictionaryItem whereCategoryId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DictionaryItem whereCode($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DictionaryItem whereColor($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DictionaryItem whereConfig($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DictionaryItem whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DictionaryItem whereCreatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DictionaryItem whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DictionaryItem whereIcon($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DictionaryItem whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DictionaryItem whereIsEnabled($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DictionaryItem whereLabel($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DictionaryItem whereRemark($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DictionaryItem whereSort($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DictionaryItem whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DictionaryItem whereUpdatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DictionaryItem whereValue($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DictionaryItem withTrashed(bool $withTrashed = true)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DictionaryItem withoutTrashed()
 * @mixin \Eloquent
 */
class DictionaryItem extends Model
{
    use HasFactory, SoftDeletes;

    // 设置时间格式为 Unix 时间戳
    protected $dateFormat = 'U';

    /**
     * 可批量赋值的属性
     */
    protected $fillable = [
        'category_id',
        'code',
        'value',
        'label',
        'sort',
        'color',
        'icon',
        'config',
        'remark',
        'is_enabled',
        'created_by',
        'updated_by',
    ];

    /**
     * 属性类型转换
     */
    protected $casts = [
        'is_enabled' => 'boolean',
        'sort' => 'integer',
        'config' => 'array',
        'created_at' => 'integer',
        'updated_at' => 'integer',
    ];

    /**
     * 隐藏的属性
     */
    protected $hidden = [
        'deleted_at',
    ];

    /**
     * 获取字典分类
     */
    public function category(): BelongsTo
    {
        return $this->belongsTo(DictionaryCategory::class, 'category_id');
    }

    /**
     * 获取显示文本
     */
    public function getDisplayTextAttribute(): string
    {
        return $this->label ?: $this->value;
    }

    /**
     * 作用域：启用状态
     */
    public function scopeEnabled($query)
    {
        return $query->where('is_enabled', true);
    }

    /**
     * 作用域：按排序字段排序
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort')->orderBy('id');
    }

    /**
     * 作用域：按分类查询
     */
    public function scopeOfCategory($query, $categoryId)
    {
        return $query->where('category_id', $categoryId);
    }

    /**
     * 作用域：按分类编码查询
     */
    public function scopeOfCategoryCode($query, $categoryCode)
    {
        return $query->whereHas('category', function ($q) use ($categoryCode) {
            $q->where('code', $categoryCode);
        });
    }
}
