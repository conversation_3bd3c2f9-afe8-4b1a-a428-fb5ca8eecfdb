<?php

/**
 * 测试资产分类数据库匹配功能
 */

require_once __DIR__ . '/vendor/autoload.php';

use Illuminate\Foundation\Application;
use App\Models\Category;
use App\Services\AssetImportService;

// 启动Laravel应用
$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "=== 测试资产分类数据库匹配功能 ===\n\n";

try {
    // 1. 检查数据库中的分类数据
    echo "1. 检查数据库中的分类数据...\n";
    
    $totalCategories = Category::count();
    echo "总分类数量: {$totalCategories}\n";
    
    if ($totalCategories > 0) {
        $categories = Category::where('status', 1)->limit(20)->get();
        echo "前20个启用的分类:\n";
        
        foreach ($categories as $category) {
            echo "  ID: {$category->id}, 名称: '{$category->name}', 状态: {$category->status}\n";
        }
    } else {
        echo "数据库中没有分类记录，创建一些测试分类...\n";
        
        // 创建测试分类
        $testCategories = [
            ['name' => '医疗设备', 'status' => 1, 'parent_id' => 0, 'level' => 1],
            ['name' => '诊断设备', 'status' => 1, 'parent_id' => 0, 'level' => 1],
            ['name' => '治疗设备', 'status' => 1, 'parent_id' => 0, 'level' => 1],
            ['name' => 'IT设备', 'status' => 1, 'parent_id' => 0, 'level' => 1],
            ['name' => '办公设备', 'status' => 1, 'parent_id' => 0, 'level' => 1],
            ['name' => '网络设备', 'status' => 1, 'parent_id' => 0, 'level' => 1],
            ['name' => '实验设备', 'status' => 1, 'parent_id' => 0, 'level' => 1],
            ['name' => '安防设备', 'status' => 1, 'parent_id' => 0, 'level' => 1],
        ];
        
        foreach ($testCategories as $categoryData) {
            Category::create($categoryData);
        }
        
        echo "创建了 " . count($testCategories) . " 个测试分类\n";
    }
    
    // 2. 测试分类匹配功能
    echo "\n2. 测试分类匹配功能...\n";
    
    $service = new AssetImportService();
    $reflection = new ReflectionClass($service);
    
    // 获取私有方法
    $parseMethod = $reflection->getMethod('parseAssetCategories');
    $parseMethod->setAccessible(true);
    
    $findMethod = $reflection->getMethod('findCategoryIdByName');
    $findMethod->setAccessible(true);
    
    // 测试用例
    $testCases = [
        '医疗设备',
        '诊断设备',
        'IT设备',
        '办公设备',
        '医疗', // 模糊匹配
        '设备', // 模糊匹配
        '不存在的分类', // 不存在
        '医疗设备,IT设备', // 多个分类
        '医疗设备;诊断设备|办公设备', // 多种分隔符
    ];
    
    foreach ($testCases as $testCase) {
        echo "\n测试分类: '{$testCase}'\n";
        
        try {
            $categoryIds = $parseMethod->invoke($service, $testCase);
            echo "  解析结果: " . json_encode($categoryIds) . "\n";
            
            if (!empty($categoryIds)) {
                echo "  匹配的分类:\n";
                foreach ($categoryIds as $categoryId) {
                    $category = Category::find($categoryId);
                    if ($category) {
                        echo "    ID: {$categoryId}, 名称: '{$category->name}'\n";
                    } else {
                        echo "    ID: {$categoryId}, 名称: 未找到\n";
                    }
                }
            } else {
                echo "  未找到匹配的分类\n";
            }
            
        } catch (Exception $e) {
            echo "  ✗ 解析失败: " . $e->getMessage() . "\n";
        }
    }
    
    // 3. 测试单个分类查找
    echo "\n3. 测试单个分类查找...\n";
    
    $singleTestCases = [
        '医疗设备', // 精确匹配
        '医疗', // 模糊匹配
        'IT', // 模糊匹配
        '不存在的分类', // 不存在
    ];
    
    foreach ($singleTestCases as $categoryName) {
        echo "\n查找分类: '{$categoryName}'\n";
        
        try {
            $categoryId = $findMethod->invoke($service, $categoryName);
            
            if ($categoryId) {
                $category = Category::find($categoryId);
                echo "  ✓ 找到分类: ID {$categoryId}, 名称 '{$category->name}'\n";
            } else {
                echo "  ✗ 未找到分类\n";
            }
            
        } catch (Exception $e) {
            echo "  ✗ 查找失败: " . $e->getMessage() . "\n";
        }
    }
    
    // 4. 测试缓存功能
    echo "\n4. 测试缓存功能...\n";
    
    $testCategoryName = '医疗设备';
    
    // 第一次查找（应该查询数据库）
    $startTime = microtime(true);
    $categoryId1 = $findMethod->invoke($service, $testCategoryName);
    $time1 = microtime(true) - $startTime;
    
    // 第二次查找（应该使用缓存）
    $startTime = microtime(true);
    $categoryId2 = $findMethod->invoke($service, $testCategoryName);
    $time2 = microtime(true) - $startTime;
    
    echo "第一次查找: {$time1}秒, 结果: {$categoryId1}\n";
    echo "第二次查找: {$time2}秒, 结果: {$categoryId2}\n";
    
    if ($time2 < $time1) {
        echo "✓ 缓存功能正常工作\n";
    } else {
        echo "⚠ 缓存可能未生效\n";
    }
    
    // 5. 测试完整的导入流程
    echo "\n5. 测试完整的导入流程...\n";
    
    // 模拟资产数据
    $mockAssetData = [
        '资产名称' => '测试医疗设备',
        '医疗分类' => '医疗设备',
        '行业分类' => 'IT设备',
    ];
    
    echo "模拟资产数据: " . json_encode($mockAssetData, JSON_UNESCAPED_UNICODE) . "\n";
    
    // 设置模板类型
    $templateTypeProperty = $reflection->getProperty('templateType');
    $templateTypeProperty->setAccessible(true);
    $templateTypeProperty->setValue($service, 'new_template');
    
    // 准备资产数据
    $prepareMethod = $reflection->getMethod('prepareNewTemplateAssetData');
    $prepareMethod->setAccessible(true);
    
    try {
        $assetData = $prepareMethod->invoke($service, $mockAssetData, 1);
        
        echo "准备的资产数据:\n";
        echo "  asset_category_ids: {$assetData['asset_category_ids']}\n";
        
        $categoryIds = json_decode($assetData['asset_category_ids'], true);
        if (!empty($categoryIds)) {
            echo "  匹配的分类:\n";
            foreach ($categoryIds as $categoryId) {
                $category = Category::find($categoryId);
                if ($category) {
                    echo "    ID: {$categoryId}, 名称: '{$category->name}'\n";
                }
            }
        }
        
    } catch (Exception $e) {
        echo "  ✗ 资产数据准备失败: " . $e->getMessage() . "\n";
    }
    
    echo "\n=== 测试完成 ===\n";
    
} catch (Exception $e) {
    echo "✗ 测试失败：" . $e->getMessage() . "\n";
    echo "错误位置：" . $e->getFile() . ":" . $e->getLine() . "\n";
}

echo "\n=== 测试结束 ===\n";
