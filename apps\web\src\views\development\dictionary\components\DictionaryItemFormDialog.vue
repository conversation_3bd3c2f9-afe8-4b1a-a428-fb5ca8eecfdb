<template>
  <ElDialog
    v-model="visible"
    :title="isEdit ? '编辑字典项' : '新增字典项'"
    width="600px"
    :close-on-click-modal="false"
    @closed="handleClosed"
  >
    <ElForm ref="formRef" :model="formData" :rules="formRules" label-width="100px">
      <ElFormItem label="字典编码" prop="code">
        <ElInput v-model="formData.code" placeholder="请输入字典编码" :disabled="isEdit" />
      </ElFormItem>
      <ElFormItem label="字典值" prop="value">
        <ElInput v-model="formData.value" placeholder="请输入字典值" />
      </ElFormItem>
      <ElFormItem label="排序" prop="sort">
        <ElInputNumber v-model="formData.sort" :min="0" :max="9999" />
      </ElFormItem>
      <ElFormItem label="状态" prop="enabled">
        <ElSwitch v-model="formData.enabled" />
      </ElFormItem>
      <ElFormItem label="颜色" prop="color">
        <ElColorPicker v-model="formData.color" :predefine="predefineColors" />
      </ElFormItem>
      <ElFormItem label="备注" prop="remark">
        <ElInput v-model="formData.remark" type="textarea" :rows="3" placeholder="请输入备注" />
      </ElFormItem>
      <ElFormItem label="扩展配置" prop="config">
        <ElInput
          v-model="configString"
          type="textarea"
          :rows="5"
          placeholder="请输入JSON格式的扩展配置"
          @blur="handleConfigBlur"
        />
      </ElFormItem>
    </ElForm>
    <template #footer>
      <ElButton @click="handleCancel">取消</ElButton>
      <ElButton type="primary" @click="handleConfirm" :loading="loading">确定</ElButton>
    </template>
  </ElDialog>
</template>

<script setup lang="ts">
  import { ref, computed, watch } from 'vue'
  import { ElMessage } from 'element-plus'
  import type { FormInstance, FormRules } from 'element-plus'
  import { createDictionaryItem, updateDictionaryItem } from '@/api/admin/dictionaryApi'
  import type { DictionaryItem, DictionaryItemForm } from '@/types/api'

  // Props
  interface Props {
    categoryId: string
  }
  const props = defineProps<Props>()

  // 使用 defineModel 简化 v-model
  const visible = defineModel<boolean>('visible', { default: false })
  const item = defineModel<DictionaryItem | null>('item', { default: null })

  // Emits
  const emit = defineEmits<{
    success: []
  }>()

  // 计算属性判断是否编辑模式
  const isEdit = computed(() => !!item.value?.id)

  // 表单实例
  const formRef = ref<FormInstance>()
  const loading = ref(false)

  // 表单数据
  const formData = ref<DictionaryItemForm>({
    id: '',
    category_id: props.categoryId,
    code: '',
    value: '',
    sort: 0,
    remark: '',
    enabled: true,
    color: '',
    config: {}
  })

  // 配置字符串（用于编辑JSON）
  const configString = ref('')

  // 预定义颜色
  const predefineColors = [
    '#ff4500',
    '#ff8c00',
    '#ffd700',
    '#90ee90',
    '#00ced1',
    '#1e90ff',
    '#c71585',
    'rgba(255, 69, 0, 0.68)',
    'rgb(255, 120, 0)',
    'hsv(51, 100, 98)',
    'hsva(120, 40, 94, 0.5)',
    'hsl(181, 100%, 37%)',
    'hsla(209, 100%, 56%, 0.73)',
    '#c7158577'
  ]

  // 表单验证规则
  const formRules: FormRules = {
    code: [{ required: true, message: '请输入字典编码', trigger: 'blur' }],
    value: [{ required: true, message: '请输入字典值', trigger: 'blur' }],
    sort: [{ required: true, message: '请输入排序号', trigger: 'blur' }]
  }

  // 处理配置输入框失去焦点
  const handleConfigBlur = () => {
    try {
      formData.value.config = configString.value ? JSON.parse(configString.value) : {}
    } catch {
      ElMessage.warning('JSON格式不正确，请检查')
    }
  }

  // 将颜色转换为16进制格式
  const convertToHex = (color: string): string => {
    if (!color) return ''

    // 如果已经是16进制格式，直接返回
    if (color.startsWith('#')) {
      return color.toUpperCase()
    }

    // 处理rgba格式
    const rgba = color.match(/rgba?\((\d+),\s*(\d+),\s*(\d+)(?:,\s*([\d.]+))?\)/)
    if (rgba) {
      const r = parseInt(rgba[1]).toString(16).padStart(2, '0')
      const g = parseInt(rgba[2]).toString(16).padStart(2, '0')
      const b = parseInt(rgba[3]).toString(16).padStart(2, '0')
      return `#${r}${g}${b}`.toUpperCase()
    }

    return color
  }

  // 监听 item 变化，更新表单数据
  watch(
    () => item.value,
    (newVal) => {
      if (newVal) {
        // 编辑模式，填充数据
        formData.value = {
          id: newVal.id,
          category_id: props.categoryId,
          code: newVal.code,
          value: newVal.value,
          sort: newVal.sort || 0,
          remark: newVal.remark || '',
          enabled: newVal.enabled,
          color: newVal.color || '',
          config: newVal.config || {}
        }
        configString.value = JSON.stringify(newVal.config || {}, null, 2)
      } else {
        // 新增模式，重置数据
        formData.value = {
          id: '',
          category_id: props.categoryId,
          code: '',
          value: '',
          sort: 0,
          remark: '',
          enabled: true,
          color: '',
          config: {}
        }
        configString.value = JSON.stringify({}, null, 2)
      }
    },
    { immediate: true }
  )

  // 处理取消
  const handleCancel = () => {
    visible.value = false
  }

  // 处理确认
  const handleConfirm = async () => {
    if (!formRef.value) return

    await formRef.value.validate()

    loading.value = true
    try {
      // 转换颜色为16进制格式
      const submitData = {
        ...formData.value,
        color: convertToHex(formData.value.color)
      }

      if (isEdit.value) {
        await updateDictionaryItem(submitData.id!, submitData)
        ElMessage.success('修改成功')
      } else {
        await createDictionaryItem(submitData)
        ElMessage.success('新增成功')
      }

      visible.value = false
      emit('success')
    } finally {
      loading.value = false
    }
  }

  // 对话框关闭后的处理
  const handleClosed = () => {
    // 重置表单
    formRef.value?.resetFields()
    // 清空编辑数据
    item.value = null
    // 重置配置字符串
    configString.value = JSON.stringify({}, null, 2)
  }
</script>
