<template>
  <div class="operation-log-page art-page-view">
    <!-- 搜索栏 -->
    <ArtSearchBar
      v-model:filter="searchFormState"
      :items="searchFormItems"
      :showExpand="true"
      @reset="handleReset"
      @search="handleSearch"
    />

    <ElCard shadow="never" class="art-table-card" style="margin-top: 0">
      <!-- 表格头部 -->
      <ArtTableHeader v-model:columns="columnChecks" @refresh="refreshAll">
        <template #left>
          <span class="text-gray-500">操作日志记录</span>
        </template>
      </ArtTableHeader>

      <!-- 操作日志表格 -->
      <ArtTable
        ref="tableRef"
        :loading="isLoading"
        :data="tableData"
        :columns="columns"
        :pagination="paginationState"
        :table-config="{ rowKey: 'id', height: '600px' }"
        :layout="{ marginTop: 10 }"
        @pagination:size-change="onPageSizeChange"
        @pagination:current-change="onCurrentPageChange"
      >
        <!-- 请求方法列 -->
        <template #method="{ row }">
          <ElTag :type="getMethodTagType(row.method)" size="small">
            {{ row.method }}
          </ElTag>
        </template>

        <!-- 操作列 -->
        <template #operation="{ row }">
          <div style="display: flex; gap: 5px">
            <ArtButtonTable type="detail" @click="showDetail(row)" />
          </div>
        </template>
      </ArtTable>

      <!-- 详情对话框 -->
      <OperationLogDetailDialog v-model:visible="detailDialogVisible" :log="selectedLog" />
    </ElCard>
  </div>
</template>

<script setup lang="ts">
  defineOptions({ name: 'OperationLog' })

  // Vue 核心
  import { ref } from 'vue'

  // UI 框架
  import { ElTag } from 'element-plus'

  // 内部 hooks
  import { useTable } from '@/composables/useTable'

  // 内部组件
  import ArtButtonTable from '@/components/custom/art-button-table/index.vue'
  import OperationLogDetailDialog from './components/OperationLogDetailDialog.vue'

  // 工具
  import { formatDate } from '@/utils/dataprocess/format'

  // API
  import { getOperationLogs } from '@/api/admin/operationLogApi'

  // 类型定义
  import type { SearchFormItem } from '@/types'
  import type { OperationLog, OperationLogSearchParams } from '@/types/api'

  // 详情对话框状态
  const detailDialogVisible = ref(false)
  const selectedLog = ref<OperationLog | null>(null)
  const tableRef = ref()

  // 搜索表单状态
  const searchFormState = ref({
    user_name: '',
    menu_name: '',
    start_time: '',
    end_time: ''
  })

  // 时间范围快捷选项
  const shortcuts = [
    {
      text: '今天',
      value: () => {
        const end = new Date()
        const start = new Date()
        start.setHours(0, 0, 0, 0)
        return [start, end]
      }
    },
    {
      text: '昨天',
      value: () => {
        const end = new Date()
        const start = new Date()
        start.setDate(start.getDate() - 1)
        start.setHours(0, 0, 0, 0)
        end.setDate(end.getDate() - 1)
        end.setHours(23, 59, 59, 999)
        return [start, end]
      }
    },
    {
      text: '最近一周',
      value: () => {
        const end = new Date()
        const start = new Date()
        start.setDate(start.getDate() - 7)
        return [start, end]
      }
    },
    {
      text: '最近一个月',
      value: () => {
        const end = new Date()
        const start = new Date()
        start.setMonth(start.getMonth() - 1)
        return [start, end]
      }
    }
  ]

  // 搜索表单配置
  const searchFormItems: SearchFormItem[] = [
    {
      prop: 'user_name',
      label: '账号',
      type: 'input',
      placeholder: '请输入账号'
    },
    {
      prop: 'menu_name',
      label: '菜单',
      type: 'input',
      placeholder: '请输入菜单名称'
    },
    {
      prop: 'dateRange',
      label: '操作时间',
      type: 'daterange',
      config: {
        type: 'datetimerange',
        startPlaceholder: '开始时间',
        endPlaceholder: '结束时间',
        valueFormat: 'YYYY-MM-DD HH:mm:ss',
        shortcuts,
        clearable: true
      }
    }
  ]

  // 使用 useTable Hook
  const {
    tableData,
    columns,
    columnChecks,
    isLoading,
    paginationState,
    refreshAll,
    onPageSizeChange,
    onCurrentPageChange,
    searchState,
    searchData,
    resetSearch
  } = useTable<OperationLog>({
    core: {
      apiFn: async (params: any) => {
        // 处理时间范围
        const searchParams: OperationLogSearchParams = {
          page: params.current,
          per_page: params.size,
          user_name: params.user_name,
          menu_name: params.menu_name
        }

        // 处理时间范围
        if (params.dateRange && params.dateRange.length === 2) {
          searchParams.start_time = params.dateRange[0]
          searchParams.end_time = params.dateRange[1]
        }

        const response = await getOperationLogs(searchParams)

        return {
          records: response.data,
          total: response.total,
          current: response.current_page,
          size: response.per_page
        }
      },
      apiParams: {
        current: 1,
        size: 20,
        user_name: '',
        menu_name: '',
        dateRange: []
      },
      columnsFactory: () => [
        {
          prop: 'user_name',
          label: '用户名',
          minWidth: 100,
          formatter: (row: OperationLog) => {
            return row.user_name || '未登录用户'
          }
        },
        {
          prop: 'menu_name',
          label: '菜单',
          minWidth: 120,
          formatter: (row: OperationLog) => {
            return row.menu_name || '-'
          }
        },
        {
          prop: 'operation_type',
          label: '操作类型',
          width: 100,
          align: 'center',
          formatter: (row: OperationLog) => {
            return row.operation_type_text || row.operation_type || '-'
          }
        },
        {
          prop: 'path',
          label: '请求路径',
          minWidth: 200,
          showOverflowTooltip: true
        },
        {
          prop: 'ip',
          label: 'IP地址',
          width: 140
        },
        {
          prop: 'created_at',
          label: '操作时间',
          width: 180,
          formatter: (row: OperationLog) => {
            return row.created_at ? formatDate(row.created_at, 'YYYY-MM-DD HH:mm:ss') : ''
          }
        },
        {
          prop: 'operation',
          label: '操作',
          width: 80,
          fixed: 'right',
          useSlot: true
        }
      ]
    }
  })

  // 搜索相关方法
  const handleReset = () => {
    searchFormState.value = {
      user_name: '',
      menu_name: '',
      start_time: '',
      end_time: ''
    }
    resetSearch()
  }

  const handleSearch = () => {
    // 直接更新 searchState 的各个属性
    searchState.user_name = searchFormState.value.user_name
    searchState.menu_name = searchFormState.value.menu_name

    // 处理时间范围
    const dateRange = searchFormState.value as any
    if (dateRange.dateRange) {
      searchState.dateRange = dateRange.dateRange
    } else {
      searchState.dateRange = []
    }

    searchData()
  }

  // 获取请求方法标签类型
  const getMethodTagType = (method: string) => {
    const typeMap: Record<string, string> = {
      GET: 'info',
      POST: 'success',
      PUT: 'warning',
      DELETE: 'danger',
      PATCH: 'warning'
    }
    return (typeMap[method] || 'info') as any
  }

  // 显示详情
  const showDetail = (row: OperationLog) => {
    selectedLog.value = row
    detailDialogVisible.value = true
  }
</script>

<style lang="scss" scoped>
  .operation-log-page {
    .text-gray-500 {
      font-size: 14px;
      color: #6b7280;
    }
  }
</style>
