<?php

/**
 * 测试新模板的导入功能
 */

require_once __DIR__ . '/vendor/autoload.php';

use Illuminate\Foundation\Application;
use App\Models\Attachment;
use App\Models\AssetImportTask;
use App\Services\AssetImportService;

// 启动Laravel应用
$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "=== 测试新模板导入功能 ===\n\n";

try {
    // 查找新模板文件
    echo "1. 查找新模板文件...\n";

    $templatePath = 'storage/app/public/资产导入模板_2025-08-14_10-26-23.xlsx';
    $fullPath = __DIR__ . '/' . $templatePath;

    if (!file_exists($fullPath)) {
        echo "模板文件不存在: {$fullPath}\n";
        exit(1);
    }

    echo "找到模板文件: {$templatePath}\n";
    echo "文件大小: " . number_format(filesize($fullPath) / 1024, 2) . " KB\n";

    // 创建或查找对应的附件记录
    echo "\n2. 创建附件记录...\n";

    $attachment = Attachment::where('file_name', '资产导入模板_2025-08-14_10-26-23.xlsx')->first();

    if (!$attachment) {
        $attachment = Attachment::create([
            'file_name' => '资产导入模板_2025-08-14_10-26-23.xlsx',
            'file_path' => '资产导入模板_2025-08-14_10-26-23.xlsx',
            'file_size' => filesize($fullPath),
            'mime_type' => 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            'storage_type' => 'local',
            'md5_hash' => md5_file($fullPath),
        ]);
        echo "创建新附件记录，ID: {$attachment->id}\n";
    } else {
        echo "使用现有附件记录，ID: {$attachment->id}\n";
    }

    // 验证文件路径解析
    echo "\n3. 验证文件路径解析...\n";

    $service = new AssetImportService();
    $reflection = new ReflectionClass($service);
    $method = $reflection->getMethod('getActualFilePath');
    $method->setAccessible(true);

    $resolvedPath = $method->invoke($service, $attachment->file_path);
    echo "原始路径: {$attachment->file_path}\n";
    echo "解析路径: {$resolvedPath}\n";
    echo "文件存在: " . (file_exists($resolvedPath) ? '是' : '否') . "\n";

    if (!file_exists($resolvedPath)) {
        echo "错误：无法找到文件，请检查路径配置\n";
        exit(1);
    }

    // 测试标题行验证
    echo "\n4. 测试标题行验证...\n";

    try {
        // 读取Excel文件获取标题行
        class TestReader
        {
            // 空的读取器类
        }

        $data = \Maatwebsite\Excel\Facades\Excel::toArray(new TestReader(), $resolvedPath);

        if (!empty($data) && !empty($data[0]) && count($data[0]) > 1) {
            $headers = $data[0][1]; // 第2行是标题行
            echo "读取到标题行，字段数: " . count($headers) . "\n";

            // 显示前10个字段
            $sampleHeaders = array_slice($headers, 0, 10);
            echo "示例字段: " . implode(', ', $sampleHeaders) . "\n";

            // 测试标题验证
            $validateMethod = $reflection->getMethod('validateHeaders');
            $validateMethod->setAccessible(true);

            try {
                $validateMethod->invoke($service, $headers);
                echo "✓ 标题行验证通过\n";
            } catch (Exception $e) {
                echo "✗ 标题行验证失败: " . $e->getMessage() . "\n";
            }
        } else {
            echo "无法读取标题行数据\n";
        }

    } catch (Exception $e) {
        echo "读取Excel文件失败: " . $e->getMessage() . "\n";
    }

    // 创建导入任务
    echo "\n5. 创建导入任务...\n";

    $importTask = AssetImportTask::create([
        'file_path' => $attachment->file_path,
        'original_filename' => $attachment->file_name,
        'status' => 'pending',
        'created_by' => 1,
    ]);

    echo "导入任务创建成功，ID: {$importTask->id}\n";

    // 测试导入处理（只处理前几行数据）
    echo "\n6. 测试导入处理...\n";

    try {
        $importTask->markAsProcessing();
        echo "任务状态更新为处理中\n";

        // 限制处理行数以避免处理过多数据
        $originalBatchSize = $service->batchSize ?? 1000;

        // 使用反射设置较小的批次大小进行测试
        $batchSizeProperty = $reflection->getProperty('batchSize');
        $batchSizeProperty->setAccessible(true);
        $batchSizeProperty->setValue($service, 5); // 只处理5行数据

        $result = $service->processImport($importTask);

        echo "导入处理完成！\n";
        echo "总行数: " . $result['total_rows'] . "\n";
        echo "成功行数: " . $result['success_rows'] . "\n";
        echo "失败行数: " . $result['failed_rows'] . "\n";

        if (!empty($result['errors'])) {
            echo "\n错误详情:\n";
            foreach (array_slice($result['errors'], 0, 3) as $error) {
                echo "  行 {$error['row']}: {$error['error']}\n";
            }
        }

        $importTask->markAsCompleted($result);
        echo "\n任务状态更新为完成\n";

    } catch (Exception $e) {
        echo "导入处理失败: " . $e->getMessage() . "\n";
        echo "错误位置: " . $e->getFile() . ":" . $e->getLine() . "\n";

        $importTask->markAsFailed([
            'error' => $e->getMessage(),
            'trace' => $e->getTraceAsString(),
        ]);
    }

    // 显示最终结果
    echo "\n7. 最终结果...\n";
    $importTask->refresh();

    echo "任务状态: {$importTask->status}\n";
    echo "总行数: {$importTask->total_rows}\n";
    echo "成功行数: {$importTask->success_rows}\n";
    echo "失败行数: {$importTask->failed_rows}\n";

    if ($importTask->summary) {
        echo "摘要: {$importTask->summary}\n";
    }

    echo "\n=== 测试完成 ===\n";

    if ($importTask->status === 'completed') {
        echo "✓ 新模板导入测试成功！\n";
        echo "\n可以使用以下API进行完整导入:\n";
        echo "POST /api/admin/assets/import/{$attachment->id}\n";
    } else {
        echo "✗ 新模板导入测试失败，请检查错误信息\n";
    }

} catch (Exception $e) {
    echo "✗ 测试失败：" . $e->getMessage() . "\n";
    echo "错误位置：" . $e->getFile() . ":" . $e->getLine() . "\n";
}

echo "\n=== 测试结束 ===\n";
