name: 附件管理
description: 附件的上传、下载、删除等操作
endpoints:
  -
    httpMethods:
      - GET
    uri: api/admin/attachments
    metadata:
      groupName: 附件管理
      groupDescription: 附件的上传、下载、删除等操作
      subgroup: ''
      subgroupDescription: ''
      title: 获取附件列表
      description: ''
      authenticated: true
      deprecated: false
      custom: []
    headers:
      Authorization: 'Bearer {YOUR_AUTH_KEY}'
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters:
      page:
        name: page
        description: 页码
        required: false
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      per_page:
        name: per_page
        description: 每页数量
        required: false
        example: 20
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      file_name:
        name: file_name
        description: 文件名（模糊搜索）
        required: false
        example: example.pdf
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      start_time:
        name: start_time
        description: 开始时间
        required: false
        example: '2024-01-01'
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      end_time:
        name: end_time
        description: 结束时间
        required: false
        example: '2024-12-31'
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanQueryParameters:
      page: 1
      per_page: 20
      file_name: example.pdf
      start_time: '2024-01-01'
      end_time: '2024-12-31'
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: '{"data":[{"id":"1","file_name":"u=3210302231,1107381898&fm=253&app=138&f=JPEG.jpg","file_path":"attachments\/2025\/08\/12\/pJ0dlrDN499j8xyPhU0PzDgbkolvkICk.jpg","file_size":61190,"mime_type":"image\/jpeg","storage_type":"local","md5_hash":"765bec84e828b788df63d4e1e58c1567","file_url":"http:\/\/localhost:8005\/storage\/attachments\/2025\/08\/12\/pJ0dlrDN499j8xyPhU0PzDgbkolvkICk.jpg","formatted_file_size":"59.76 KB","created_at":1754963644,"updated_at":1754963644,"attachable_type":"App\\Models\\User","attachable_id":"1","category":"avatar","description":null},{"id":"1","file_name":"u=3210302231,1107381898&fm=253&app=138&f=JPEG.jpg","file_path":"attachments\/2025\/08\/12\/pJ0dlrDN499j8xyPhU0PzDgbkolvkICk.jpg","file_size":61190,"mime_type":"image\/jpeg","storage_type":"local","md5_hash":"765bec84e828b788df63d4e1e58c1567","file_url":"http:\/\/localhost:8005\/storage\/attachments\/2025\/08\/12\/pJ0dlrDN499j8xyPhU0PzDgbkolvkICk.jpg","formatted_file_size":"59.76 KB","created_at":1754963644,"updated_at":1754963644,"attachable_type":"App\\Models\\User","attachable_id":"1","category":"avatar","description":null}],"links":{"first":"\/?page=1","last":"\/?page=1","prev":null,"next":null},"meta":{"current_page":1,"from":1,"last_page":1,"links":[{"url":null,"label":"&laquo; Previous","active":false},{"url":"\/?page=1","label":"1","active":true},{"url":null,"label":"Next &raquo;","active":false}],"path":"\/","per_page":20,"to":2,"total":2}}'
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth:
      - headers
      - Authorization
      - 'Bearer 6g43cv8PD1aE5beadkZfhV6'
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/admin/attachments/upload
    metadata:
      groupName: 附件管理
      groupDescription: 附件的上传、下载、删除等操作
      subgroup: ''
      subgroupDescription: ''
      title: 上传附件（本地上传）
      description: ''
      authenticated: true
      deprecated: false
      custom: []
    headers:
      Authorization: 'Bearer {YOUR_AUTH_KEY}'
      Content-Type: multipart/form-data
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      file:
        name: file
        description: 要上传的文件（最大10MB）
        required: true
        example: null
        type: file
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanBodyParameters: []
    fileParameters:
      file: null
    responses:
      -
        status: 401
        content: '{"message":"Unauthenticated."}'
        headers:
          cache-control: 'no-cache, private'
          content-type: application/json
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth:
      - headers
      - Authorization
      - 'Bearer 6g43cv8PD1aE5beadkZfhV6'
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: 'api/admin/attachments/{id}'
    metadata:
      groupName: 附件管理
      groupDescription: 附件的上传、下载、删除等操作
      subgroup: ''
      subgroupDescription: ''
      title: 获取附件详情
      description: ''
      authenticated: true
      deprecated: false
      custom: []
    headers:
      Authorization: 'Bearer {YOUR_AUTH_KEY}'
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 附件ID
        required: true
        example: 16
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      id: 16
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"message":"Unauthenticated."}'
        headers:
          cache-control: 'no-cache, private'
          content-type: application/json
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth:
      - headers
      - Authorization
      - 'Bearer 6g43cv8PD1aE5beadkZfhV6'
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: 'api/admin/attachments/{attachment}/download'
    metadata:
      groupName: 附件管理
      groupDescription: 附件的上传、下载、删除等操作
      subgroup: ''
      subgroupDescription: ''
      title: 下载附件
      description: ''
      authenticated: true
      deprecated: false
      custom: []
    headers:
      Authorization: 'Bearer {YOUR_AUTH_KEY}'
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      attachment:
        name: attachment
        description: 'The attachment.'
        required: true
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      id:
        name: id
        description: 附件ID
        required: true
        example: 16
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      attachment: 1
      id: 16
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"message":"Unauthenticated."}'
        headers:
          cache-control: 'no-cache, private'
          content-type: application/json
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth:
      - headers
      - Authorization
      - 'Bearer 6g43cv8PD1aE5beadkZfhV6'
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/admin/attachments/sts/credentials
    metadata:
      groupName: 附件管理
      groupDescription: 附件的上传、下载、删除等操作
      subgroup: ''
      subgroupDescription: ''
      title: 获取STS临时凭证
      description: ''
      authenticated: true
      deprecated: false
      custom: []
    headers:
      Authorization: 'Bearer {YOUR_AUTH_KEY}'
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      filename:
        name: filename
        description: 文件名
        required: true
        example: example.pdf
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      filesize:
        name: filesize
        description: 文件大小（字节）
        required: true
        example: 1024000
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      mime_type:
        name: mime_type
        description: MIME类型
        required: true
        example: application/pdf
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      md5_hash:
        name: md5_hash
        description: 文件MD5值（用于秒传）
        required: false
        example: 5d41402abc4b2a76b9719d911017c592
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: true
        custom: []
    cleanBodyParameters:
      filename: example.pdf
      filesize: 1024000
      mime_type: application/pdf
      md5_hash: 5d41402abc4b2a76b9719d911017c592
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
            "quick_upload": false,
            "upload_id": "550e8400-e29b-41d4-a716-446655440000",
            "credentials": {
              "AccessKeyId": "STS.xxx",
              "AccessKeySecret": "xxx",
              "SecurityToken": "xxx",
              "Expiration": "2025-08-04T12:00:00Z"
            },
            "region": "cn-hangzhou",
            "bucket": "my-bucket",
            "endpoint": "https://oss-cn-hangzhou.aliyuncs.com",
            "prefix": "attachments/"
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth:
      - headers
      - Authorization
      - 'Bearer 6g43cv8PD1aE5beadkZfhV6'
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/admin/attachments/sts/confirm
    metadata:
      groupName: 附件管理
      groupDescription: 附件的上传、下载、删除等操作
      subgroup: ''
      subgroupDescription: ''
      title: 确认上传完成
      description: ''
      authenticated: true
      deprecated: false
      custom: []
    headers:
      Authorization: 'Bearer {YOUR_AUTH_KEY}'
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      upload_id:
        name: upload_id
        description: 上传ID
        required: true
        example: 550e8400-e29b-41d4-a716-446655440000
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      object_key:
        name: object_key
        description: OSS对象键值
        required: true
        example: attachments/2025/08/04/xxx.pdf
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      filename:
        name: filename
        description: 文件名（可选）
        required: false
        example: example.pdf
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: true
        custom: []
      filesize:
        name: filesize
        description: 文件大小（可选）
        required: false
        example: 1024000
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: true
        custom: []
      mime_type:
        name: mime_type
        description: MIME类型（可选）
        required: false
        example: application/pdf
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: true
        custom: []
    cleanBodyParameters:
      upload_id: 550e8400-e29b-41d4-a716-446655440000
      object_key: attachments/2025/08/04/xxx.pdf
      filename: example.pdf
      filesize: 1024000
      mime_type: application/pdf
    fileParameters: []
    responses:
      -
        status: 200
        content: '{"id":"1","file_name":"u=3210302231,1107381898&fm=253&app=138&f=JPEG.jpg","file_path":"attachments\/2025\/08\/12\/pJ0dlrDN499j8xyPhU0PzDgbkolvkICk.jpg","file_size":61190,"mime_type":"image\/jpeg","storage_type":"local","md5_hash":"765bec84e828b788df63d4e1e58c1567","file_url":"http:\/\/localhost:8005\/storage\/attachments\/2025\/08\/12\/pJ0dlrDN499j8xyPhU0PzDgbkolvkICk.jpg","formatted_file_size":"59.76 KB","created_at":1754963644,"updated_at":1754963644,"attachable_type":null,"attachable_id":null,"category":null,"description":null}'
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth:
      - headers
      - Authorization
      - 'Bearer 6g43cv8PD1aE5beadkZfhV6'
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: api/admin/attachments/by-business
    metadata:
      groupName: 附件管理
      groupDescription: 附件的上传、下载、删除等操作
      subgroup: ''
      subgroupDescription: ''
      title: 根据业务ID获取附件列表
      description: ''
      authenticated: true
      deprecated: false
      custom: []
    headers:
      Authorization: 'Bearer {YOUR_AUTH_KEY}'
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters:
      attachable_type:
        name: attachable_type
        description: 业务类型
        required: true
        example: App\Models\Entity
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      attachable_id:
        name: attachable_id
        description: 业务ID
        required: true
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      category:
        name: category
        description: 附件分类
        required: false
        example: contract
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanQueryParameters:
      attachable_type: App\Models\Entity
      attachable_id: 1
      category: contract
    bodyParameters:
      attachable_type:
        name: attachable_type
        description: ''
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      attachable_id:
        name: attachable_id
        description: ''
        required: true
        example: 16
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      category:
        name: category
        description: ''
        required: false
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
    cleanBodyParameters:
      attachable_type: architecto
      attachable_id: 16
      category: architecto
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"message":"Unauthenticated."}'
        headers:
          cache-control: 'no-cache, private'
          content-type: application/json
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth:
      - headers
      - Authorization
      - 'Bearer 6g43cv8PD1aE5beadkZfhV6'
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - PUT
    uri: 'api/admin/attachments/update-by-relation/{attachmentRelation_id}'
    metadata:
      groupName: 附件管理
      groupDescription: 附件的上传、下载、删除等操作
      subgroup: ''
      subgroupDescription: ''
      title: 更新文件关联描述
      description: ''
      authenticated: true
      deprecated: false
      custom: []
    headers:
      Authorization: 'Bearer {YOUR_AUTH_KEY}'
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      attachmentRelation_id:
        name: attachmentRelation_id
        description: 'The ID of the attachmentRelation.'
        required: true
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      attachable_relation_id:
        name: attachable_relation_id
        description: 附件关联ID
        required: true
        example: 16
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      attachmentRelation_id: 1
      attachable_relation_id: 16
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      description:
        name: description
        description: 描述
        required: false
        example: 这是我的描述
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanBodyParameters:
      description: 这是我的描述
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"message":"Unauthenticated."}'
        headers:
          cache-control: 'no-cache, private'
          content-type: application/json
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth:
      - headers
      - Authorization
      - 'Bearer 6g43cv8PD1aE5beadkZfhV6'
    controller: null
    method: null
    route: null
    custom: []
