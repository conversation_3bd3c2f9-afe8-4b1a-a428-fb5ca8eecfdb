<?php

namespace Database\Seeders;

use App\Models\Menu;
use App\Models\MenuPermission;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class MenuSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // 清空表数据 - 先禁用外键检查
        \DB::statement('SET FOREIGN_KEY_CHECKS=0;');
        Menu::truncate();
        MenuPermission::truncate();
        \DB::statement('SET FOREIGN_KEY_CHECKS=1;');

        // 菜单数据（基于 asyncRoutes.ts 中未注释的菜单）
        $menus = [
            // 仪表盘
            [
                'name' => 'Dashboard',
                'path' => '/dashboard',
                'component' => '/index/index',
                'title' => '仪表盘',
                'icon' => '&#xe721;',
                'sort' => 100,
                'children' => [
                    [
                        'name' => 'Console',
                        'path' => 'console',
                        'component' => '/dashboard/console/index',
                        'title' => '控制台',
                        'sort' => 100,
                        'keep_alive' => false,
                        'fixed_tab' => true,
                    ],
                ],
            ],

            // 系统管理
            [
                'name' => 'System',
                'path' => '/system',
                'component' => '/index/index',
                'title' => '系统管理',
                'icon' => '&#xe7b9;',
                'sort' => 90,
                'children' => [
                    [
                        'name' => 'User',
                        'path' => 'user',
                        'component' => '/system/user/index',
                        'title' => '用户管理',
                        'sort' => 100,
                        'keep_alive' => true,
                        'permissions' => [
                            ['title' => '新增', 'auth_mark' => 'add', 'sort' => 5, 'route_name' => 'users.store'],
                            ['title' => '编辑', 'auth_mark' => 'edit', 'sort' => 4, 'route_name' => 'users.update'],
                            ['title' => '删除', 'auth_mark' => 'delete', 'sort' => 3, 'route_name' => 'users.destroy'],
                            ['title' => '重置密码', 'auth_mark' => 'reset_password', 'sort' => 2, 'route_name' => 'users.update'],
                            ['title' => '分配角色', 'auth_mark' => 'assign_roles', 'sort' => 1, 'route_name' => 'users.roles.assign'],
                        ],
                    ],
                    [
                        'name' => 'Role',
                        'path' => 'role',
                        'component' => '/system/role/index',
                        'title' => '角色管理',
                        'sort' => 90,
                        'keep_alive' => true,
                        'permissions' => [
                            ['title' => '新增', 'auth_mark' => 'add', 'sort' => 3, 'route_name' => 'roles.store'],
                            ['title' => '编辑', 'auth_mark' => 'edit', 'sort' => 2, 'route_name' => 'roles.update'],
                            ['title' => '删除', 'auth_mark' => 'delete', 'sort' => 1, 'route_name' => 'roles.destroy'],
                            ['title' => '分配权限', 'auth_mark' => 'assign_permissions', 'sort' => 4, 'route_name' => 'roles.menu-permissions.assign'],
                        ],
                    ],
                    [
                        'name' => 'UserCenter',
                        'path' => 'user-center',
                        'component' => '/system/user-center/index',
                        'title' => '个人中心',
                        'sort' => 80,
                        'is_hide' => true,
                        'keep_alive' => true,
                        'is_hide_tab' => true,
                    ],
                    [
                        'name' => 'Menus',
                        'path' => 'menu',
                        'component' => '/system/menu/index',
                        'title' => '菜单管理',
                        'sort' => 70,
                        'keep_alive' => true,
                        'permissions' => [
                            ['title' => '新增', 'auth_mark' => 'add', 'sort' => 3, 'route_name' => 'menus.store'],
                            ['title' => '编辑', 'auth_mark' => 'edit', 'sort' => 2, 'route_name' => 'menus.update'],
                            ['title' => '删除', 'auth_mark' => 'delete', 'sort' => 1, 'route_name' => 'menus.destroy'],
                        ],
                    ],
                    [
                        'name' => 'Config',
                        'path' => 'config',
                        'component' => '/system/config/index',
                        'title' => '配置管理',
                        'sort' => 80,
                        'keep_alive' => true,
                        'permissions' => [
                            ['title' => '查看', 'auth_mark' => 'view', 'sort' => 2, 'route_name' => 'configs.index'],
                            ['title' => '编辑', 'auth_mark' => 'edit', 'sort' => 1, 'route_name' => 'configs.update'],
                        ],
                    ],
                    [
                        'name' => 'OperationLog',
                        'path' => 'operation-log',
                        'component' => '/system/operation-log/index',
                        'title' => '操作日志',
                        'sort' => 60,
                        'keep_alive' => true,
                        'permissions' => [],
                    ],
                ],
            ],

            // 开发管理
            [
                'name' => 'Development',
                'path' => '/development',
                'component' => '/index/index',
                'title' => '开发管理',
                'icon' => '&#xe7c5;',
                'sort' => 80,
                'keep_alive' => false,
                'children' => [
                    [
                        'name' => 'Asset',
                        'path' => 'asset',
                        'component' => '/development/asset/index',
                        'title' => '资产管理',
                        'sort' => 100,
                        'keep_alive' => true,
                        'permissions' => [
                            ['title' => '新增', 'auth_mark' => 'add', 'sort' => 3, 'route_name' => 'assets.store'],
                            ['title' => '编辑', 'auth_mark' => 'edit', 'sort' => 2, 'route_name' => 'assets.update'],
                            ['title' => '删除', 'auth_mark' => 'delete', 'sort' => 1, 'route_name' => 'assets.destroy'],
                        ],
                    ],
                    [
                        'name' => 'Dictionary',
                        'path' => 'dictionary',
                        'component' => '/development/dictionary/index',
                        'title' => '字典管理',
                        'sort' => 90,
                        'keep_alive' => true,
                        'permissions' => [
                            ['title' => '新增', 'auth_mark' => 'add', 'sort' => 3, 'route_name' => 'dictionaries.store'],
                            ['title' => '编辑', 'auth_mark' => 'edit', 'sort' => 2, 'route_name' => 'dictionaries.update'],
                            ['title' => '删除', 'auth_mark' => 'delete', 'sort' => 1, 'route_name' => 'dictionaries.destroy'],
                        ],
                    ],
                    [
                        'name' => 'Category',
                        'path' => 'category',
                        'component' => '/development/category/index',
                        'title' => '分类管理',
                        'sort' => 80,
                        'keep_alive' => true,
                        'permissions' => [
                            ['title' => '新增', 'auth_mark' => 'add', 'sort' => 3, 'route_name' => 'categories.store'],
                            ['title' => '编辑', 'auth_mark' => 'edit', 'sort' => 2, 'route_name' => 'categories.update'],
                            ['title' => '删除', 'auth_mark' => 'delete', 'sort' => 1, 'route_name' => 'categories.destroy'],
                        ],
                    ],
                    [
                        'name' => 'Entity',
                        'path' => 'entity',
                        'component' => '/development/entity/index',
                        'title' => '主体管理',
                        'sort' => 70,
                        'keep_alive' => true,
                        'permissions' => [
                            ['title' => '新增', 'auth_mark' => 'add', 'sort' => 3, 'route_name' => 'entities.store'],
                            ['title' => '编辑', 'auth_mark' => 'edit', 'sort' => 2, 'route_name' => 'entities.update'],
                            ['title' => '删除', 'auth_mark' => 'delete', 'sort' => 1, 'route_name' => 'entities.destroy'],
                        ],
                    ],
                    [
                        'name' => 'Lifecycle',
                        'path' => 'lifecycle',
                        'component' => '/development/lifecycle/index',
                        'title' => '生命周期',
                        'sort' => 60,
                        'keep_alive' => true,
                        'permissions' => [
                            ['title' => '新增', 'auth_mark' => 'add', 'sort' => 3, 'route_name' => 'lifecycles.store'],
                            ['title' => '编辑', 'auth_mark' => 'edit', 'sort' => 2, 'route_name' => 'lifecycles.update'],
                            ['title' => '删除', 'auth_mark' => 'delete', 'sort' => 1, 'route_name' => 'lifecycles.destroy'],
                            ['title' => '查看详情', 'auth_mark' => 'view', 'sort' => 4, 'route_name' => 'lifecycles.show'],
                        ],
                    ],
                    [
                        'name' => 'AttachmentManagement',
                        'path' => 'attachment',
                        'component' => '/development/attachment/index',
                        'title' => '附件管理',
                        'sort' => 50,
                        'keep_alive' => true,
                        'permissions' => [
                            ['title' => '上传', 'auth_mark' => 'upload', 'sort' => 2, 'route_name' => 'attachments.upload'],
                            ['title' => '删除', 'auth_mark' => 'delete', 'sort' => 1, 'route_name' => 'attachments.destroy'],
                        ],
                    ],
                ],
            ],
        ];

        // 插入菜单数据
        $this->insertMenus($menus);
    }

    private function insertMenus($menus, $parentId = null)
    {
        foreach ($menus as $menuData) {
            $children = $menuData['children'] ?? [];
            $permissions = $menuData['permissions'] ?? [];

            unset($menuData['children'], $menuData['permissions']);

            // 设置默认值
            $menuData['parent_id'] = $parentId;
            $menuData['status'] = true;

            // 创建菜单
            $menu = Menu::create($menuData);

            // 创建权限按钮
            foreach ($permissions as $permission) {
                $menu->permissions()->create($permission);
            }

            // 递归创建子菜单
            if (! empty($children)) {
                $this->insertMenus($children, $menu->id);
            }
        }
    }
}
