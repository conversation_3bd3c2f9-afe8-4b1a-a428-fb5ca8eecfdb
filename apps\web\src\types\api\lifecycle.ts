// 生命周期管理相关类型定义

import type { Asset } from './asset'

// 生命周期实体
export interface Lifecycle {
  id?: number
  asset_id?: number | null // 资产ID
  asset?: Asset // 关联的资产信息
  type: string | null // 类型（字典值）
  date: string // 日期
  initiator: number | null // 发起人ID
  initiator_name?: string // 发起人名称
  content: string // 内容
  assistants: number[] // 协助人员ID数组
  assistant_names?: string[] // 协助人员名称数组
  acceptance_entity: number | null // 验收主体ID
  acceptance_entity_name?: string // 验收主体名称
  acceptance_personnel: number | null // 验收人员ID
  acceptance_personnel_name?: string // 验收人员名称
  acceptance_time: string // 验收时间
  attachments: any[] // 相关文件
  attachment_details?: any[] // 附件详情
  created_at?: string // 创建时间
  updated_at?: string // 更新时间
}

// 生命周期跟进记录
export interface LifecycleFollowUp {
  id?: number
  lifecycle_id: number | null // 周期表ID
  date: string // 日期
  person_id: number | null // 人员ID（只能是协助人员中的）
  content: string // 内容
  created_at?: string // 添加时间
  attachments: any[] // 相关文件
  attachment_details?: any[] // 附件详情
}
