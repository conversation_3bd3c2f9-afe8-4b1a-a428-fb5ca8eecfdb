<?php

/**
 * 测试相关主体数据保存格式
 */

require_once __DIR__ . '/vendor/autoload.php';

use Illuminate\Foundation\Application;
use App\Models\Asset;
use App\Models\Entity;
use App\Models\EntityContact;

// 启动Laravel应用
$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "=== 测试相关主体数据保存格式 ===\n\n";

try {
    // 查找最新导入的资产
    echo "1. 查找最新导入的资产...\n";

    $latestAssets = Asset::orderBy('created_at', 'desc')
                         ->limit(5)
                         ->get();

    if ($latestAssets->isEmpty()) {
        echo "未找到资产记录\n";
        exit(1);
    }

    echo "找到 " . $latestAssets->count() . " 个最新资产\n\n";

    // 分析相关主体数据格式
    echo "2. 分析相关主体数据格式...\n\n";

    foreach ($latestAssets as $index => $asset) {
        echo "资产 " . ($index + 1) . ":\n";
        echo "  ID: {$asset->id}\n";
        echo "  名称: {$asset->name}\n";
        echo "  品牌: {$asset->brand}\n";
        echo "  创建时间: {$asset->created_at}\n";

        // 解析相关主体数据
        $relatedEntities = is_string($asset->related_entities)
            ? json_decode($asset->related_entities, true)
            : $asset->related_entities;

        if (empty($relatedEntities)) {
            echo "  相关主体: 无\n";
        } else {
            echo "  相关主体数据:\n";
            echo "  原始JSON: " . (is_string($asset->related_entities) ? $asset->related_entities : json_encode($asset->related_entities)) . "\n";
            echo "  解析后数据:\n";

            foreach ($relatedEntities as $entityIndex => $entityData) {
                echo "    主体 " . ($entityIndex + 1) . ":\n";

                // 显示所有字段
                foreach ($entityData as $key => $value) {
                    $displayValue = is_null($value) ? 'null' : (string)$value;
                    echo "      {$key}: {$displayValue}\n";
                }

                // 验证主体是否存在
                if (isset($entityData['entity_id'])) {
                    $entity = Entity::find($entityData['entity_id']);
                    if ($entity) {
                        echo "      [验证] 主体存在: {$entity->name} (类型: {$entity->entity_type})\n";

                        // 查找对应的联系人
                        if (!empty($entityData['contact_name'])) {
                            $contact = EntityContact::where('entity_id', $entity->id)
                                                  ->where('name', $entityData['contact_name'])
                                                  ->first();
                            if ($contact) {
                                echo "      [验证] 联系人存在: {$contact->name} (电话: {$contact->phone})\n";
                            } else {
                                echo "      [警告] 联系人不存在: {$entityData['contact_name']}\n";
                            }
                        }
                    } else {
                        echo "      [错误] 主体不存在: ID {$entityData['entity_id']}\n";
                    }
                }

                echo "\n";
            }
        }

        echo str_repeat("-", 60) . "\n\n";
    }

    // 生成标准格式示例
    echo "3. 生成标准格式示例...\n\n";

    $standardFormat = [];

    if (!$latestAssets->isEmpty()) {
        $sampleAsset = $latestAssets->first();
        $relatedEntities = is_string($sampleAsset->related_entities)
            ? json_decode($sampleAsset->related_entities, true)
            : $sampleAsset->related_entities;

        if (!empty($relatedEntities)) {
            foreach ($relatedEntities as $entityData) {
                $standardFormat[] = [
                    'position' => $entityData['position'] ?? null,
                    'entity_id' => $entityData['entity_id'] ?? null,
                    'department' => $entityData['department'] ?? null,
                    'entity_type' => $entityData['entity_type'] ?? null,
                    'contact_name' => $entityData['contact_name'] ?? null,
                    'contact_phone' => $entityData['contact_phone'] ?? null,
                ];
            }
        }
    }

    echo "标准格式示例:\n";
    echo json_encode($standardFormat, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "\n\n";

    // 对比用户提供的格式
    echo "4. 对比用户提供的格式...\n\n";

    $userProvidedFormat = [
        [
            "position" => "运维工程师",
            "entity_id" => 134,
            "department" => null,
            "entity_type" => "manufacturer",
            "contact_name" => "孙八(制造)",
            "contact_phone" => "13896950648"
        ],
        [
            "position" => "产品经理",
            "entity_id" => 131,
            "department" => null,
            "entity_type" => "supplier",
            "contact_name" => "蒋十七(销售)",
            "contact_phone" => "13926669151"
        ],
        [
            "position" => "产品经理",
            "entity_id" => 108,
            "department" => null,
            "entity_type" => "service_provider",
            "contact_name" => "沈十八(服务)",
            "contact_phone" => "13714065469"
        ],
        [
            "position" => "客服专员",
            "entity_id" => 128,
            "department" => null,
            "entity_type" => "service_provider",
            "contact_name" => "王十二(服务)",
            "contact_phone" => "13787013845"
        ]
    ];

    echo "用户提供的格式:\n";
    echo json_encode($userProvidedFormat, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "\n\n";

    // 格式对比分析
    echo "5. 格式对比分析...\n\n";

    $currentFields = !empty($standardFormat) ? array_keys($standardFormat[0]) : [];
    $expectedFields = array_keys($userProvidedFormat[0]);

    echo "当前格式字段: " . implode(', ', $currentFields) . "\n";
    echo "期望格式字段: " . implode(', ', $expectedFields) . "\n";

    $missingFields = array_diff($expectedFields, $currentFields);
    $extraFields = array_diff($currentFields, $expectedFields);

    if (empty($missingFields) && empty($extraFields)) {
        echo "✓ 格式完全匹配！\n";
    } else {
        if (!empty($missingFields)) {
            echo "⚠ 缺少字段: " . implode(', ', $missingFields) . "\n";
        }
        if (!empty($extraFields)) {
            echo "⚠ 多余字段: " . implode(', ', $extraFields) . "\n";
        }
    }

    // 数据类型检查
    echo "\n6. 数据类型检查...\n\n";

    if (!empty($standardFormat)) {
        foreach ($expectedFields as $field) {
            $expectedType = gettype($userProvidedFormat[0][$field]);
            $actualType = isset($standardFormat[0][$field]) ? gettype($standardFormat[0][$field]) : 'missing';

            if ($expectedType === $actualType) {
                echo "✓ {$field}: {$expectedType}\n";
            } else {
                echo "⚠ {$field}: 期望 {$expectedType}, 实际 {$actualType}\n";
            }
        }
    }

    echo "\n=== 测试完成 ===\n";

} catch (Exception $e) {
    echo "✗ 测试失败：" . $e->getMessage() . "\n";
    echo "错误位置：" . $e->getFile() . ":" . $e->getLine() . "\n";
}

echo "\n=== 测试结束 ===\n";
