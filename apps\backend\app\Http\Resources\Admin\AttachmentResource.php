<?php

namespace App\Http\Resources\Admin;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use function App\Support\string_to_timestamp;

class AttachmentResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        $data = [
            'id' => (string) $this->id,
            'file_name' => $this->file_name,
            'file_path' => $this->file_path,
            'file_size' => $this->file_size,
            'mime_type' => $this->mime_type,
            'storage_type' => $this->storage_type,
            'md5_hash' => $this->md5_hash,
            'file_url' => $this->file_url,
            'formatted_file_size' => $this->formatted_file_size,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ];

        // 如果有关联信息，添加第一个关联的信息
        if ($this->relationLoaded('attachables') && $this->attachables->isNotEmpty()) {
            $firstRelation = $this->attachables->first();
            $data['attachable_type'] = $firstRelation->attachable_type;
            $data['attachable_id'] = (string) $firstRelation->attachable_id;
            $data['category'] = $firstRelation->category;
            $data['description'] = $firstRelation->description;
        } else {
            // 为了前端兼容，提供默认值
            $data['attachable_type'] = null;
            $data['attachable_id'] = null;
            $data['category'] = null;
            $data['description'] = null;
        }

        return $data;
    }
}
