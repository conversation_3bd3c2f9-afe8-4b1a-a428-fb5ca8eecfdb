<template>
  <div class="category-page art-full-height art-page-view">
    <ElCard shadow="never" class="art-table-card">
      <!-- 表格头部 -->
      <ArtTableHeader v-model:columns="columnChecks" @refresh="refreshAll">
        <template #left>
          <ElButton v-if="hasAuth('add')" type="primary" @click="() => handleAdd()">
            <ElIcon><Plus /></ElIcon>
            新增分类
          </ElButton>
          <ElButton @click="() => handleExpandAll()">
            <ElIcon><Expand /></ElIcon>
            {{ expandAll ? '全部收起' : '全部展开' }}
          </ElButton>
        </template>
      </ArtTableHeader>

      <!-- 分类树形表格 -->
      <ArtTable
        ref="tableRef"
        :loading="isLoading"
        :data="tableData"
        :columns="columns"
        :table-config="{ rowKey: 'id' }"
        :layout="{ marginTop: 10 }"
      >
        <!-- 状态列插槽 -->
        <template #status="{ row }">
          <ElTag :type="row.status === 1 ? 'success' : 'danger'">
            {{ row.status === 1 ? '启用' : '禁用' }}
          </ElTag>
        </template>

        <!-- 子级数量列插槽 -->
        <template #children_count="{ row }">
          <ElTag type="info">{{ row.children_count || 0 }}</ElTag>
        </template>

        <!-- 操作列插槽 -->
        <template #operation="{ row }">
          <div style="display: flex; gap: 5px">
            <ArtButtonTable
              v-if="hasAuth('add')"
              type="add"
              text="新增子级"
              @click="handleAdd(row)"
            />
            <ArtButtonTable v-if="hasAuth('edit')" type="edit" @click="handleEdit(row)" />
            <ArtButtonTable v-if="hasAuth('delete')" type="delete" @click="handleDelete(row)" />
          </div>
        </template>
      </ArtTable>

      <!-- 分类编辑对话框 -->
      <CategoryDialog
        v-model="dialogVisible"
        :title="dialogTitle"
        :is-edit="isEdit"
        :category-data="currentCategory"
        :category-options="flatCategoryOptions"
        @success="handleDialogSuccess"
      />
    </ElCard>
  </div>
</template>

<script setup lang="ts">
  defineOptions({ name: 'Category' })

  // Vue 核心
  import { ref, computed, nextTick } from 'vue'

  // UI 框架
  import { ElMessage, ElMessageBox, ElTag } from 'element-plus'
  import { Plus, Expand } from '@element-plus/icons-vue'

  // 工具函数
  import { formatDate } from '@/utils/dataprocess/format'

  // 内部 hooks
  import { useTable } from '@/composables/useTable'
  import { useAuth } from '@/composables/useAuth'

  // 内部组件
  import ArtButtonTable from '@/components/custom/art-button-table/index.vue'
  import CategoryDialog from './components/CategoryDialog.vue'

  // API
  import { getCategoryList, deleteCategory } from '@/api/admin/categoryApi'

  // 类型定义
  import type { Category, CategoryForm } from '@/types/api'

  // 对话框相关状态
  const dialogVisible = ref(false)
  const dialogTitle = ref('')
  const isEdit = ref(false)
  const currentCategory = ref<CategoryForm | undefined>()
  const tableRef = ref()
  const expandAll = ref(false)

  // 权限控制
  const { hasAuth } = useAuth()

  // 使用 useTable 管理表格
  const {
    tableData,
    isLoading,
    columns,
    columnChecks,
    refreshAll,
    refreshAfterCreate,
    refreshAfterUpdate,
    refreshAfterRemove
  } = useTable<Category>({
    core: {
      apiFn: async () => {
        const data = await getCategoryList() // getCategoryList 已经返回树形结构
        // 处理树形表格的展开/收起
        nextTick(() => {
          if (tableRef.value && tableRef.value.expandAll) {
            if (expandAll.value) {
              tableRef.value.expandAll()
            } else {
              tableRef.value.collapseAll()
            }
          }
        })
        return {
          records: data,
          total: data.length,
          current: 1,
          size: data.length
        }
      },
      columnsFactory: () => [
        {
          prop: 'name',
          label: '分类名称',
          minWidth: 200
        },
        {
          prop: 'code',
          label: '分类代码',
          width: 150
        },
        {
          prop: 'level',
          label: '层级',
          width: 80
        },
        {
          prop: 'sort',
          label: '排序',
          width: 80
        },
        {
          prop: 'status',
          label: '状态',
          width: 100,
          useSlot: true
        },
        {
          prop: 'children_count',
          label: '子级数量',
          width: 100,
          useSlot: true
        },
        {
          prop: 'remark',
          label: '备注',
          minWidth: 200,
          showOverflowTooltip: true
        },
        {
          prop: 'created_at',
          label: '创建时间',
          width: 180,
          formatter: (row) =>
            row.created_at ? formatDate(row.created_at, 'YYYY-MM-DD HH:mm:ss') : ''
        },
        {
          prop: 'operation',
          label: '操作',
          width: 250,
          fixed: 'right',
          useSlot: true
        }
      ]
    }
  })

  // 构建扁平化的分类选项（替代树形选择器）
  const flatCategoryOptions = computed(() => {
    const options: Array<{
      id: number
      displayName: string
      disabled: boolean
    }> = []

    // 获取某个分类的所有子孙ID
    const getDescendantIds = (categoryId: number): Set<number> => {
      const ids = new Set<number>()

      const collectIds = (data: Category[]) => {
        data.forEach((item) => {
          if (
            item.parent_id === categoryId ||
            (item.parent_id !== null && ids.has(item.parent_id))
          ) {
            ids.add(item.id)
            if (item.children && item.children.length > 0) {
              collectIds(item.children)
            }
          }
        })
      }

      collectIds(tableData.value)
      return ids
    }

    // 如果是编辑模式，获取当前分类及其所有子孙的ID
    const disabledIds = new Set<number>()
    if (isEdit.value && currentCategory.value && currentCategory.value.id) {
      disabledIds.add(currentCategory.value.id) // 禁用自己
      const descendantIds = getDescendantIds(currentCategory.value.id)
      descendantIds.forEach((id) => disabledIds.add(id)) // 禁用所有子孙
    }

    const flattenCategory = (data: Category[], level = 0) => {
      data.forEach((item) => {
        const prefix = '　'.repeat(level) // 使用全角空格表示层级
        const disabled = disabledIds.has(item.id)

        options.push({
          id: item.id,
          displayName: `${prefix}${item.name}`,
          disabled
        })

        if (item.children && item.children.length > 0) {
          flattenCategory(item.children, level + 1)
        }
      })
    }

    flattenCategory(tableData.value, 0)
    return options
  })

  // 展开/收起全部
  const handleExpandAll = () => {
    expandAll.value = !expandAll.value

    if (tableRef.value) {
      if (expandAll.value) {
        tableRef.value.expandAll()
      } else {
        tableRef.value.collapseAll()
      }
    }
  }

  // 新增分类
  const handleAdd = (parent?: Category) => {
    dialogTitle.value = parent ? `新增"${parent.name}"的子级分类` : '新增分类'
    isEdit.value = false

    if (parent) {
      // 点击表格行的"新增子级"按钮
      currentCategory.value = {
        name: '',
        code: '',
        parent_id: parent.id,
        sort: 0,
        status: 1,
        remark: ''
      }
    } else {
      // 点击顶部的"新增分类"按钮
      currentCategory.value = {
        name: '',
        code: '',
        parent_id: 0,
        sort: 0,
        status: 1,
        remark: ''
      }
    }
    dialogVisible.value = true
  }

  // 编辑分类
  const handleEdit = (row: Category) => {
    dialogTitle.value = '编辑分类'
    isEdit.value = true
    // 转换为表单数据
    currentCategory.value = {
      id: row.id,
      name: row.name,
      code: row.code,
      parent_id: row.parent_id,
      sort: row.sort,
      status: row.status,
      remark: row.remark
    }
    dialogVisible.value = true
  }

  // 删除分类
  const handleDelete = async (row: Category) => {
    if (row.children_count && row.children_count > 0) {
      ElMessage.warning('该分类下存在子级分类，无法删除')
      return
    }

    try {
      await ElMessageBox.confirm(`确定要删除分类"${row.name}"吗？`, '删除确认', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })

      await deleteCategory(row.id)
      ElMessage.success('删除成功')
      refreshAfterRemove()
    } catch {
      // 用户取消删除，不需要处理
    }
  }

  // 对话框成功回调
  const handleDialogSuccess = () => {
    if (isEdit.value) {
      refreshAfterUpdate()
    } else {
      refreshAfterCreate()
    }
  }
</script>

<style lang="scss" scoped>
  .category-page {
    // 样式已移至组件
  }
</style>
