<?php

/**
 * 详细分析Excel导入模板结构
 */

require_once __DIR__ . '/vendor/autoload.php';

use PhpOffice\PhpSpreadsheet\IOFactory;
use PhpOffice\PhpSpreadsheet\Cell\Coordinate;

echo "=== 详细分析Excel导入模板 ===\n\n";

try {
    $templatePath = 'storage/app/public/资产导入模板_2025-08-14_10-26-23.xlsx';
    $fullPath = __DIR__ . '/' . $templatePath;

    if (!file_exists($fullPath)) {
        echo "模板文件不存在: {$fullPath}\n";
        exit(1);
    }

    echo "模板文件: {$templatePath}\n";
    echo "文件大小: " . number_format(filesize($fullPath) / 1024, 2) . " KB\n\n";

    // 使用PhpSpreadsheet读取
    $spreadsheet = IOFactory::load($fullPath);

    // 获取所有工作表
    $worksheetCount = $spreadsheet->getSheetCount();
    echo "工作表数量: {$worksheetCount}\n\n";

    for ($sheetIndex = 0; $sheetIndex < $worksheetCount; $sheetIndex++) {
        $worksheet = $spreadsheet->getSheet($sheetIndex);
        $sheetName = $worksheet->getTitle();

        echo "=== 工作表 " . ($sheetIndex + 1) . ": {$sheetName} ===\n";

        // 获取数据范围
        $highestRow = $worksheet->getHighestDataRow();
        $highestColumn = $worksheet->getHighestDataColumn();
        $highestColumnIndex = Coordinate::columnIndexFromString($highestColumn);

        echo "数据范围: A1:{$highestColumn}{$highestRow}\n";
        echo "行数: {$highestRow}, 列数: {$highestColumnIndex}\n\n";

        // 读取所有数据
        echo "完整数据内容:\n";
        for ($row = 1; $row <= min($highestRow, 10); $row++) { // 最多显示10行
            echo "第 {$row} 行:\n";
            for ($col = 1; $col <= min($highestColumnIndex, 20); $col++) { // 最多显示20列
                $cellValue = $worksheet->getCellByColumnAndRow($col, $row)->getCalculatedValue();
                $columnLetter = Coordinate::stringFromColumnIndex($col);

                if (!empty($cellValue)) {
                    echo "  {$columnLetter}: {$cellValue}\n";
                }
            }
            echo "\n";
        }

        // 如果行数太多，显示省略信息
        if ($highestRow > 10) {
            echo "... 还有 " . ($highestRow - 10) . " 行数据\n\n";
        }

        // 尝试找到标题行
        echo "寻找标题行:\n";
        $possibleHeaderRows = [];

        for ($row = 1; $row <= min($highestRow, 5); $row++) {
            $nonEmptyCount = 0;
            $cellValues = [];

            for ($col = 1; $col <= $highestColumnIndex; $col++) {
                $cellValue = $worksheet->getCellByColumnAndRow($col, $row)->getCalculatedValue();
                if (!empty($cellValue)) {
                    $nonEmptyCount++;
                    $cellValues[] = $cellValue;
                }
            }

            if ($nonEmptyCount > 3) { // 如果有超过3个非空单元格，可能是标题行
                $possibleHeaderRows[$row] = [
                    'count' => $nonEmptyCount,
                    'values' => array_slice($cellValues, 0, 10) // 只显示前10个
                ];
            }
        }

        if (!empty($possibleHeaderRows)) {
            echo "可能的标题行:\n";
            foreach ($possibleHeaderRows as $rowNum => $info) {
                echo "  第 {$rowNum} 行 ({$info['count']} 个字段): " . implode(', ', $info['values']) . "\n";
            }
        } else {
            echo "未找到明显的标题行\n";
        }

        echo "\n" . str_repeat("-", 50) . "\n\n";
    }

    // 尝试使用Laravel Excel读取
    echo "=== 使用Laravel Excel读取 ===\n";

    $app = require_once __DIR__ . '/bootstrap/app.php';
    $app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

    class TemplateReader
    {
        // 空的读取器类
    }

    try {
        $data = \Maatwebsite\Excel\Facades\Excel::toArray(new TemplateReader(), $fullPath);

        echo "Laravel Excel 读取结果:\n";
        echo "工作表数量: " . count($data) . "\n\n";

        foreach ($data as $sheetIndex => $sheetData) {
            echo "工作表 " . ($sheetIndex + 1) . ":\n";
            echo "行数: " . count($sheetData) . "\n";

            if (!empty($sheetData)) {
                echo "列数: " . count($sheetData[0]) . "\n";

                // 显示前几行数据
                foreach (array_slice($sheetData, 0, 5) as $rowIndex => $row) {
                    echo "第 " . ($rowIndex + 1) . " 行: " . implode(' | ', array_slice($row, 0, 10)) . "\n";
                }
            }
            echo "\n";
        }

    } catch (Exception $e) {
        echo "Laravel Excel 读取失败: " . $e->getMessage() . "\n";
    }

} catch (Exception $e) {
    echo "✗ 分析失败：" . $e->getMessage() . "\n";
    echo "错误位置：" . $e->getFile() . ":" . $e->getLine() . "\n";
    echo "堆栈跟踪：\n" . $e->getTraceAsString() . "\n";
}

echo "\n=== 详细分析结束 ===\n";
