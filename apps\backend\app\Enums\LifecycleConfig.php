<?php

namespace App\Enums;

/**
 * 全生命周期配置选项
 *
 * 此文件由 php artisan dictionary:generate-enums 命令自动生成
 * 请勿手动修改，如需更改请在字典管理中修改后重新生成
 *
 * @generated
 */
enum LifecycleConfig: string
{
    case INSTALLATION = 'installation';
    case MAINTENANCE = 'maintenance';
    case TRAINING = 'training';
    case REPAIR = 'repair';
    case AFTER_SALES_SERVICE = 'after_sales_service';

    /**
     * 获取枚举对应的中文标签
     */
    public function label(): string
    {
        return match ($this) {
            self::INSTALLATION => '安装',
            self::MAINTENANCE => '维护',
            self::TRAINING => '培训',
            self::REPAIR => '维修',
            self::AFTER_SALES_SERVICE => '售后部',
        };
    }

    /**
     * 根据值获取枚举实例
     */
    public static function tryFromValue(string $value): ?self
    {
        return self::tryFrom($value);
    }

    /**
     * 检查值是否有效
     */
    public static function isValid(string $value): bool
    {
        return self::tryFrom($value) !== null;
    }

    /**
     * 获取所有枚举值
     */
    public static function values(): array
    {
        return array_column(self::cases(), 'value');
    }
}
