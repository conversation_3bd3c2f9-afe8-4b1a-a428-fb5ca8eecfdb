<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Relations\BelongsTo;
use App\Models\BaseModel;

/**
 * @property int $id
 * @property int $role_id 角色ID
 * @property int $menu_id 菜单ID
 * @property int|null $menu_permission_id 菜单权限ID，为空表示只有菜单访问权限
 * @property \Illuminate\Support\Carbon|null $created_at 创建时间
 * @property \Illuminate\Support\Carbon|null $updated_at 更新时间
 * @property-read \App\Models\Menu $menu
 * @property-read \App\Models\MenuPermission|null $menuPermission
 * @property-read \App\Models\Role $role
 * @method static \Illuminate\Database\Eloquent\Builder<static>|RoleMenuPermission newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|RoleMenuPermission newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|RoleMenuPermission query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|RoleMenuPermission whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|RoleMenuPermission whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|RoleMenuPermission whereMenuId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|RoleMenuPermission whereMenuPermissionId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|RoleMenuPermission whereRoleId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|RoleMenuPermission whereUpdatedAt($value)
 * @mixin \Eloquent
 */
class RoleMenuPermission extends BaseModel
{
    protected $fillable = [
        'role_id',
        'menu_id',
        'menu_permission_id',
    ];

    /**
     * 关联角色
     */
    public function role(): BelongsTo
    {
        return $this->belongsTo(Role::class);
    }

    /**
     * 关联菜单
     */
    public function menu(): BelongsTo
    {
        return $this->belongsTo(Menu::class);
    }

    /**
     * 关联菜单权限
     */
    public function menuPermission(): BelongsTo
    {
        return $this->belongsTo(MenuPermission::class);
    }
}
