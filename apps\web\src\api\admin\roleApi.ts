import request from '@/utils/http'
import type {
  RoleListData,
  RoleSearchParams,
  RoleForm,
  RoleListItem,
  UserRoleAssignParams,
  RolePermissionAssignParams
} from '@/types/api/role'

/**
 * 获取角色列表
 */
export const getRoleList = (params: RoleSearchParams): Promise<RoleListData> => {
  return request.get<RoleListData>({
    url: '/admin/roles',
    params
  })
}

/**
 * 创建角色
 */
export const createRole = (data: RoleForm): Promise<RoleListItem> => {
  return request.post<RoleListItem>({
    url: '/admin/roles',
    data
  })
}

/**
 * 获取角色详情
 */
export const getRoleDetail = (id: number): Promise<RoleListItem> => {
  return request.get<RoleListItem>({
    url: `/admin/roles/${id}`
  })
}

/**
 * 更新角色
 */
export const updateRole = (id: number, data: RoleForm): Promise<RoleListItem> => {
  return request.put<RoleListItem>({
    url: `/admin/roles/${id}`,
    data
  })
}

/**
 * 删除角色
 */
export const deleteRole = (id: number): Promise<void> => {
  return request.del<void>({
    url: `/admin/roles/${id}`
  })
}

/**
 * 为用户分配角色（追加式）
 */
export const assignUserRoles = (userId: number, data: UserRoleAssignParams): Promise<void> => {
  return request.post<void>({
    url: `/admin/users/${userId}/roles/assign`,
    data
  })
}

/**
 * 同步用户角色（覆盖式）
 */
export const syncUserRoles = (userId: number, data: UserRoleAssignParams): Promise<void> => {
  return request.put<void>({
    url: `/admin/users/${userId}/roles/sync`,
    data
  })
}

/**
 * 移除用户角色
 */
export const removeUserRoles = (userId: number, data: UserRoleAssignParams): Promise<void> => {
  return request.del<void>({
    url: `/admin/users/${userId}/roles/remove`,
    data
  })
}

/**
 * 更新角色的菜单权限
 */
export const updateRolePermissions = (
  roleId: number,
  data: RolePermissionAssignParams
): Promise<void> => {
  return request.post<void>({
    url: `/admin/roles/${roleId}/menu-permissions/assign`,
    data: {
      permissions: data.menus // 转换为后端期望的格式
    }
  })
}
