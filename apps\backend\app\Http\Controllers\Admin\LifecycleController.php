<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\LifecycleRequest;
use App\Http\Resources\Admin\LifecycleResource;
use App\Services\LifecycleService;
use Illuminate\Http\Request;

/**
 * @group 生命周期管理
 *
 * 管理设备生命周期记录
 */
class LifecycleController extends Controller
{
    public function __construct(
        private LifecycleService $lifecycleService
    ) {}

    /**
     * 获取生命周期列表
     *
     * @queryParam page int 页码 Example: 1
     * @queryParam per_page integer 每页数量. Example: 20
     * @queryParam asset_id string 资产ID筛选. Example: AST-2024-001
     * @queryParam type string 类型筛选. Example: installation
     * @queryParam start_date string 开始日期筛选. Example: 2024-01-01
     * @queryParam end_date string 结束日期筛选. Example: 2024-12-31
     * @queryParam initiator_id integer 发起人ID筛选. Example: 1
     * @queryParam acceptance_entity_id integer 验收主体ID筛选. Example: 1
     *
     * @apiResourceCollection App\Http\Resources\Admin\LifecycleResource
     *
     * @apiResourceModel App\Models\Lifecycle paginate=20 with=initiator:id,nickname;assistants:id,nickname;acceptanceEntity:id,name;acceptancePersonnel:id,name;attachments
     */
    public function index(Request $request)
    {
        $perPage = $request->get('per_page', 20);
        $filters = $request->only([
            'asset_id',
            'type',
            'start_date',
            'end_date',
            'initiator_id',
            'acceptance_entity_id',
        ]);

        $lifecycles = $this->lifecycleService->paginate($filters, $perPage);

        return LifecycleResource::collection($lifecycles);
    }

    /**
     * 获取生命周期详情
     *
     * @urlParam id integer required 生命周期ID. Example: 1
     */
    public function show(int $id)
    {
        $lifecycle = $this->lifecycleService->find($id);

        if (! $lifecycle) {
            return $this->error('生命周期记录不存在', 404);
        }

        return new LifecycleResource($lifecycle);
    }

    /**
     * 创建生命周期
     *
     * @bodyParam asset_id integer 资产ID. Example: 1
     * @bodyParam type string required 类型（字典值）. Example: installation
     * @bodyParam date string required 日期. Example: 2024-01-15
     * @bodyParam initiator_id integer required 发起人ID. Example: 1
     * @bodyParam content string required 内容. Example: 采购了一批新的服务器设备
     * @bodyParam assistants integer[] required 协助人员ID数组. Example: [2, 3]
     * @bodyParam acceptance_entity_id integer required 验收主体ID. Example: 1
     * @bodyParam acceptance_personnel_id integer required 验收人员ID. Example: 1
     * @bodyParam acceptance_time string required 验收时间. Example: 2024-01-20 14:30:00
     * @bodyParam attachments integer[] 附件ID数组. Example: [1, 2, 3]
     */
    public function store(LifecycleRequest $request)
    {
        $lifecycle = $this->lifecycleService->create($request->validated());

        return (new LifecycleResource($lifecycle))->response()->setStatusCode(201);
    }

    /**
     * 更新生命周期
     *
     * @urlParam id integer required 生命周期ID. Example: 1
     *
     * @bodyParam asset_id integer 资产ID. Example: 1
     * @bodyParam type string 类型（字典值）. Example: installation
     * @bodyParam date string 日期. Example: 2024-01-15
     * @bodyParam initiator_id integer 发起人ID. Example: 1
     * @bodyParam content string 内容. Example: 采购了一批新的服务器设备
     * @bodyParam assistants integer[] 协助人员ID数组. Example: [2, 3]
     * @bodyParam acceptance_entity_id integer 验收主体ID. Example: 1
     * @bodyParam acceptance_personnel_id integer 验收人员ID. Example: 1
     * @bodyParam acceptance_time string 验收时间. Example: 2024-01-20 14:30:00
     * @bodyParam attachments integer[] 附件ID数组. Example: [1, 2, 3]
     */
    public function update(LifecycleRequest $request, int $id)
    {
        $lifecycle = $this->lifecycleService->find($id);

        if (! $lifecycle) {
            return $this->error('生命周期记录不存在', 404);
        }

        $lifecycle = $this->lifecycleService->update($lifecycle, $request->validated());

        return new LifecycleResource($lifecycle);
    }

    /**
     * 删除生命周期
     *
     * @urlParam id integer required 生命周期ID. Example: 1
     */
    public function destroy(int $id)
    {
        $lifecycle = $this->lifecycleService->find($id);

        if (! $lifecycle) {
            return $this->error('生命周期记录不存在', 404);
        }

        $this->lifecycleService->delete($lifecycle);

        return response()->noContent();
    }

    /**
     * 获取验收人员列表
     *
     * @urlParam entityId integer required 验收主体ID. Example: 1
     */
    public function getAcceptancePersonnel(int $entityId)
    {
        $personnel = $this->lifecycleService->getAcceptancePersonnel($entityId);

        return response()->json($personnel);
    }
}
