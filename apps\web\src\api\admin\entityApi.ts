import request from '@/utils/http'
import type { Entity, EntityForm, EntitySearchParams, EntityPageResponse } from '@/types/api/entity'

/**
 * 获取主体分页列表
 */
export const getEntityList = (params: EntitySearchParams = {}): Promise<EntityPageResponse> => {
  return request.get<EntityPageResponse>({
    url: '/admin/entities',
    params: {
      ...params,
      per_page: params.size || params.per_page || 20,
      page: params.current || params.page || 1
    }
  })
}

/**
 * 获取主体详情
 */
export const getEntityDetail = (id: string): Promise<Entity> => {
  return request.get<Entity>({
    url: `/admin/entities/${id}`
  })
}

/**
 * 创建主体
 */
export const createEntity = (data: EntityForm): Promise<Entity> => {
  return request.post<Entity>({
    url: '/admin/entities',
    data
  })
}

/**
 * 更新主体
 */
export const updateEntity = (id: string, data: EntityForm): Promise<Entity> => {
  return request.put<Entity>({
    url: `/admin/entities/${id}`,
    data
  })
}

/**
 * 删除主体
 */
export const deleteEntity = (id: string): Promise<void> => {
  return request.del<void>({
    url: `/admin/entities/${id}`
  })
}
