<?php

namespace App\Http\Resources\Admin;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use function App\Support\string_to_timestamp;

class DictionaryItemResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'category_id' => $this->category_id,
            'code' => $this->code,
            'value' => $this->value,
            'label' => $this->label,
            'sort' => $this->sort,
            'color' => $this->color,
            'icon' => $this->icon,
            'config' => $this->config,
            'remark' => $this->remark,
            'is_enabled' => $this->is_enabled,
            'category' => new DictionaryCategoryResource($this->whenLoaded('category')),
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ];
    }
}
