<?php

require_once __DIR__ . '/vendor/autoload.php';

$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "=== 检查最新创建的主体 ===\n\n";

$entities = \App\Models\Entity::orderBy('created_at', 'desc')->limit(20)->get();

foreach ($entities as $entity) {
    echo "ID: {$entity->id}, 名称: '{$entity->name}', 类型: {$entity->entity_type}\n";
}

echo "\n=== 检查完成 ===\n";
