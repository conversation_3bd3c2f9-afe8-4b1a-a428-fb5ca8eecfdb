<?php

namespace App\Console\Commands;

use App\Models\DictionaryCategory;
use App\Models\DictionaryItem;
use Illuminate\Console\Command;
use Illuminate\Support\Str;

class GenerateDictionaryEnums extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'dictionary:generate-enums {--category=* : 指定要生成的分类code，不指定则生成所有}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '从字典表生成对应的枚举类';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $categories = $this->option('category');

        if (empty($categories)) {
            // 生成所有字典分类
            $query = DictionaryCategory::where('is_enabled', true);
        } else {
            // 生成指定的字典分类
            $query = DictionaryCategory::where('is_enabled', true)
                ->whereIn('code', $categories);
        }

        $dictionaryCategories = $query->get();

        if ($dictionaryCategories->isEmpty()) {
            $this->error('没有找到需要生成的字典分类');

            return Command::FAILURE;
        }

        $this->info('开始生成字典枚举...');

        foreach ($dictionaryCategories as $category) {
            $this->generateEnum($category);
        }

        $this->info('字典枚举生成完成！');

        return Command::SUCCESS;
    }

    /**
     * 生成单个字典分类的枚举
     */
    protected function generateEnum(DictionaryCategory $category): void
    {
        $this->info("正在生成字典分类: {$category->name} ({$category->code})");

        // 获取该分类下所有启用的字典项
        $items = DictionaryItem::where('category_id', $category->id)
            ->where('is_enabled', true)
            ->orderBy('sort')
            ->get();

        if ($items->isEmpty()) {
            $this->warn('  - 该分类下没有启用的字典项，跳过生成');

            return;
        }

        // 生成枚举类名
        $enumName = $this->generateEnumName($category->code);
        $enumPath = app_path("Enums/{$enumName}.php");

        // 生成枚举内容
        $content = $this->generateEnumContent($enumName, $category, $items);

        // 确保目录存在
        $dir = dirname($enumPath);
        if (! is_dir($dir)) {
            mkdir($dir, 0755, true);
        }

        // 写入文件
        file_put_contents($enumPath, $content);
        $this->info("  - 已生成: {$enumPath}");
    }

    /**
     * 根据字典分类code生成枚举类名
     */
    protected function generateEnumName(string $code): string
    {
        // 将下划线转换为驼峰命名
        return Str::studly($code);
    }

    /**
     * 生成枚举类的内容
     */
    protected function generateEnumContent(string $enumName, DictionaryCategory $category, $items): string
    {
        $cases = '';
        $labels = '';

        foreach ($items as $item) {
            // 生成case名称（转换为大写+下划线）
            // 特殊处理包含反斜杠的代码（如模型类名）
            $caseCode = str_replace('\\', '_', $item->code);
            $caseName = Str::upper(Str::snake($caseCode));

            // 生成case定义
            $cases .= "    case {$caseName} = '{$item->code}';\n";

            // 生成label映射
            $labels .= "            self::{$caseName} => '{$item->label}',\n";
        }

        // 生成完整的枚举类内容
        $content = <<<PHP
<?php

namespace App\Enums;

/**
 * {$category->name}
 *
 * 此文件由 php artisan dictionary:generate-enums 命令自动生成
 * 请勿手动修改，如需更改请在字典管理中修改后重新生成
 *
 * @generated
 */
enum {$enumName}: string
{
{$cases}
    /**
     * 获取枚举对应的中文标签
     */
    public function label(): string
    {
        return match (\$this) {
{$labels}        };
    }

    /**
     * 根据值获取枚举实例
     */
    public static function tryFromValue(string \$value): ?self
    {
        return self::tryFrom(\$value);
    }

    /**
     * 检查值是否有效
     */
    public static function isValid(string \$value): bool
    {
        return self::tryFrom(\$value) !== null;
    }

    /**
     * 获取所有枚举值
     */
    public static function values(): array
    {
        return array_column(self::cases(), 'value');
    }
}

PHP;

        return $content;
    }

}
