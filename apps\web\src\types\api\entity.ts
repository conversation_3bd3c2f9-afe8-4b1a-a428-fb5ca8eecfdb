// 主体管理相关类型定义

// 主体类型
export interface EntityType {
  code: string
  value: string
  color: string
}

// 使用统一的附件类型
import type { AttachmentItem } from './attachment'
export type { AttachmentItem as Attachment }

// 主体实体
export interface Entity {
  id: string | number
  name: string
  tax_number: string
  address: string
  phone: string
  entity_type: string
  keywords: string
  remark?: string
  contacts?: Contact[]
  contacts_count?: number
  attachments_count?: number
  created_by?: number
  updated_by?: number
  created_at: string
  updated_at: string
  attachments?: AttachmentItem[]
}

// 主体表单
export interface EntityForm {
  id?: string
  name: string
  tax_number: string
  address: string
  phone: string
  entity_type: string
  keywords: string
  remark?: string
  contacts?: Contact[]
  attachments?: number[]
}

// 联系人
export interface Contact {
  id: string
  entity_id: string
  name: string
  phone: string
  position: string
  department: string
  created_at: string
}

// 联系人表单
export interface ContactForm {
  id?: string
  entity_id: string
  name: string
  phone: string
  position: string
  department: string
}

// 主体搜索参数
export interface EntitySearchParams {
  keyword?: string
  entity_type?: string
  current?: number
  size?: number
  page?: number
  per_page?: number
  name?: string
  tax_number?: string
  keywords?: string
}

// 导入通用分页类型
import type { PaginatedResponse } from './pagination'

// 主体分页响应
export type EntityPageResponse = PaginatedResponse<Entity>

// 联系人分页响应
export interface ContactPageResponse {
  data: Contact[]
  total: number
  current: number
  size: number
}

// 主体类型常量
export const ENTITY_TYPES = [
  {
    code: 'manufacturer',
    value: '生产厂',
    color: '#409EFF'
  },
  {
    code: 'supplier',
    value: '供应商',
    color: '#E6A23C'
  },
  {
    code: 'end_customer',
    value: '最终客户',
    color: '#67C23A'
  },
  {
    code: 'service_provider',
    value: '服务商',
    color: '#909399'
  },
  {
    code: 'after_sales',
    value: '售后部',
    color: '#F56C6C'
  }
]
