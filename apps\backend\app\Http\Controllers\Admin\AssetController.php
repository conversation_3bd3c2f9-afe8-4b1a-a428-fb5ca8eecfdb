<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\AssetRequest;
use App\Http\Requests\AssetImportRequest;
use App\Http\Resources\Admin\AssetResource;
use App\Jobs\ProcessAssetImport;
use App\Models\Asset;
use App\Models\AssetImportTask;
use App\Services\AssetService;
use App\Exports\AssetTemplateExport;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Maatwebsite\Excel\Facades\Excel;

/**
 * @group 资产管理
 */
class AssetController extends Controller
{
    private $assetService;

    public function __construct(AssetService $assetService)
    {
        $this->assetService = $assetService;
    }

    /**
     * 获取资产列表
     *
     * @queryParam name string 资产名称搜索 Example: 办公电脑
     * @queryParam brand string 品牌搜索 Example: 联想
     * @queryParam serial_number string 序列号搜索 Example: ABC123456
     * @queryParam keyword string 通用搜索关键词（同时搜索名称、品牌、型号、序列号）Example: 联想
     * @queryParam asset_category_id int 资产分类ID Example: 1
     * @queryParam department_category_id int 科室分类ID Example: 2
     * @queryParam industry_category_id int 行业分类ID Example: 3
     * @queryParam asset_status string 资产状态（字典code） Example: in_use
     * @queryParam asset_condition string 成色（字典code） Example: brand_new
     * @queryParam asset_source string 资产来源（字典code） Example: purchase
     * @queryParam is_accessory boolean 是否附属设备 Example: false
     * @queryParam parent_id int 主设备ID Example: 1
     * @queryParam page int 页码 Example: 1
     * @queryParam per_page int 每页条数 Example: 20
     *
     * @apiResourceCollection App\Http\Resources\Admin\AssetResource
     *
     * @apiResourceModel App\Models\Asset paginate=20
     */
    public function index(Request $request)
    {
        $assets = $this->assetService->paginate($request->all());

        return AssetResource::collection($assets);
    }

    /**
     * 获取可作为主设备的资产列表
     *
     * @queryParam exclude_id int 排除的资产ID（避免自己关联自己） Example: 5
     * @queryParam keyword string 搜索关键词 Example: 电脑
     * @queryParam page int 页码 Example: 1
     * @queryParam per_page int 每页条数 Example: 20
     */
    public function mainAssets(Request $request)
    {
        $assets = $this->assetService->getMainAssets($request->all());

        return AssetResource::collection($assets);
    }

    /**
     * 创建资产
     *
     * @bodyParam name string required 资产名称 Example: 办公台式电脑
     * @bodyParam brand string 品牌 Example: 联想
     * @bodyParam model string 规格型号 Example: ThinkCentre M720
     * @bodyParam serial_number string 序列号 Example: ABC123456789
     * @bodyParam asset_category_ids array 资产分类ID Example: [1,2,3]
     * @bodyParam asset_source string 资产来源（字典code） Example: purchase
     * @bodyParam asset_status string 资产状态（字典code） Example: new_unstocked
     * @bodyParam asset_condition string 成色（字典code） Example: brand_new
     * @bodyParam parent_id int 主设备ID（附属设备时必填） Example: 1
     * @bodyParam is_accessory boolean 是否附属设备 Example: false
     * @bodyParam region_code string 地区代码 Example: 12
     * @bodyParam detailed_address string 详细地址 Example: XX街道XX号XX大厦
     * @bodyParam start_date date 启用日期 Example: 2024-01-01
     * @bodyParam warranty_period int 合同质保期（月） Example: 36
     * @bodyParam warranty_alert int 质保期预警（天） Example: 30
     * @bodyParam maintenance_cycle int 维护周期（天） Example: 90
     * @bodyParam expected_years int 预计使用年限（年） Example: 5
     * @bodyParam related_entities array 相关主体信息 JSON数组 Example: [{"entity_type":"manufacturer","entity_id":1,"contact_name":"张三","contact_phone":"13800138000","position":"产品经理","department":"产品部"}]
     * @bodyParam related_entities[].entity_type string required 主体类型（字典code） Example: manufacturer
     * @bodyParam related_entities[].entity_id int required 主体ID Example: 1
     * @bodyParam related_entities[].contact_name string required 联系人姓名 Example: 张三
     * @bodyParam related_entities[].contact_phone string required 联系电话 Example: 13800138000
     * @bodyParam related_entities[].position string 职位 Example: 产品经理
     * @bodyParam related_entities[].department string 部门 Example: 产品部
     * @bodyParam remark string 备注
     * @bodyParam attachments array 附件ID数组 Example: [1,2,3]
     */
    public function store(AssetRequest $request)
    {
        $asset = $this->assetService->create($request->validated());

        return (new AssetResource($asset))
            ->response()
            ->setStatusCode(201);
    }

    /**
     * 获取资产详情
     *
     * @urlParam asset integer required 资产ID Example: 1
     */
    public function show(Asset $asset)
    {
        // 加载附件关系
        $asset->load(['attachments', 'parent', 'children']);

        return new AssetResource($asset);
    }

    /**
     * 更新资产
     *
     * @urlParam asset integer required 资产ID Example: 1
     * @bodyParam name string required 资产名称 Example: 办公台式电脑
     * @bodyParam brand string 品牌 Example: 联想
     * @bodyParam model string 规格型号 Example: ThinkCentre M720
     * @bodyParam serial_number string 序列号 Example: ABC123456789
     * @bodyParam asset_category_ids array 资产分类ID Example: [1,2,3]
     * @bodyParam asset_source string 资产来源（字典code） Example: purchase
     * @bodyParam asset_status string 资产状态（字典code） Example: new_unstocked
     * @bodyParam asset_condition string 成色（字典code） Example: brand_new
     * @bodyParam parent_id int 主设备ID（附属设备时必填） Example: 1
     * @bodyParam is_accessory boolean 是否附属设备 Example: false
     * @bodyParam region_code string 地区代码 Example: 12
     * @bodyParam detailed_address string 详细地址 Example: XX街道XX号XX大厦
     * @bodyParam start_date date 启用日期 Example: 2024-01-01
     * @bodyParam warranty_period int 合同质保期（月） Example: 36
     * @bodyParam warranty_alert int 质保期预警（天） Example: 30
     * @bodyParam maintenance_cycle int 维护周期（天） Example: 90
     * @bodyParam expected_years int 预计使用年限（年） Example: 5
     * @bodyParam related_entities array 相关主体信息 JSON数组 Example: [{"entity_type":"manufacturer","entity_id":1,"contact_name":"张三","contact_phone":"13800138000","position":"产品经理","department":"产品部"}]
     * @bodyParam related_entities[].entity_type string required 主体类型（字典code） Example: manufacturer
     * @bodyParam related_entities[].entity_id int required 主体ID Example: 1
     * @bodyParam related_entities[].contact_name string required 联系人姓名 Example: 张三
     * @bodyParam related_entities[].contact_phone string required 联系电话 Example: 13800138000
     * @bodyParam related_entities[].position string 职位 Example: 产品经理
     * @bodyParam related_entities[].department string 部门 Example: 产品部
     * @bodyParam remark string 备注
     * @bodyParam attachments array 附件ID数组 Example: [1,2,3]
     */
    public function update(AssetRequest $request, Asset $asset)
    {
        $oldName = $asset->name;
        $asset = $this->assetService->update($asset, $request->validated());

        return new AssetResource($asset);
    }

    /**
     * 删除资产
     *
     * @urlParam asset integer required 资产ID Example: 1
     */
    public function destroy(Asset $asset)
    {
        $assetName = $asset->name;
        $assetId = $asset->id;

        $this->assetService->delete($asset);

        return response()->json(['message' => '资产删除成功']);
    }

    /**
     * 导出资产模板
     *
     * 导出包含所有字段的Excel模板文件，用于资产批量导入
     */
    public function exportTemplate()
    {
        $filename = '资产导入模板_' . date('Y-m-d_H-i-s') . '.xlsx';

        return Excel::download(new AssetTemplateExport(), $filename);
    }

    /**
     * 批量导入资产
     *
     * 上传Excel文件并创建导入任务，通过消息队列异步处理
     *
     * @bodyParam file file required Excel文件 Example: file.xlsx
     */
    public function import(AssetImportRequest $request)
    {
        $file = $request->file('file');

        // 生成唯一的文件名
        $filename = 'asset_imports/' . time() . '_' . $file->getClientOriginalName();

        // 存储文件
        $filePath = $file->storeAs('', $filename, 'local');

        // 创建导入任务
        $importTask = AssetImportTask::create([
            'file_path' => $filePath,
            'original_filename' => $file->getClientOriginalName(),
            'status' => 'pending',
            'created_by' => auth()->id(),
        ]);

        // 分发到队列
        ProcessAssetImport::dispatch($importTask);

        return response()->json([
            'message' => '导入任务已创建，正在后台处理',
            'task_id' => $importTask->id,
        ], 201);
    }

    /**
     * 获取导入任务状态
     *
     * @urlParam task integer required 导入任务ID Example: 1
     */
    public function importStatus(AssetImportTask $task)
    {
        return response()->json([
            'id' => $task->id,
            'status' => $task->status,
            'original_filename' => $task->original_filename,
            'total_rows' => $task->total_rows,
            'success_rows' => $task->success_rows,
            'failed_rows' => $task->failed_rows,
            'error_details' => $task->error_details,
            'summary' => $task->summary,
            'started_at' => $task->started_at,
            'completed_at' => $task->completed_at,
            'created_at' => $task->created_at,
        ]);
    }

    /**
     * 获取导入任务列表
     *
     * @queryParam status string 任务状态 Example: completed
     * @queryParam page int 页码 Example: 1
     * @queryParam per_page int 每页条数 Example: 20
     */
    public function importTasks(Request $request)
    {
        $query = AssetImportTask::query()->with('creator');

        // 状态筛选
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // 按创建时间倒序
        $query->orderBy('created_at', 'desc');

        $tasks = $query->paginate($request->get('per_page', 20));

        return response()->json($tasks);
    }
}
