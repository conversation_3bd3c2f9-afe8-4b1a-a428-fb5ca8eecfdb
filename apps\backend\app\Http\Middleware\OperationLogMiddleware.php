<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use App\Services\OperationLogService;

class OperationLogMiddleware
{
    private $operationLogService;

    public function __construct(OperationLogService $operationLogService)
    {
        $this->operationLogService = $operationLogService;
    }

    /**
     * 处理传入的请求
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next)
    {
        // 先让请求继续传递到应用程序，确保认证中间件已经执行
        $response = $next($request);

        // 排除一些不需要记录的路径
        if (!$this->shouldSkip($request)) {
            try {
                // 使用操作日志服务记录日志
                $this->operationLogService->logOperation($request);
            } catch (\Exception $e) {
                // 记录日志失败，记录到系统日志
                \Log::error('Failed to log operation: ' . $e->getMessage(), [
                    'exception' => $e,
                    'request' => $request->path(),
                ]);
            }
        }

        return $response;
    }

    /**
     * 判断是否应该跳过记录
     */
    protected function shouldSkip(Request $request)
    {
        // 跳过不需要记录的路径
        $skipPaths = [
            'api/admin/operation-logs', // 跳过操作日志自身的接口
            'api/admin/attachments/download', // 跳过文件下载接口
            '_debugbar', // 跳过调试工具路径
            'api/admin/auth/login', // 跳过登录接口（避免密码泄露）
            'api/admin/auth/logout', // 跳过登出接口
        ];

        foreach ($skipPaths as $path) {
            if (str_starts_with($request->path(), $path)) {
                return true;
            }
        }

        return false;
    }
}
