<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphTo;
use App\Models\BaseModel;

/**
 * @property int $id
 * @property int $attachment_id 附件ID
 * @property int $attachable_id 业务表主键
 * @property string $attachable_type 业务表类型
 * @property string|null $category 附件分类
 * @property int $sort 排序
 * @property string|null $description 附件描述
 * @property int|null $created_at 创建时间
 * @property int|null $updated_at 更新时间
 * @property-read \Illuminate\Database\Eloquent\Model|\Eloquent $attachable
 * @property-read \App\Models\Attachment $attachment
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AttachmentRelation newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AttachmentRelation newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AttachmentRelation query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AttachmentRelation whereAttachableId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AttachmentRelation whereAttachableType($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AttachmentRelation whereAttachmentId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AttachmentRelation whereCategory($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AttachmentRelation whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AttachmentRelation whereDescription($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AttachmentRelation whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AttachmentRelation whereSort($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AttachmentRelation whereUpdatedAt($value)
 * @mixin \Eloquent
 */
class AttachmentRelation extends BaseModel
{
    use HasFactory;

    // 设置时间格式为 Unix 时间戳
    protected $dateFormat = 'U';

    protected $fillable = [
        'attachment_id',
        'attachable_id',
        'attachable_type',
        'category',
        'sort',
        'description',
    ];

    protected $casts = [
        'sort' => 'integer',
        'created_at' => 'integer',
        'updated_at' => 'integer',
    ];

    /**
     * 获取附件
     */
    public function attachment(): BelongsTo
    {
        return $this->belongsTo(Attachment::class);
    }

    /**
     * 获取关联的业务实体
     */
    public function attachable(): MorphTo
    {
        return $this->morphTo();
    }
}
