<template>
  <div class="role-page art-full-height">
    <!-- 搜索栏 -->
    <ArtSearchBar
      v-model:filter="searchFormState"
      :items="searchFormItems"
      @reset="handleReset"
      @search="handleSearch"
    />

    <ElCard class="art-table-card" shadow="never">
      <!-- 表格头部 -->
      <ArtTableHeader v-model:columns="columnChecks" @refresh="refresh">
        <template #left>
          <ElButton v-if="hasAuth('add')" type="primary" :icon="Plus" @click="showDialog('add')"
            >新增角色</ElButton
          >
        </template>
      </ArtTableHeader>

      <!-- 表格 -->
      <ArtTable
        :loading="loading"
        :data="data"
        :columns="columns"
        :pagination="pagination"
        :table-config="{ rowKey: 'id' }"
        :layout="{ marginTop: 10 }"
        @pagination:size-change="handleSizeChange"
        @pagination:current-change="handleCurrentChange"
      >
      </ArtTable>

      <!-- 角色弹窗 -->
      <RoleDialog
        v-model:visible="dialogVisible"
        :type="dialogType"
        :data="currentRoleData"
        @submit="handleDialogSubmit"
      />

      <!-- 权限分配弹窗 -->
      <RolePermissionDialog
        v-model:visible="permissionDialogVisible"
        :role-data="currentPermissionRole"
        @submit="handlePermissionSubmit"
      />
    </ElCard>
  </div>
</template>

<script setup lang="ts">
  import { ref, h, nextTick } from 'vue'
  import { ElMessageBox, ElMessage, ElButton, ElTag } from 'element-plus'
  import { Plus } from '@element-plus/icons-vue'
  import { useTable } from '@/composables/useTable'
  import { useAuth } from '@/composables/useAuth'
  import { handleSuccess } from '@/utils/errorHandler'
  import { formatDate } from '@/utils/dataprocess/format'
  import ArtButtonMore from '@/components/core/forms/art-button-more/index.vue'
  import type { ButtonMoreItem } from '@/components/core/forms/art-button-more/index.vue'
  import { getRoleList, deleteRole as deleteRoleApi } from '@/api/admin/roleApi'
  import RoleDialog from './components/RoleDialog.vue'
  import RolePermissionDialog from './components/RolePermissionDialog.vue'
  import type { RoleListItem } from '@/types/api/role'
  import type { SearchFormItem } from '@/types'

  defineOptions({ name: 'RoleIndex' })

  // 权限控制
  const { hasAuth } = useAuth()

  // 弹窗相关
  const dialogType = ref<Form.DialogType>('add')
  const dialogVisible = ref(false)
  const currentRoleData = ref<RoleListItem | null>(null)

  // 权限弹窗相关
  const permissionDialogVisible = ref(false)
  const currentPermissionRole = ref<RoleListItem | null>(null)

  // 搜索表单相关
  const searchFormState = ref({
    search: ''
  })

  // 搜索表单配置项
  const searchFormItems: SearchFormItem[] = [
    {
      label: '角色名称',
      prop: 'search',
      type: 'input',
      config: {
        clearable: true,
        placeholder: '请输入角色名称'
      }
    }
  ]

  const {
    columns,
    columnChecks,
    tableData: data,
    isLoading: loading,
    paginationState: pagination,
    searchState: searchParams, // eslint-disable-line @typescript-eslint/no-unused-vars
    searchData: getDataByPage,
    resetSearch: resetSearchParams,
    onPageSizeChange: handleSizeChange,
    onCurrentPageChange: handleCurrentChange,
    refreshAll: refresh,
    refreshAfterCreate: refreshAfterAdd,
    refreshAfterUpdate: refreshAfterEdit,
    refreshAfterRemove: refreshAfterDelete
  } = useTable<RoleListItem>({
    // 核心配置
    core: {
      apiFn: (params: any) => {
        // 转换参数格式
        const apiParams = {
          page: params.current,
          per_page: params.size,
          search: searchFormState.value.search || ''
        }
        return getRoleList(apiParams)
      },
      apiParams: {
        current: 1,
        size: 20,
        total: 0
      },
      columnsFactory: () => [
        { type: 'index', width: 60, label: '序号' },
        {
          prop: 'name',
          label: '角色名称',
          minWidth: 150
        },
        {
          prop: 'description',
          label: '角色描述',
          minWidth: 200,
          formatter: (row) => row.description || '-'
        },
        {
          prop: 'users_count',
          label: '用户数量',
          width: 100,
          formatter: (row) => {
            return h(ElTag, { type: 'info' }, () => row.users_count || 0)
          }
        },
        {
          prop: 'created_at',
          label: '创建时间',
          minWidth: 160,
          sortable: true,
          formatter: (row) =>
            row.created_at ? formatDate(row.created_at, 'YYYY-MM-DD HH:mm:ss') : ''
        },
        {
          prop: 'operation',
          label: '操作',
          width: 100,
          fixed: 'right',
          formatter: (row) =>
            h(ArtButtonMore, {
              list: getOperationButtons(row),
              onClick: (item: ButtonMoreItem) => handleButtonMoreClick(item, row)
            })
        }
      ]
    },
    // 数据处理
    transform: {
      // 使用响应适配器来处理后端返回的数据格式
      responseAdapter: (response: any) => {
        // 如果响应包含 data 和 meta，说明是标准格式
        if (response.data && response.meta) {
          return {
            data: response.data,
            total: response.meta.total,
            pageSize: response.meta.per_page,
            currentPage: response.meta.current_page
          }
        }
        // 否则使用默认格式
        return {
          data: response.data || [],
          total: response.total || 0,
          pageSize: response.per_page || 20,
          currentPage: response.page || 1
        }
      }
    },
    // 性能优化
    performance: {
      enableCache: true,
      cacheTime: 10 * 60 * 1000
    },
    // 生命周期钩子
    hooks: {
      onError: (error) => ElMessage.error(error.message)
    },
    // 调试配置
    debug: {
      enableLog: true
    }
  })

  /**
   * 动态生成操作按钮
   */
  const getOperationButtons = (row: RoleListItem): ButtonMoreItem[] => {
    const buttons: ButtonMoreItem[] = []

    if (hasAuth('edit')) {
      buttons.push({ key: 'edit', label: '编辑' })
    }

    if (hasAuth('assign_permissions')) {
      buttons.push({ key: 'permission', label: '菜单权限' })
    }

    if (hasAuth('delete')) {
      buttons.push({ key: 'delete', label: '删除' })
    }

    return buttons
  }

  /**
   * 处理搜索
   */
  const handleSearch = () => {
    getDataByPage()
  }

  /**
   * 处理重置
   */
  const handleReset = () => {
    searchFormState.value = {
      search: ''
    }
    resetSearchParams()
  }

  /**
   * 处理更多按钮点击
   */
  const handleButtonMoreClick = (item: ButtonMoreItem, row: RoleListItem): void => {
    switch (item.key) {
      case 'edit':
        showDialog('edit', row)
        break
      case 'permission':
        showPermissionDialog(row)
        break
      case 'delete':
        deleteRole(row)
        break
    }
  }

  /**
   * 显示角色弹窗
   */
  const showDialog = (type: Form.DialogType, row?: RoleListItem): void => {
    dialogType.value = type
    currentRoleData.value = row || null
    nextTick(() => {
      dialogVisible.value = true
    })
  }

  /**
   * 删除角色
   */
  const deleteRole = async (row: RoleListItem): Promise<void> => {
    try {
      await ElMessageBox.confirm(`确定要删除角色"${row.name}"吗？`, '删除确认', {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'warning'
      })

      await deleteRoleApi(row.id)
      handleSuccess('删除成功')
      refreshAfterDelete()
    } catch {
      // 用户取消删除，不需要处理
    }
  }

  /**
   * 显示权限分配弹窗
   */
  const showPermissionDialog = (row: RoleListItem): void => {
    currentPermissionRole.value = row
    nextTick(() => {
      permissionDialogVisible.value = true
    })
  }

  /**
   * 处理权限分配提交
   */
  const handlePermissionSubmit = () => {
    // 权限分配成功后刷新列表
    refresh()
  }

  /**
   * 处理弹窗提交事件
   */
  const handleDialogSubmit = async () => {
    dialogVisible.value = false
    await (dialogType.value === 'add' ? refreshAfterAdd() : refreshAfterEdit())
    currentRoleData.value = null
  }
</script>

<style lang="scss" scoped>
  .role-page {
    display: flex;
    flex-direction: column;
  }
</style>
